"""
Bybit 合约 (V5 Public WebSocket) 最优买卖价订阅示例
---------------------------------------------------
目标: 订阅 USDT 永续 (category=linear) 合约 (BTCUSDT, ETHUSDT 等) 的最优挂单
频道: tickers
Endpoint: wss://stream.bybit.com/v5/public/linear

订阅请求:
  {
    "op": "subscribe",
    "args": ["tickers.BTCUSDT", "tickers.ETHUSDT"]
  }

返回数据示例 (精简):
  {
    "topic": "tickers.BTCUSDT",
    "type": "snapshot",
    "ts": 1696400000123,
    "data": {
        "symbol": "BTCUSDT",
        "bid1Price": "27123.5",
        "bid1Size": "0.543",
        "ask1Price": "27123.6",
        "ask1Size": "0.412",
        "lastPrice": "27123.55",
        "openInterest": "123456",
        "turnover24h": "1234567.89",
        "timestamp": "1696400000123",
        ...
    }
  }

心跳:
  - 客户端需定期发送 {"op":"ping"} 维持连接
  - 服务端响应 {"op":"pong"} 或含 "success":true 的 pong 结构
  - 若长时间未收到任何消息，建议主动重连

功能特性:
  - 多 symbol 订阅
  - 心跳 + 超时检测
  - 自动重连 (指数退避，带抖动)
  - 延迟估计 (latency_ms)

可扩展:
  - 支持 inverse / option / spot：修改 WebSocket URL
  - 写入 Kafka/Redis/DB
  - 增加指标监控 (Prometheus)

Python: 3.10+
"""

import asyncio
import json
import logging
import signal
import time
from dataclasses import dataclass
from typing import List, Optional, Callable, Awaitable

import websockets
from websockets import WebSocketClientProtocol

# ---------------- 基础配置 ---------------- #

# USDT 永续 / 线性合约
BYBIT_LINEAR_WS = "wss://stream.bybit.com/v5/public/linear"
# 其它可选:
#   反向合约 (inverse): wss://stream.bybit.com/v5/public/inverse
#   期权 (option):      wss://stream.bybit.com/v5/public/option
#   现货 (spot):        wss://stream.bybit.com/v5/public/spot
#
# 若需要测试网，可替换为:
#   wss://stream-testnet.bybit.com/v5/public/linear  (并用测试网账户/市场)
#
HEARTBEAT_INTERVAL = 15         # 发送 ping 间隔 (秒)
MESSAGE_TIMEOUT = 45            # 超过该秒数未收到任意消息则触发重连
RECONNECT_BASE_DELAY = 2        # 初始重连等待
RECONNECT_MAX_DELAY = 60        # 最大重连等待


# ---------------- 数据模型 ---------------- #

@dataclass
class BestQuote:
    symbol: str
    bid_price: float
    bid_qty: float
    ask_price: float
    ask_qty: float
    ts: int           # 服务端时间戳 (ms)
    latency_ms: float # 本地简单延迟估计 (ms)


# ---------------- 客户端实现 ---------------- #

class BybitTickersClient:
    """
    Bybit tickers 频道客户端 (仅关注最优买卖价)。
    """

    def __init__(
        self,
        symbols: List[str],
        category_ws_url: str = BYBIT_LINEAR_WS,
        on_quote: Optional[Callable[[BestQuote], Awaitable[None]]] = None,
    ):
        """
        :param symbols: e.g. ["BTCUSDT", "ETHUSDT"]
        :param category_ws_url: 对应 category 的公共 WS 地址
        :param on_quote: 异步回调
        """
        self.symbols = [s.upper() for s in symbols]
        self.ws_url = category_ws_url
        self.on_quote = on_quote

        self._ws: Optional[WebSocketClientProtocol] = None
        self._stop_evt = asyncio.Event()
        self._last_msg_time: float = 0.0
        self._reconnect_attempt: int = 0
        self._tasks: list[asyncio.Task] = []

    # ---------- 对外接口 ---------- #
    async def start(self):
        logging.info("启动 BybitTickersClient 订阅: %s", self.symbols)
        while not self._stop_evt.is_set():
            try:
                await self._connect_and_run()
            except asyncio.CancelledError:
                raise
            except Exception as e:
                logging.exception("主循环异常: %s", e)

            if self._stop_evt.is_set():
                break

            # 指数退避
            self._reconnect_attempt += 1
            delay = min(RECONNECT_BASE_DELAY * (2 ** (self._reconnect_attempt - 1)), RECONNECT_MAX_DELAY)
            jitter = delay * 0.2
            wait_time = delay + (jitter * (0.5 - (time.time() % 1)))
            logging.warning("将在 %.2fs 后重连 (尝试 #%d)", wait_time, self._reconnect_attempt)
            try:
                await asyncio.wait_for(self._stop_evt.wait(), timeout=wait_time)
            except asyncio.TimeoutError:
                pass

        logging.info("客户端已停止。")

    async def stop(self):
        logging.info("停止中 ...")
        self._stop_evt.set()
        await self._close_ws()
        for t in self._tasks:
            if not t.done():
                t.cancel()
        await asyncio.gather(*self._tasks, return_exceptions=True)

    # ---------- 内部流程 ---------- #
    async def _connect_and_run(self):
        logging.info("连接 %s", self.ws_url)
        async with websockets.connect(
            self.ws_url,
            ping_interval=None,  # 手动心跳
            ping_timeout=None,
            max_queue=1024,
            close_timeout=5,
        ) as ws:
            self._ws = ws
            self._reconnect_attempt = 0
            self._last_msg_time = time.time()

            await self._subscribe()

            recv_task = asyncio.create_task(self._receiver_loop(), name="receiver")
            heartbeat_task = asyncio.create_task(self._heartbeat_loop(), name="heartbeat")
            watchdog_task = asyncio.create_task(self._watchdog_loop(), name="watchdog")

            self._tasks = [recv_task, heartbeat_task, watchdog_task]

            done, pending = await asyncio.wait(self._tasks, return_when=asyncio.FIRST_EXCEPTION)
            for d in done:
                if exc := d.exception():
                    raise exc

        self._ws = None

    async def _close_ws(self):
        if self._ws:
            try:
                await self._ws.close()
            except Exception:
                pass

    async def _send_json(self, obj: dict):
        if not self._ws:
            return
        txt = json.dumps(obj, separators=(",", ":"))
        await self._ws.send(txt)
        logging.debug("发送: %s", txt)

    async def _subscribe(self):
        # Bybit V5 tickers 订阅: args = ["tickers.SYMBOL", ...]
        args = [f"tickers.{sym}" for sym in self.symbols]
        req = {"op": "subscribe", "args": args}
        await self._send_json(req)
        logging.info("已发送订阅: %s", args)

    # ---------- 心跳 & 看门狗 ---------- #
    async def _heartbeat_loop(self):
        while not self._stop_evt.is_set():
            await asyncio.sleep(HEARTBEAT_INTERVAL)
            if not self._ws:
                continue
            try:
                # 发送 {"op":"ping"} (也可直接发送 ws ping frame，这里使用协议形式)
                await self._send_json({"op": "ping"})
                logging.debug("发送 ping")
            except Exception as e:
                logging.warning("心跳发送失败: %s", e)
                break

    async def _watchdog_loop(self):
        while not self._stop_evt.is_set():
            await asyncio.sleep(5)
            idle = time.time() - self._last_msg_time
            if idle > MESSAGE_TIMEOUT:
                logging.warning("超过 %.1fs 未收到消息(阈值=%ss)，主动断开以重连", idle, MESSAGE_TIMEOUT)
                await self._close_ws()
                break

    # ---------- 接收处理 ---------- #
    async def _receiver_loop(self):
        assert self._ws is not None
        ws = self._ws
        async for raw in ws:
            self._last_msg_time = time.time()

            # 尝试解析 JSON
            try:
                msg = json.loads(raw)
            except json.JSONDecodeError:
                logging.debug("非 JSON 消息: %s", raw)
                continue

            # pong / 心跳响应
            if msg.get("op") == "pong":
                logging.debug("收到 pong")
                continue

            # 订阅响应
            if msg.get("op") == "subscribe":
                if msg.get("success"):
                    logging.info("订阅成功: %s", msg.get("args"))
                else:
                    logging.error("订阅失败: %s", msg)
                continue

            # 错误返回
            if msg.get("ret_code") not in (None, 0):
                # Bybit 常见结构: {"ret_code":0,"ret_msg":"OK","op":"subscribe","args":[...]}
                if msg.get("ret_code") != 0:
                    logging.error("返回错误: %s", msg)
                # 仍继续
            
            # 数据消息: 期望含 topic & data
            topic = msg.get("topic")
            if topic and topic.startswith("tickers."):
                d = msg.get("data") or {}
                ts = int(msg.get("ts") or 0)
                now_ms = int(time.time() * 1000)
                try:
                    # 可能存在 d["timestamp"] 或上层 msg["ts"]
                    latency = now_ms - ts if ts > 0 else -1
                    quote = BestQuote(
                        symbol=d.get("symbol", topic.split(".", 1)[-1]),
                        bid_price=float(d.get("bid1Price") or 0.0),
                        bid_qty=float(d.get("bid1Size") or 0.0),
                        ask_price=float(d.get("ask1Price") or 0.0),
                        ask_qty=float(d.get("ask1Size") or 0.0),
                        ts=ts,
                        latency_ms=latency
                    )
                    if self.on_quote:
                        await self.on_quote(quote)
                except Exception as e:
                    logging.exception("处理数据异常: %s | raw=%s", e, d)
                continue

            # 其它类型日志调试
            if topic:
                logging.debug("忽略 topic=%s msg=%s", topic, msg)
            else:
                logging.debug("未识别结构: %s", msg)


# ---------------- 使用示例 ---------------- #

async def example_on_quote(q: BestQuote):
    print(
        f"[{time.strftime('%X')}] {q.symbol} "
        f"Bid {q.bid_price:.2f} ({q.bid_qty:.4f}) | "
        f"Ask {q.ask_price:.2f} ({q.ask_qty:.4f}) | "
        f"ts={q.ts} latency={q.latency_ms}ms"
    )

async def main():
    symbols = ["OPUSDT","LDOUSDT","NEARUSDT","CRVUSDT","ZETAUSDT","LRCUSDT","ARBUSDT","LINKUSDT","BCHUSDT","MASKUSDT","ALGOUSDT","YFIUSDT","XLMUSDT","CROUSDT","ETHWUSDT","ETHUSDT","ETCUSDT","USDCUSDT","THETAUSDT","CHZUSDT","AXSUSDT","SANDUSDT","ADAUSDT","FLOWUSDT","AI16ZUSDT","INJUSDT","SUSHIUSDT","1INCHUSDT","MAJORUSDT","MYRIAUSDT","CFXUSDT","ZILUSDT","GMTUSDT","HMSTRUSDT","ORBSUSDT","ATOMUSDT","SOLUSDT","DOGSUSDT","ETHFIUSDT","SAHARAUSDT","KSMUSDT","AVAXUSDT","IDUSDT","BOMEUSDT","MINAUSDT","AAVEUSDT","LPTUSDT","IOTAUSDT","NEOUSDT","ACTUSDT","TONUSDT","TRXUSDT","CELOUSDT","XTZUSDT","YGGUSDT","COMPUSDT","BTCUSDT","BNBUSDT","NOTUSDT","HBARUSDT","SHELLUSDT","1000000MOGUSDT","DOGEUSDT","CETUSUSDT","XRPUSDT","QTUMUSDT","BANDUSDT","ICPUSDT","KAITOUSDT","CATIUSDT","IMXUSDT","SUIUSDT","LTCUSDT","BRETTUSDT","BNTUSDT","NILUSDT","SPKUSDT","FILUSDT","HUMAUSDT","LQTYUSDT","JUPUSDT","PARTIUSDT","AUCTIONUSDT","MAGICUSDT","DOTUSDT"]
    client = BybitTickersClient(symbols, on_quote=example_on_quote)

    loop = asyncio.get_running_loop()
    stop_evt = asyncio.Event()

    def _stop():
        if not stop_evt.is_set():
            stop_evt.set()
            asyncio.create_task(client.stop())

    for sig in (signal.SIGINT, signal.SIGTERM):
        try:
            loop.add_signal_handler(sig, _stop)
        except NotImplementedError:
            pass

    runner = asyncio.create_task(client.start())
    await stop_evt.wait()
    await runner

if __name__ == "__main__":
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s | %(levelname)-8s | %(message)s",
        datefmt="%H:%M:%S",
    )
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("用户中断")