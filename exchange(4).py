from dataclasses import dataclass
import time
from decimal import Decimal
import math
from typing import Dict, <PERSON><PERSON>
from pybit.unified_trading import HTTP
from pybit.exceptions import FailedRequestError, InvalidRequestError
from pybit.unified_trading import WebSocketTrading
from time import sleep
import re
# Bitget v1 API (保留兼容性)
try:
    from pybitget import Client
    from pybitget import logger
    from pybitget.enums import NEW_BUY, NEW_SELL
    from pybitget.utils import random_string
    from pybitget.exceptions import BitgetAPIException,BitgetParamsException,BitgetRequestException
    PYBITGET_AVAILABLE = True
    print("✅ pybitget (v1) 导入成功")
except ImportError as e:
    PYBITGET_AVAILABLE = False
    print(f"⚠️ pybitget (v1) 导入失败: {e}")
    # 定义占位符类以避免NameError
    class BitgetAPIException(Exception): pass
    class BitgetParamsException(Exception): pass
    class BitgetRequestException(Exception): pass

# Bitget v2 API (新版本)
try:
    import bitget.v2.mix.order_api as bitgetOrderApi
    import bitget.v2.mix.account_api as bitgetAccountApi
    import bitget.v2.mix.market_api as bitgetMarketApi
    import bitget.bitget_api as bitgetBaseApi
    BITGET_V2_AVAILABLE = True
    print("Bitget v2 API 导入成功")
except ImportError as e:
    BITGET_V2_AVAILABLE = False
    print(f"Bitget v2 API 导入失败: {e}")
except Exception as e:
    BITGET_V2_AVAILABLE = False
    print(f"Bitget v2 API 导入异常: {e}")
import uuid

# OKX API
try:
    from okx import OkxRestClient
    from okx.exceptions import OkxApiException,OkxParamsException,OkxRequestException
    OKX_AVAILABLE = True
    print("✅ OKX API 导入成功")
except ImportError as e:
    OKX_AVAILABLE = False
    print(f"⚠️ OKX API 导入失败: {e}")
    # 定义占位符类
    class OkxApiException(Exception): pass
    class OkxParamsException(Exception): pass
    class OkxRequestException(Exception): pass

# from bitget import BitgetSync
from abc import ABC,abstractmethod

# Binance API
try:
    from binance import Client as BinanceClient
    from binance.exceptions import BinanceAPIException, BinanceRequestException, BinanceOrderException
    BINANCE_AVAILABLE = True
    print("✅ Binance API 导入成功")
except ImportError as e:
    BINANCE_AVAILABLE = False
    print(f"⚠️ Binance API 导入失败: {e}")
    # 定义占位符类
    class BinanceAPIException(Exception): pass
    class BinanceRequestException(Exception): pass
    class BinanceOrderException(Exception): pass
import logging

output_logger = logging.getLogger('output_logger')


class BaseExchangeHTTP(ABC):
    def __init__(self, exchange_name,api_key,api_secret,passphrase,isTest=False):
        self.exchange_name = exchange_name
        self.session = None
        self.api_key = api_key
        self.passphrase = passphrase
        self.api_secret = api_secret
        self.isTest = isTest
        
    @abstractmethod
    def connect(self) -> bool:
        pass

    @abstractmethod
    def place_order(self):
        """return True/False, response"""
        pass
    
    @abstractmethod
    def log_error(self):
        pass

    @abstractmethod
    def get_balance_info(self):
        """return total equity, total balance(in usdt) float"""
        pass

    @abstractmethod
    def get_order_detail(self):
        """返还字典，键 有
        symbol 交易对名称（统一）
        avg_price, 成交平均价格 float
        exec_qty,  成交数量 float
        exec_fee,  成交手续费   float
        created_time, 交易所系统创建订单时间 int
        our_time， 我们本地记录的创建时间   int
        """
        pass

    @abstractmethod
    def error_handler(self):
        pass

    @abstractmethod
    def get_symbol_info(self):
        """ return lot_size(float), max_quantity"""
        pass

    @abstractmethod
    def get_orderbook(self):
        """return orderbook asks, bids"""
        pass
    def lcm(self,a, b):
        """
        计算两个浮点数 a 和 b 的最小公倍数。
        
        参数:
            a (float): 第一个浮点数
            b (float): 第二个浮点数
            
        返回:
            float: a 和 b 的最小公倍数
        """
        # 1. 将浮点数转换为字符串，以精确确定小数位数
        dec_a = Decimal(str(a))
        dec_b = Decimal(str(b))
        
        # 2. 获取各自的小数位数
        scale_a = -dec_a.as_tuple().exponent
        scale_b = -dec_b.as_tuple().exponent
        
        # 3. 找出最大的小数位数
        max_scale = max(scale_a, scale_b)
        
        # 4. 计算乘数 (10 ^ max_scale)
        multiplier = 10 ** max_scale
        
        # 5. 将原数乘以 multiplier 得到整数
        int_a = int(dec_a * multiplier)
        int_b = int(dec_b * multiplier)
        
        # 6. 计算两个整数的最大公约数
        gcd_value = math.gcd(int_a, int_b)
        
        # 7. 计算两个整数的最小公倍数
        # LCM(a, b) = |a * b| / GCD(a, b)
        lcm_value = abs(int_a * int_b) // gcd_value
        
        # 8. 将整数的最小公倍数转换回浮点数
        result = lcm_value / multiplier
        
        return result
    
    def generate_orderid(self,symbol:str):
        """通过交易对生成orderid"""
        return symbol + "SPLIT" + str(int(time.time()))
    
    def strip_orderid(self,orderid:str):
        """从orderid中提取交易对和时间戳"""
        return orderid.split('SPLIT')
    
class BybitExchangeHTTP(BaseExchangeHTTP):
    def __init__(self,exchange_name,api_key,api_secret,isTest=False):
        super().__init__(exchange_name, api_key, api_secret, None,isTest)
        self.exchange_name = 'Bybit'


    def log_error(self,e):
        output_logger.info(f'错误代码: {e.status_code}, 错误信息: {e.message}')

    def connect(self) -> bool:
        try:
            bybit_session = HTTP(
                # 永远是False
            testnet=False,
            demo=self.isTest,
            api_key=self.api_key,
            api_secret=self.api_secret,
        )
            self.session = bybit_session
            return True
        
        except Exception as e:
            self.error_handler(e)
    
    def get_symbol_info(self, symbol) -> float:
        lot_size = None
        try:
            inst_info = self.session.get_instruments_info(category='linear',symbol=symbol)
            if inst_info['retCode'] == 0:
                if len(inst_info['result']['list']) >= 1:
                    lot_size_filter = inst_info['result']['list'][0]['lotSizeFilter']
                    lot_size = lot_size_filter['minOrderQty']
                    max_order_quantity = lot_size_filter['maxOrderQty']
                if len(inst_info['result']['list']) > 1:
                    output_logger.info(f'警告: 获取最小交易量时返还了多个交易对，返回值可能有误')
                return float(lot_size),float(max_order_quantity)
        except Exception as e:
            self.error_handler(e)
            return None, None
        
    def get_orderbook(self,symbol):
        try:
            ret = self.session.get_orderbook(category='linear',symbol=symbol)
            if ret['retCode'] == 0:
                asks = ret['result']['a']
                bids = ret['result']['b']
                return asks, bids
            else:
                output_logger.info(f'Bybit get_orderbook retCode: {ret["retCode"]}, retMsg: {ret["retMsg"]}')
        except Exception as e:
            self.error_handler(e)

    def place_order(self,category,symbol,side,orderType,qty):
        """note, qty is a string
        now only limited to orderType = Market because price is not in args
        orderType Limit / Market
        side Buy / Sell
        https://bybit-exchange.github.io/docs/v5/order/create-order#response-parameters
        
        res["orderId"] = symbol_timestamp"
        """
        try:
            orderid = self.generate_orderid(symbol)
            f_orderType = orderType[0].upper() + orderType[1:]
            f_side = side[0].upper() + side[1:]
            response = self.session.place_order(
            category=category,
            symbol=symbol,
            side=f_side,
            orderType=f_orderType,
            qty=qty,
            orderLinkId=orderid)

            response['orderid'] = orderid

            print(f'bybit place_order response: {response}')
            return True,response

        except Exception as e:
            self.error_handler(e)
            return False, None
        
    def get_order_detail(self, orderid:str):
        """order id is self generated cutomized order id
            created time 是交易所系统中生成订单的时间
            our_time 是我们使用交易所api下单前记录的时间
                qty是下单量，cumExecQty是累积已经成交量"""
        try:
            result = self.session.get_open_orders(category='linear', orderLinkId=orderid)
            print(f'bybit get_order_detail result: {result}')
            order_detail = result['result']['list'][0]
            _, timestamp = self.strip_orderid(orderid)

            detail = {"symbol": order_detail['symbol'],
                    "avg_price": float(order_detail['avgPrice']),
                    "exec_qty": float(order_detail['cumExecQty']),
                    "exec_fee": abs(float(order_detail['cumExecFee'])),
                    "created_time": int(order_detail['createdTime']),
                    "our_time":int(timestamp),
                    "exchange":self.exchange_name,
                    "orderid":orderid,
                    }
            return detail
            
        except Exception as e:
            self.error_handler(e)

    def get_balance_info(self) :
        try:
            res = self.session.get_wallet_balance(accountType="UNIFIED")
            if res['retCode'] == 0:
                balance_info = res['result']['list'][0]
                total_equity = balance_info['totalEquity']

                # 调试：打印实际的数据结构
                output_logger.info(f"Bybit balance_info structure: {balance_info}")

                # 查找USDT币种的余额
                usdt_balance = None
                if 'coin' in balance_info and balance_info['coin']:
                    for coin in balance_info['coin']:
                        if coin['coin'] == 'USDT':
                            usdt_balance = coin.get('walletBalance') or coin.get('availableBalance')
                            break

                    # 如果没找到USDT，使用第一个币种的余额
                    if usdt_balance is None and len(balance_info['coin']) > 0:
                        first_coin = balance_info['coin'][0]
                        usdt_balance = first_coin.get('walletBalance') or first_coin.get('availableBalance')

                # 如果还是没找到，尝试直接从账户信息获取
                if usdt_balance is None:
                    usdt_balance = balance_info.get('totalAvailableBalance') or balance_info.get('totalWalletBalance')

                # 如果账户余额为0或空，这是正常情况
                if usdt_balance is not None or total_equity == '0':
                    usdt_balance = usdt_balance or '0'  # 如果为None，设为'0'
                    return float(total_equity), float(usdt_balance)
                else:
                    output_logger.info(f"Bybit: 未找到可用余额信息。数据结构: {balance_info}")
                    return None, None
            else:
                output_logger.info(f"Bybit get_balance_info retCode: {res['retCode']}, retMsg: {res.get('retMsg', 'Unknown error')}")
                return None, None

        except Exception as e:
            self.error_handler(e)
            return None,None

    def get_symbol_info(self, symbol):
        """获取交易对信息，返回最小交易量、最大交易量和合约面值"""
        try:
            # 获取合约信息
            result = self.session.get_instruments_info(category="linear", symbol=symbol)
            if result['retCode'] == 0 and result['result']['list']:
                instrument = result['result']['list'][0]
                lot_size_filter = instrument.get('lotSizeFilter', {})
                min_order_qty = float(lot_size_filter.get('minOrderQty', '0'))
                max_order_qty = float(lot_size_filter.get('maxOrderQty', '999999'))
                # Bybit直接使用币的数量，合约面值为1
                return min_order_qty, max_order_qty, 1.0
            return None, None, None
        except Exception as e:
            self.error_handler(e)
            return None, None, None

    def get_order_details(self, order_id, symbol):
        """查询订单详情"""
        try:
            response = self.session.get_open_orders(category="linear", symbol=symbol, orderId=order_id)
            if response.get('retCode') == 0 and response.get('result', {}).get('list'):
                return response['result']['list'][0]
            return None
        except Exception as e:
            self.error_handler(e)
            return None

    def error_handler(self, e):
        if isinstance(e, (InvalidRequestError,FailedRequestError)):
            self.log_error(e)
        else:
            output_logger.info(f"Bybit error: {e}")

class Bitgetv1HTTP(BaseExchangeHTTP):
    def __init__(self, exchange_name,api_key,api_secret,passphrase,isTest=False):
        self.exchange_name = 'Bitget'
        self.session = None
        self.api_key = api_key
        self.passphrase = passphrase
        self.api_secret = api_secret
        self.isTest = isTest
        
    def connect(self):
        """return True if successfully connected"""
        try:
            if not PYBITGET_AVAILABLE:
                output_logger.error("pybitget 库不可用，无法使用 Bitget v1 API")
                return False

            client = Client(self.api_key, self.api_secret, passphrase=self.passphrase,isTest=self.isTest)
            self.session = client
            return True
        except Exception as e:
            # logger.debug(e)
            self.error_handler(e)
            return False

    def _convert_symbol(self,symbol:str) -> str:
        return symbol + '_UMCBL'

    def place_order(self,category,symbol,side,orderType,qty):
        """category linear/ .../ should not matter because in v1 it is included in symbol name
        orderType = market/ limit, orderType == limit -> price is mandatory
        side = NEW_BUY NEW_SELL BUY_CLOSE SELL_CLOSE, check enum
        we fix marginCoin = USDT for our own purpose
        raise exceptions.BitgetAPIException(response)
        pybitget.exceptions.BitgetAPIException: API Request Error(code=40762):
        The order amount exceeds the balance

        真实下单
        """

        # symbol = "BTCUSDT_UMCBL"
        try:
            category = "linear"
            marginCoin = "USDT"
            symbol = self._convert_symbol(symbol)
            size = qty

            # 生成自定义订单ID
            orderid = self.generate_orderid(symbol.replace('_UMCBL', ''))

            if side == 'buy':
                f_side = NEW_BUY
            elif side == 'sell':
                f_side = NEW_SELL
            else:
                #TODO
                # not supported yet
                # BUY_CLOSE and SELL_CLOSE, is it necessary
                raise ValueError
            s = self.session.mix_place_order(symbol, marginCoin, size, f_side, orderType, clientOrderId=orderid)
            # logger.debug(s)

            # 添加自定义订单ID到响应中
            if s:
                s['orderid'] = orderid

            return True, s
            #TODO
            # 失败后的处理，失败原因

            # s = client.mix_place_order(symbol, marginCoin, size, side, orderType, price=price, clientOrderId=random_string("cntest"))
            # if orderType = limit, put in price
        except Exception as e:
            # logger.debug(e)
            self.error_handler(e)
            return False, None
        
    def log_error(self,e):
        output_logger.info(f'Bitget status code: {e.status_code}. Error Message: {e.message}')

    def get_balance_info(self):
        """返回值 总资产?, 可用资产"""
        try:
            result = self.session.mix_get_accounts(productType='UMCBL')
            return result['data'][0]['equity'],result['data'][0]['available']
        except Exception as e:
            # logger.debug(e)
            self.error_handler(e)
            return None,None

    def get_symbol_info(self, symbol):
        """获取交易对信息，返回最小交易量、最大交易量和合约面值"""
        try:
            symbol = self._convert_symbol(symbol)
            # 使用正确的API端点获取合约信息
            result = self.session.mix_get_contracts(productType='UMCBL')
            print(f'{result}')

            if result['code'] == '00000' and result['data']:
                # 查找指定的交易对
                for contract in result['data']:
                    if contract['symbol'] == symbol:
                        min_trade_num = contract.get('minTradeNum')  # 最小交易量
                        max_trade_num = contract.get('maxTradeNum')  # 最大交易量
                        if min_trade_num and max_trade_num:
                            # Bitget直接使用币的数量，合约面值为1
                            return float(min_trade_num), float(max_trade_num), 1.0
                        break
            return None, None, None
        except Exception as e:
            self.error_handler(e)
            return None, None, None

    def get_orderbook(self, symbol):
        """获取订单簿数据"""
        try:
            symbol = self._convert_symbol(symbol)
            result = self.session.mix_get_depth(symbol=symbol, limit=15)
            if result['code'] == '00000' and result['data']:
                asks = result['data']['asks']  # 卖单 [[price, size], ...]
                bids = result['data']['bids']  # 买单 [[price, size], ...]
                return asks, bids
            return None, None
        except Exception as e:
            self.error_handler(e)
            return None, None

    def get_order_detail(self, orderid: str):
        """获取订单详情 - Bitget v1"""
        try:
            symbol, timestamp = self.strip_orderid(orderid)
            symbol_converted = self._convert_symbol(symbol)

            # 使用 Bitget v1 API 查询订单详情
            response = self.session.mix_get_order_detail(symbol_converted, orderid)

            if response and response.get('code') == '00000' and response.get('data'):
                order_data = response['data']
                detail = {
                    "symbol": symbol,
                    "avg_price": float(order_data.get('priceAvg', '0')),
                    "exec_qty": float(order_data.get('filledQty', '0')),
                    "exec_fee": abs(float(order_data.get('fee', '0'))),
                    "created_time": int(order_data.get('cTime', '0')),
                    "our_time": int(timestamp),
                    "exchange": self.exchange_name,
                    "orderid": orderid,
                }
                return detail
            else:
                output_logger.info(f'Bitget v1 get_order_detail failed, orderid: {orderid}, response: {response}')
                return None

        except Exception as e:
            self.error_handler(e)
            return None

    def error_handler(self, e):
        if isinstance(e,(BitgetAPIException,BitgetParamsException,BitgetRequestException)):
            self.log_error(e)
        else:
            output_logger.info(e)

class BitgetV2HTTP(BaseExchangeHTTP):
    """Bitget v2 API 实现"""

    def __init__(self, exchange_name, api_key, api_secret, passphrase, isTest=False):
        super().__init__(exchange_name, api_key, api_secret, passphrase, isTest)
        self.exchange_name = 'Bitget'
        self.base_api = None

    def connect(self):
        try:
            if not BITGET_V2_AVAILABLE:
                output_logger.error("Bitget v2 API 不可用，请安装正确的 bitget 库")
                return False

            # 初始化基础API客户端
            self.base_api = bitgetBaseApi.BitgetApi(self.api_key, self.api_secret, self.passphrase)

            # 测试连接
            test_response = self.base_api.get("/api/v2/mix/market/contracts", {"productType": "USDT-FUTURES"})
            if test_response.get('code') == '00000':
                output_logger.info("Bitget v2 连接成功")
                return True
            else:
                output_logger.error(f"Bitget v2 连接测试失败: {test_response}")
                return False

        except Exception as e:
            self.error_handler(e)
            return False

    def place_order(self, category, symbol, side, orderType, qty, action=None):
        try:
            symbol = self._convert_symbol(symbol)

            # 生成自定义订单ID
            orderid = self.generate_orderid(symbol.replace('USDT', ''))

            # 构建v2 API参数
            params = {
                "symbol": symbol,
                "productType": "USDT-FUTURES",
                "marginMode": "crossed",
                "marginCoin": "USDT",
                "size": str(qty),
                "side": side,
                # "tradeSide": action,
                "orderType": orderType,
                "force": "gtc",  # Good Till Cancel
                "clientOid": orderid  # 添加自定义订单ID
            }
            print(f'Bitget v2 下单参数: {params}')
            # 使用v2 API下单
            response = self.base_api.post("/api/v2/mix/order/place-order", params)

            if response.get('code') == '00000':
                # 添加自定义订单ID到响应中
                if response:
                    response['orderid'] = orderid
                return True, response
            else:
                output_logger.error(f"Bitget v2 下单失败: {response}")
                return False, response

        except Exception as e:
            self.error_handler(e)
            return False, None

    def get_balance_info(self):
        try:
            # 使用v2 API获取账户信息
            response = self.base_api.get("/api/v2/mix/account/accounts", {"productType": "USDT-FUTURES"})

            if response.get('code') == '00000' and response.get('data'):
                account_data = response['data'][0]
                total_equity = float(account_data.get('usdtEquity', '0'))
                available_balance = float(account_data.get('available', '0'))
                return total_equity, available_balance
            else:
                output_logger.error(f"Bitget v2 余额查询失败: {response}")
                return None, None

        except Exception as e:
            self.error_handler(e)
            return None, None

    def get_symbol_info(self, symbol):
        """获取交易对信息，返回最小交易量、最大交易量和合约面值"""
        try:
            symbol = self._convert_symbol(symbol)

            # 使用v2 API获取合约信息
            response = self.base_api.get("/api/v2/mix/market/contracts", {"productType": "USDT-FUTURES"})

            if response.get('code') == '00000' and response.get('data'):
                # 查找指定的交易对
                for contract in response['data']:
                    if contract['symbol'] == symbol:
                        min_trade_num = float(contract.get('minTradeNum', '1'))
                        max_trade_num = float(contract.get('maxOrderQty', '999999'))
                        # Bitget v2 直接使用币的数量，合约面值为1
                        return min_trade_num, max_trade_num, 1.0
            return None, None, None
        except Exception as e:
            self.error_handler(e)
            return None, None, None

    def get_orderbook(self, symbol):
        """获取订单簿数据"""
        try:
            symbol = self._convert_symbol(symbol)
            response = self.base_api.get("/api/v2/mix/market/orderbook", {
                "symbol": symbol,
                "productType": "USDT-FUTURES",
                "limit": "15"
            })

            if response.get('code') == '00000' and response.get('data'):
                data = response['data']
                asks = data.get('asks', [])  # 卖单 [[price, size], ...]
                bids = data.get('bids', [])  # 买单 [[price, size], ...]
                return asks, bids
            return None, None
        except Exception as e:
            self.error_handler(e)
            return None, None

    def _convert_symbol(self, symbol):
        """转换symbol格式"""
        if symbol.endswith('USDT'):
            return symbol
        elif symbol.endswith('_UMCBL'):
            return symbol.replace('_UMCBL', '')
        return symbol + 'USDT'

    def error_handler(self, e):
        if isinstance(e, (BitgetAPIException, BitgetParamsException, BitgetRequestException)):
            self.log_error(e)
        else:
            output_logger.info(f'Bitget v2 error: {e}')

    def log_error(self, e):
        output_logger.info(f'Bitget v2 Error Code: {e.code}. Error Message: {e.message}.')

    def get_order_detail(self, orderid: str):
        """获取订单详情 - Bitget v2"""
        try:
            symbol, timestamp = self.strip_orderid(orderid)
            symbol_converted = self._convert_symbol(symbol)

            response = self.base_api.get("/api/v2/mix/order/detail", {
                "symbol": symbol_converted,
                "productType": "USDT-FUTURES",
                "clientOid": orderid
            })

            if response.get('code') == '00000' and response.get('data'):
                order_data = response['data']
                detail = {
                    "symbol": symbol,
                    "avg_price": float(order_data.get('priceAvg', '0')),
                    "exec_qty": float(order_data.get('baseVolume', '0')),
                    "exec_fee": abs(float(order_data.get('fee', '0'))),
                    "created_time": int(order_data.get('cTime', '0')),
                    "our_time": int(timestamp),
                    "exchange": self.exchange_name,
                    "orderid": orderid,
                }
                return detail
            else:
                output_logger.info(f'Bitget v2 get_order_detail failed, orderid: {orderid}, response: {response}')
                return None

        except Exception as e:
            self.error_handler(e)
            return None

    def get_order_details(self, order_id, symbol):
        """查询订单详情"""
        try:
            symbol_converted = self._convert_symbol(symbol)
            response = self.base_api.get("/api/v2/mix/order/detail", {
                "symbol": symbol_converted,
                "orderId": order_id
            })
            if response.get('code') == '00000' and response.get('data'):
                return response['data']
            return None
        except Exception as e:
            self.error_handler(e)
            return None
    
#     def error_handler(self):
#         return super().error_handler()
    
#     def log_error(self):
#         return super().log_error()
    
#     def get_balance_info(self):
#         return super().get_balance_info()
    

class OkxExchangeHTTP(BaseExchangeHTTP):
    def __init__(self, exchange_name, api_key, api_secret, passphrase, isTest=False):
        super().__init__(exchange_name, api_key, api_secret, passphrase, isTest)
        self.exchange_name = 'Okx'

    def connect(self):
        try:
            # 调试：打印连接参数
            output_logger.info(f"OKX connecting with isTest={self.isTest}, passphrase length={len(self.passphrase)}")

            api = OkxRestClient(self.api_key, self.api_secret, self.passphrase,simulation=self.isTest)
            self.session = api

            # 测试连接
            try:
                test_response = api.publicdata.get_instruments(instType="SWAP", instId="BTC-USDT-SWAP")
                output_logger.info(f"OKX connection test successful: {test_response.get('code', 'unknown')}")
                return True
            except Exception as test_e:
                output_logger.info(f"OKX connection test failed: {test_e}")
                return False

        except Exception as e:
            self.error_handler(e)
            return False

    def _symbol_convert(self,symbol:str) -> str:
        return symbol.replace('USDT','-USDT-SWAP')
    
    # Get instrument information to check lot size requirements
    def _get_instrument_info(self, instId):
        """Get instrument information including minSz and lotSz"""
        return self.session.publicdata.get_instruments(instType="SWAP", instId=instId)
    
    def _get_position_mode(self):
        """Get current position mode from account config"""
        account_config = self.session.account.get_account_config()
        return account_config['data'][0]['posMode']

    def place_order(self,category,symbol,side,orderType,qty,action):
        """
        tdMode: https://www.okx.com/docs-v5/trick_zh/#order-management
        side: buy/ sell
        ordType: market, limit

        return success:bool, response(most likely a dict)
        """
        #fix lotSz problem, posSide problem. only consider long_short_mode
        posSide = ''
        if action == 'open' and side == 'buy':
            posSide = 'long'
        elif action == 'open' and side == 'sell':
            posSide = 'short'
        elif action == 'close' and side == 'sell':
            posSide = 'long'
        elif action =='close' and side == 'buy':
            posSide = 'short'
        tdMode = 'cross'
        orderid = self.generate_orderid(symbol)
        symbol = self._symbol_convert(symbol)

        instrument_info = self._get_instrument_info(symbol)
        min_size = float(instrument_info['data'][0]['minSz'])
        lot_size = float(instrument_info['data'][0]['lotSz'])
        print(f'min_size: {min_size}, lot_size: {lot_size}')
        # BTC-USDT-SWAP lotSz = 0.01
        succeed = True
        try:
            # api.trade.place_order(instId, tdMode, side, ordType, sz, ccy='', clOrdId='', posSide='', px='', pxUsd='', pxVol='', reduceOnly='', tgtCcy='', banAmend=False, tradeQuoteCcy='', stpMode='', attachAlgoOrds=None)
            print(f'placing order: {symbol}, {tdMode}, {side}, {orderType}, {qty}, {posSide}')
            response = self.session.trade.place_order(symbol,tdMode,side,orderType,qty,ccy='', clOrdId=orderid, posSide=posSide, px='', pxUsd='', pxVol='', reduceOnly='', tgtCcy='', banAmend=False, tradeQuoteCcy='', stpMode='', attachAlgoOrds=None)
            if response['code'] == '1':
                succeed = False
                details = response['data'][0]
                # failed
                if isinstance(details['sCode'],str):
                    output_logger.info(f'Okx Message: {response["msg"]}')
                    output_logger.info(f'Okx Error Code: {details["sCode"]}, Error Message: {details["sMsg"]}')
            elif response['code'] == '0':
                succeed = True

            else:
                raise ValueError
            
            response['orderid'] = orderid
            print(f'Okx place_order response: {response}')
            return succeed, response
        except Exception as e:
            self.error_handler(e)
            return False, None
        
    def get_balance_info(self):
        """usdt balance"""
        try:
            res = self.session.account.get_balance()
            # 调试：打印完整响应
            output_logger.info(f"OKX get_balance response: {res}")

            if res['code'] == '0' and res['data']:
                account_data = res['data'][0]
                total_equity = account_data['totalEq']

                # 查找USDT的可用余额
                usdt_balance = None
                for detail in account_data['details']:
                    if detail['ccy'] == 'USDT':
                        usdt_balance = detail['availBal']
                        break

                # 如果没找到USDT，使用第一个币种的余额
                if usdt_balance is None and len(account_data['details']) > 0:
                    usdt_balance = account_data['details'][0]['availBal']

                if usdt_balance is not None or total_equity == '0':
                    usdt_balance = usdt_balance or '0'  # 如果为None，设为'0'
                    return float(total_equity), float(usdt_balance)
                else:
                    output_logger.info(f"OKX: 未找到可用余额信息。账户数据: {account_data}")
                    return None, None
            else:
                output_logger.info(f"OKX get_balance_info code: {res['code']}, msg: {res.get('msg', 'Unknown error')}")
                return None, None
        except Exception as e:
            self.error_handler(e)
            return None,None
        
    def get_orderbook(self,symbol):
        try:
            ret = self.session.get_orderbook(category='linear',symbol=symbol)
            if ret['retCode'] == '0':
                asks = ret['result']['a']
                bids = ret['result']['b']
                return asks, bids
            else:
                output_logger.info(f'Bybit get_orderbook retCode: {ret["retCode"]}, retMsg: {ret["retMsg"]}')
        except Exception as e:
            self.error_handler(e)

    def get_order_detail(self, orderid:str):
        """https://www.okx.com/docs-v5/en/#order-book-trading-trade-get-order-details

        """
        try:
            print(f'okx get_order_detail orderid: {orderid}')
            symbol,timestamp = self.strip_orderid(orderid)
            print(f'okx get_order_detail symbol: {symbol}, timestamp: {timestamp}')
            instId = self._convert_to_okx_symbol(symbol)
            result = self.session.trade.get_order(instId, clOrdId=orderid)
            print(f'okx get_order_detail result: {result}') 
            if result["code"] == "0" and result.get("data") and len(result["data"]) > 0:
                order_detail = result["data"][0]
                detail = {"symbol": symbol,
                    "avg_price": float(order_detail['avgPx']),
                    "exec_qty": float(order_detail['accFillSz']),
                    "exec_fee": abs(float(order_detail['fee'])),
                    "created_time": int(order_detail['fillTime']),
                    "our_time":int(timestamp),
                    "exchange":self.exchange_name,
                    "orderid":orderid,
                }
                print(f'okx get_order_detail detail: {detail}')
                return detail
            else:
                output_logger.info(f'okx get_order_detail failed, orderid: {orderid}, result: {result}')
                return None

        except Exception as e:
            print(f'okx get_order_detail exception: {e}')
            self.error_handler(e)
            return None

    def error_handler(self,e):
        if isinstance(e,(OkxApiException,OkxParamsException,OkxApiException)):
            self.log_error(e)
        else:
            output_logger.info(f'Okx error: {e}')
    
    def log_error(self, e):
        output_logger.info(f'Okx status code: {e.status_code}. Error message: {e.message}')

    def get_symbol_info(self,symbol) -> float:
        lot_size = None
        max_market_sz = None
        ct_val = None
        try:
            symbol = self._symbol_convert(symbol)
            inst_info = self.session.account.get_instruments(instType='SWAP',instId=symbol)
            if inst_info['code'] == '0':
                if len(inst_info['data']) >= 1:
                    info_info = inst_info['data'][0]
                    lot_size = info_info['lotSz']
                    max_market_sz = info_info['maxMktSz']
                    ct_val = info_info['ctVal']
                    if len(inst_info['data']) > 1:
                        output_logger.info(f'警告: 获取最小交易量时返还了多个交易对，返回值可能有误')
                return float(lot_size),float(max_market_sz),float(ct_val)
        except Exception as e:
            self.error_handler(e)
            return None,None
        
    def _convert_to_okx_symbol(self, symbol):
        """将标准symbol转换为OKX格式"""
        # BTCUSDT -> BTC-USDT-SWAP
        if 'USDT' in symbol:
            base = symbol.replace('USDT', '')
            return f"{base}-USDT-SWAP"
        return symbol

    def coins_to_contracts(self, symbol, coin_amount):
        """将币的数量转换为OKX合约张数"""
        try:
            _, _, ct_val = self.get_symbol_info(symbol)
            if ct_val:
                return int(float(coin_amount) / ct_val)  # 向下取整到整张
            return None
        except Exception as e:
            self.error_handler(e)
            return None

    def contracts_to_coins(self, symbol, contract_amount):
        """将OKX合约张数转换为币的数量"""
        try:
            _, _, ct_val = self.get_symbol_info(symbol)
            if ct_val:
                return contract_amount * ct_val
            return None
        except Exception as e:
            self.error_handler(e)
            return None

    def get_order_details(self, order_id, symbol):
        """查询订单详情"""
        try:
            symbol_converted = self._symbol_convert(symbol)
            response = self.session.trade.get_order(instId=symbol_converted, ordId=order_id)
            if response.get('code') == '0' and response.get('data'):
                return response['data'][0]
            return None
        except Exception as e:
            self.error_handler(e)
            return None
class BinanceExchangeHTTP(BaseExchangeHTTP):
    def __init__(self,exchange_name, api_key, api_secret, isTest=False):
        super().__init__(exchange_name, api_key, api_secret, None, isTest)
        self.exchange_name = 'Binance'


    def connect(self):
        try:
            client = BinanceClient(self.api_key, self.api_secret, testnet=self.isTest)
            self.session = client
            return True
        except Exception as e:
            self.error_handler(e)
            return False
    
    def get_balance_info(self):
        try:
            # 获取期货账户余额
            res = self.session.futures_account_balance()

            # 调试：打印响应结构
            # output_logger.info(f"Binance futures_account_balance response: {res}")

            if res and len(res) > 0:
                # 查找USDT余额
                usdt_balance = None
                for balance_info in res:
                    if balance_info['asset'] == 'USDT':
                        usdt_balance = balance_info
                        break

                if usdt_balance:
                    return float(usdt_balance['balance']), float(usdt_balance['availableBalance'])
                else:
                    # 如果没找到USDT，使用第一个资产
                    first_balance = res[0]
                    return float(first_balance['balance']), float(first_balance['availableBalance'])
            else:
                output_logger.info("Binance: 未找到账户余额信息")
                return None, None

        except Exception as e:
            self.error_handler(e)
            return None, None

    def place_order(self, category,symbol,side:str,orderType,qty):
        """sample output for ret {
 	"...
 	"cumQty": "0",
 	"cumQuote": "0", // 成交金额
 	"executedQty": "0", // 成交量
 	"orderId": 22542179, // 系统订单号
 	"avgPrice": "0.00000",	// 平均成交价
 	"origQty": "10", // 原始委托数量
 	"price": "0", // 委托价格
 	"side": "SELL", // 买卖方向
 	"positionSide": "SHORT", // 持仓方向
 	"status": "NEW", // 订单状态
 	"stopPrice": "0", // 触发价，对`TRAILING_STOP_MARKET`无效
     ...
 	"symbol": "BTCUSDT", // 交易对
 	"timeInForce": "GTD", // 有效方法
 	"origType": "TRAILING_STOP_MARKET",  // 触发前订单类型
 	"activatePrice": "9020", // 跟踪止损激活价格, 仅`TRAILING_STOP_MARKET` 订单返回此字段
  	"priceRate": "0.3",	// 跟踪止损回调比例, 仅`TRAILING_STOP_MARKET` 订单返回此字段
 	"updateTime": 1566818724722, // 更新时间
 	"workingType": "CONTRACT_PRICE", // 条件价格触发类型
 	"priceProtect": false,            // 是否开启条件单触发保护
 	"priceMatch": "NONE",              //盘口价格下单模式
 	"selfTradePreventionMode": "NONE", //订单自成交保护模式
 	"goodTillDate": 1693207680000      //订单TIF为GTD时的自动取消时间
     ...
}"""
        try:
            # 生成自定义订单ID
            orderid = self.generate_orderid(symbol)

            ret = self.session.futures_create_order(
            symbol=symbol,
            side=side.upper(),
            type=orderType.upper(),
            quantity=qty,
            positionSide="BOTH",  #TODO check mode BOTH for One-way Mode ; LONG or SHORT for Hedge Mode
            newClientOrderId=orderid  # 添加自定义订单ID
        )

            # 添加自定义订单ID到响应中
            if ret:
                ret['orderid'] = orderid

            return True, ret
        except Exception as e:
            self.error_handler(e)
            return False, None
    
    def get_symbol_info(self, symbol):
        """获取交易对信息，返回最小交易量、最大交易量和合约面值"""
        try:
            # 获取交易规则信息
            exchange_info = self.session.futures_exchange_info()
            for symbol_info in exchange_info['symbols']:
                if symbol_info['symbol'] == symbol:
                    # 查找LOT_SIZE过滤器
                    for filter_info in symbol_info['filters']:
                        if filter_info['filterType'] == 'LOT_SIZE':
                            min_qty = float(filter_info['minQty'])
                            max_qty = float(filter_info['maxQty'])
                            # Binance直接使用币的数量，合约面值为1
                            return min_qty, max_qty, 1.0
            return None, None, None
        except Exception as e:
            self.error_handler(e)
            return None, None, None

    def error_handler(self,e):
        if isinstance(e, BinanceAPIException):
            self.log_error(e)
        elif isinstance(e, BinanceRequestException):
            output_logger.info(f'Binance Request Error: {e}')
        else:
            output_logger.info(f'Binance Unknown Error: {e}')
            
    def get_orderbook(self, symbol):
        """获取订单簿数据"""
        try:
            result = self.session.futures_order_book(symbol=symbol, limit=20)
            asks = result['asks']  # 卖单 [[price, quantity], ...]
            bids = result['bids']  # 买单 [[price, quantity], ...]
            return asks, bids
        except Exception as e:
            self.error_handler(e)
            return None, None

    def log_error(self,e):
        output_logger.info(f'Binance Error Code: {e.code}. Error Message: {e.message}.')

    def get_order_detail(self, orderid: str):
        """获取订单详情 - Binance"""
        try:
            print(f'binance get_order_detail orderid: {orderid}')
            symbol, timestamp = self.strip_orderid(orderid)

            # 使用 Binance API 查询订单详情
            response = self.session.futures_get_order(symbol=symbol, origClientOrderId=orderid)
            print(f'binance get_order_detail response: {response}')

            if response:
                detail = {
                    "symbol": symbol,
                    "avg_price": float(response.get('avgPrice', '0')),
                    "exec_qty": float(response.get('executedQty', '0')),
                    "exec_fee": abs(float(response.get('commission', '0'))),
                    "created_time": int(response.get('time', '0')),
                    "our_time": int(timestamp),
                    "exchange": self.exchange_name,
                    "orderid": orderid,
                }
                print(f'binance get_order_detail detail: {detail}')
                return detail
            else:
                output_logger.info(f'Binance get_order_detail failed, orderid: {orderid}')
                return None

        except Exception as e:
            self.error_handler(e)
            return None

    def get_order_details(self, order_id, symbol):
        """查询订单详情"""
        try:
            response = self.session.futures_get_order(symbol=symbol, orderId=order_id)
            return response
        except Exception as e:
            self.error_handler(e)
            return None
class BaseExchangeWs(ABC):
    def __init__(self, exchange_name,api_key,api_secret,passphrase,isTest):
        self.exchange_name = exchange_name
        self.session = None
        self.api_key = api_key
        self.passphrase = passphrase
        api_secret = api_secret
        self.isTest = isTest

    @abstractmethod
    def connect(self):
        pass


    @abstractmethod
    def place_order(self):
        pass
    
    @abstractmethod
    def log_error(self):
        pass

    @abstractmethod
    def get_balance_info(self):
        pass
class BybitExchangeWs(BaseExchangeWs):
    def __init__(self,exchange_name,api_key,api_secret,isTest):
        super().__init__(exchange_name, api_key, api_secret, None, isTest)  #ignore passphrase
        self.ws_trading = None
        self.api_key = api_key
        self.api_secret = api_secret
        self.isTest = isTest

    def connect(self):
        try:
            ws_trading = WebSocketTrading(
        testnet=True,
        api_key=self.api_key,
        api_secret=self.api_secret,
    )
            self.ws_trading = ws_trading
            return True
        except InvalidRequestError as e:
            self.log_error(e)

    def handle_place_order_message(self,message):
        # Receive the orderId
        output_logger.info(message)
        print(message)

    def place_order(self,category,symbol,side,orderType,price,qty):
        """orderType Limit / Market
            side Buy / Sell """
        try:
            self.ws_trading.place_order(
            self.handle_place_order_message,
            category=category,
            symbol=symbol,
            side=side,
            orderType=orderType,
            price=price,
            qty=qty)

        except InvalidRequestError as e:
            self.log_error(e)
        
        except FailedRequestError as e:
            self.log_error(e)

        except Exception as e:
            print(e)
            
    def log_error(self,e):
        output_logger.info(f'错误代码: {e.status_code}, 错误信息: {e.message}')


    def get_balance_info(self):
        pass
    

