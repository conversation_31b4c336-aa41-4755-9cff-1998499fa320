#!/usr/bin/env python3
"""
测试余额查询修复
"""

from logging_setup import setup_loggers
import logging

# 设置日志
setup_loggers()
decision_logger = logging.getLogger('decision_logger')
output_logger = logging.getLogger('output_logger')

def test_binance_balance_fix():
    """测试Binance余额查询修复"""
    print("=== 测试 Binance 余额查询修复 ===")
    
    try:
        from exchange import BinanceExchangeHTTP
        from api_keys import EXCHANGE_API_KEYS
        
        # 创建实例（使用测试模式）
        binance = BinanceExchangeHTTP('Binance', 
                                     api_key=EXCHANGE_API_KEYS['binance']['api_key'],
                                     api_secret=EXCHANGE_API_KEYS['binance']['secret'],
                                     isTest=True)  # 使用测试网络
        
        print("✅ BinanceExchangeHTTP 实例化成功")
        
        # 测试连接
        connected = binance.connect()
        if connected:
            print("✅ Binance 连接成功")
            
            # 测试余额查询
            equity, balance = binance.get_balance_info()
            if equity is not None:
                print(f"✅ Binance 余额查询成功: 总资产={equity}, 可用余额={balance}")
            else:
                print("⚠️ Binance 余额查询返回None（可能是账户为空）")
        else:
            print("❌ Binance 连接失败")
            
    except Exception as e:
        print(f"❌ Binance 测试失败: {e}")

def test_bybit_balance_fix():
    """测试Bybit余额查询修复"""
    print("\n=== 测试 Bybit 余额查询修复 ===")
    
    try:
        from exchange import BybitExchangeHTTP
        from api_keys import EXCHANGE_API_KEYS
        
        # 创建实例
        bybit = BybitExchangeHTTP('Bybit', 
                                 api_key=EXCHANGE_API_KEYS['bybit']['api_key'],
                                 api_secret=EXCHANGE_API_KEYS['bybit']['secret'])
        
        print("✅ BybitExchangeHTTP 实例化成功")
        
        # 测试连接
        connected = bybit.connect()
        if connected:
            print("✅ Bybit 连接成功")
            
            # 测试余额查询
            equity, balance = bybit.get_balance_info()
            if equity is not None:
                print(f"✅ Bybit 余额查询成功: 总资产={equity}, 可用余额={balance}")
            else:
                print("⚠️ Bybit 余额查询返回None")
        else:
            print("❌ Bybit 连接失败")
            
    except Exception as e:
        print(f"❌ Bybit 测试失败: {e}")

def test_okx_connection_fix():
    """测试OKX连接修复"""
    print("\n=== 测试 OKX 连接修复 ===")
    
    try:
        from exchange import OkxExchangeHTTP
        from api_keys import EXCHANGE_API_KEYS
        
        # 创建实例（使用测试模式）
        okx = OkxExchangeHTTP('OKX', 
                             api_key=EXCHANGE_API_KEYS['okx']['api_key'],
                             api_secret=EXCHANGE_API_KEYS['okx']['secret'],
                             passphrase=EXCHANGE_API_KEYS['okx']['passphrase'],
                             isTest=True)
        
        print("✅ OkxExchangeHTTP 实例化成功")
        
        # 测试连接
        connected = okx.connect()
        if connected:
            print("✅ OKX 连接成功")
            
            # 测试余额查询
            equity, balance = okx.get_balance_info()
            if equity is not None:
                print(f"✅ OKX 余额查询成功: 总资产={equity}, 可用余额={balance}")
            else:
                print("⚠️ OKX 余额查询返回None")
        else:
            print("❌ OKX 连接失败")
            
    except Exception as e:
        print(f"❌ OKX 测试失败: {e}")

def main():
    """主测试函数"""
    print("=== 余额查询修复测试 ===\n")
    
    test_binance_balance_fix()
    test_bybit_balance_fix()
    test_okx_connection_fix()
    
    print("\n=== 测试完成 ===")
    print("注意：如果看到详细的调试信息，说明修复生效了")
    print("现在可以运行: python trader.py")

if __name__ == "__main__":
    main()
