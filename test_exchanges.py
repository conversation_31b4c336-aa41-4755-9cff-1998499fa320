#!/usr/bin/env python3
"""
测试交易所连接和基本功能
"""

# 先测试导入是否成功
print("测试导入...")

try:
    from logging_setup import setup_loggers
    print("✅ logging_setup 导入成功")
except ImportError as e:
    print(f"❌ logging_setup 导入失败: {e}")

try:
    from api_keys import EXCHANGE_API_KEYS
    print("✅ api_keys 导入成功")
except ImportError as e:
    print(f"❌ api_keys 导入失败: {e}")

# 设置日志
import logging
try:
    setup_loggers()
    logger = logging.getLogger('decision_logger')
    print("✅ 日志设置成功")
except Exception as e:
    print(f"❌ 日志设置失败: {e}")

# 测试交易所类导入
try:
    from exchange import BybitExchangeHTTP
    print("✅ BybitExchangeHTTP 导入成功")
except ImportError as e:
    print(f"❌ BybitExchangeHTTP 导入失败: {e}")

try:
    from exchange import BinanceExchangeHTTP
    print("✅ BinanceExchangeHTTP 导入成功")
except ImportError as e:
    print(f"❌ BinanceExchangeHTTP 导入失败: {e}")

try:
    from exchange import OkxExchangeHTTP
    print("✅ OkxExchangeHTTP 导入成功")
except ImportError as e:
    print(f"❌ OkxExchangeHTTP 导入失败: {e}")

try:
    from exchange import Bitgetv1HTTP
    print("✅ Bitgetv1HTTP 导入成功")
except ImportError as e:
    print(f"❌ Bitgetv1HTTP 导入失败: {e}")

def test_basic_functionality():
    """测试基本功能（如果导入成功）"""
    print("\n=== 测试基本功能 ===")

    # 只有在所有导入都成功的情况下才进行功能测试
    try:
        from exchange import BybitExchangeHTTP, BinanceExchangeHTTP, OkxExchangeHTTP, Bitgetv1HTTP
        print("✅ 所有交易所类导入成功，可以进行功能测试")

        # 测试类实例化（不连接API）
        try:
            bybit = BybitExchangeHTTP('Bybit', api_key='test', api_secret='test')
            print("✅ BybitExchangeHTTP 实例化成功")
        except Exception as e:
            print(f"❌ BybitExchangeHTTP 实例化失败: {e}")

        try:
            binance = BinanceExchangeHTTP('Binance', api_key='test', api_secret='test')
            print("✅ BinanceExchangeHTTP 实例化成功")
        except Exception as e:
            print(f"❌ BinanceExchangeHTTP 实例化失败: {e}")

        try:
            okx = OkxExchangeHTTP('OKX', api_key='test', api_secret='test', passphrase='test')
            print("✅ OkxExchangeHTTP 实例化成功")
        except Exception as e:
            print(f"❌ OkxExchangeHTTP 实例化失败: {e}")

        try:
            bitget = Bitgetv1HTTP('Bitget', api_key='test', api_secret='test', passphrase='test')
            print("✅ Bitgetv1HTTP 实例化成功")
        except Exception as e:
            print(f"❌ Bitgetv1HTTP 实例化失败: {e}")

    except ImportError as e:
        print(f"❌ 交易所类导入失败，跳过功能测试: {e}")

if __name__ == "__main__":
    print("=== 循环导入修复测试 ===")
    test_basic_functionality()
    print("\n测试完成!")
