"""
Binance WebSocket bookTicker (best bid/ask) subscription example.

Features:
  - Combined stream subscription for multiple symbols
  - Automatic reconnect with exponential backoff
  - Client heartbeat (ping frames) + message inactivity watchdog
  - Graceful shutdown (Ctrl+C)
  - Simple callback structure for further processing/storage

Author: Example
Python: 3.10+
Dependencies: websockets==12.0
"""

import asyncio
import json
import logging
import signal
import sys
import time
from dataclasses import dataclass
from typing import List, Dict, Callable, Awaitable, Optional

import websockets
from websockets import WebSocketClientProtocol

# ---------------- Configuration ---------------- #

BINANCE_SPOT_WS_BASE = "wss://stream.binance.com:9443/stream?streams="
# 若要连接 U 本位永续合约，请使用：
# BINANCE_FUTURES_WS_BASE = "wss://fstream.binance.com/stream?streams="

# 心跳发送间隔（秒）: 客户端主动发送 ping 帧
HEARTBEAT_INTERVAL = 30
# 若超过该时间（秒）未收到任何消息，判定为超时，触发重连
MESSAGE_TIMEOUT = 90
# 初始重连等待时间（秒）
RECONNECT_BASE_DELAY = 2
# 最大重连等待时间（秒）
RECONNECT_MAX_DELAY = 60

# ---------------- Data Structures ---------------- #

@dataclass
class BookTicker:
    symbol: str
    bid_price: float
    bid_qty: float
    ask_price: float
    ask_qty: float
    update_time: int  # event time (E)
    trans_time: int   # transaction time (T) if provided


# ---------------- Core Client ---------------- #

class BinanceBookTickerClient:
    """
    A WebSocket client for Binance bookTicker stream with:
      - multiple symbol subscription
      - reconnect & heartbeat
    """

    def __init__(
        self,
        symbols: List[str],
        on_ticker: Optional[Callable[[BookTicker], Awaitable[None]]] = None,
        spot: bool = False
    ):
        """
        :param symbols: e.g. ["BTCUSDT", "ETHUSDT"]
        :param on_ticker: async callback receiving BookTicker
        :param spot: True for spot endpoint, False for futures (adjust base URL)
        """
        self.symbols = [s.upper() for s in symbols]
        base = BINANCE_SPOT_WS_BASE if spot else "wss://fstream.binance.com/stream?streams="
        stream_paths = "/".join(f"{s.lower()}@bookTicker" for s in self.symbols)
        self.url = base + stream_paths

        self.on_ticker = on_ticker
        self._ws: Optional[WebSocketClientProtocol] = None
        self._stop = asyncio.Event()

        self._last_message_ts: float = 0.0
        self._reconnect_attempt: int = 0
        self._tasks: list[asyncio.Task] = []

    # ------------- Public API ------------- #
    async def start(self):
        """Start the client (runs until stop() called or exception unrecoverable)."""
        logging.info("Starting BinanceBookTickerClient for symbols: %s", self.symbols)
        while not self._stop.is_set():
            try:
                await self._connect_and_run()
            except asyncio.CancelledError:
                raise
            except Exception as e:
                logging.exception("Unexpected error in main loop: %s", e)

            if self._stop.is_set():
                break

            # Reconnection backoff
            self._reconnect_attempt += 1
            delay = min(RECONNECT_BASE_DELAY * (2 ** (self._reconnect_attempt - 1)), RECONNECT_MAX_DELAY)
            jitter = 0.2 * delay
            wait_time = delay + (jitter * (0.5 - time.time() % 1))  # simple pseudo-jitter
            logging.warning("Reconnecting after %.2fs (attempt %d)", wait_time, self._reconnect_attempt)
            try:
                await asyncio.wait_for(self._stop.wait(), timeout=wait_time)
            except asyncio.TimeoutError:
                pass

        logging.info("Client stopped.")

    async def stop(self):
        """Signal the client to stop."""
        logging.info("Stopping client ...")
        self._stop.set()
        await self._close_ws()
        # Cancel background tasks
        for t in self._tasks:
            if not t.done():
                t.cancel()
        await asyncio.gather(*self._tasks, return_exceptions=True)

    # ------------- Internal ------------- #
    async def _connect_and_run(self):
        logging.info("Connecting to %s", self.url)
        async with websockets.connect(
            self.url,
            ping_interval=None,  # We manage heartbeat manually
            ping_timeout=None,
            max_queue=1024,
            close_timeout=5
        ) as ws:
            self._ws = ws
            self._last_message_ts = time.time()
            self._reconnect_attempt = 0  # reset on successful connect

            # Launch background tasks
            heartbeat_task = asyncio.create_task(self._heartbeat_loop(), name="heartbeat")
            watchdog_task = asyncio.create_task(self._watchdog_loop(), name="watchdog")
            receiver_task = asyncio.create_task(self._receiver_loop(), name="receiver")

            self._tasks = [heartbeat_task, watchdog_task, receiver_task]

            done, pending = await asyncio.wait(
                self._tasks,
                return_when=asyncio.FIRST_EXCEPTION
            )

            # If any task raised, propagate
            for d in done:
                if exc := d.exception():
                    raise exc
        # Exiting context means connection closed
        self._ws = None

    async def _close_ws(self):
        if self._ws is not None:
            try:
                await self._ws.close()
            except Exception:
                pass

    async def _receiver_loop(self):
        assert self._ws is not None
        ws = self._ws
        async for raw in ws:
            self._last_message_ts = time.time()
            try:
                msg = json.loads(raw)
            except json.JSONDecodeError:
                logging.warning("Failed to parse message: %s", raw)
                continue

            # Combined stream structure: {"stream": "...", "data": {...}}
            data = msg.get("data", {})
            if not data:
                continue
            # bookTicker keys: u,s,b,B,a,A,T,E
            if data.get("e") == "bookTicker" or ("b" in data and "a" in data):
                try:
                    ticker = BookTicker(
                        symbol=data["s"],
                        bid_price=float(data["b"]),
                        bid_qty=float(data["B"]),
                        ask_price=float(data["a"]),
                        ask_qty=float(data["A"]),
                        update_time=int(data.get("E", 0)),
                        trans_time=int(data.get("T", 0)),
                    )
                    if self.on_ticker:
                        await self.on_ticker(ticker)
                except Exception as e:
                    logging.exception("Error handling ticker: %s", e)
            else:
                logging.debug("Unhandled message: %s", msg)

    async def _heartbeat_loop(self):
        while not self._stop.is_set():
            await asyncio.sleep(HEARTBEAT_INTERVAL)
            if self._ws is None:
                continue
            try:
                # Send a WebSocket level ping frame (websockets lib handles pong future)
                await self._ws.ping()
                logging.debug("Sent ping")
            except Exception as e:
                logging.warning("Heartbeat ping failed: %s", e)
                # Force reconnect by exiting
                break

    async def _watchdog_loop(self):
        while not self._stop.is_set():
            await asyncio.sleep(5)
            now = time.time()
            if (now - self._last_message_ts) > MESSAGE_TIMEOUT:
                logging.warning(
                    "No message for %.1fs (> %ss), triggering reconnect",
                    now - self._last_message_ts,
                    MESSAGE_TIMEOUT,
                )
                await self._close_ws()
                break


# ---------------- Example Usage ---------------- #

async def example_on_ticker(t: BookTicker):
    # 这里可以写入数据库 / 推送消息队列 / 更新内存订单簿等
    print(
        f"[{time.strftime('%X')}] {t.symbol} "
        f"Bid {t.bid_price:.2f} ({t.bid_qty:.4f})  |  "
        f"Ask {t.ask_price:.2f} ({t.ask_qty:.4f})"
    )


async def main():
    symbols = ["OPUSDT","LDOUSDT","NEARUSDT","CRVUSDT","ZETAUSDT","LRCUSDT","ARBUSDT","LINKUSDT","BCHUSDT","MASKUSDT","ALGOUSDT","YFIUSDT","XLMUSDT","CROUSDT","ETHWUSDT","ETHUSDT","ETCUSDT","USDCUSDT","THETAUSDT","CHZUSDT","AXSUSDT","SANDUSDT","ADAUSDT","FLOWUSDT","AI16ZUSDT","INJUSDT","SUSHIUSDT","1INCHUSDT","MAJORUSDT","MYRIAUSDT","CFXUSDT","ZILUSDT","GMTUSDT","HMSTRUSDT","ORBSUSDT","ATOMUSDT","SOLUSDT","DOGSUSDT","ETHFIUSDT","SAHARAUSDT","KSMUSDT","AVAXUSDT","IDUSDT","BOMEUSDT","MINAUSDT","AAVEUSDT","LPTUSDT","IOTAUSDT","NEOUSDT","ACTUSDT","TONUSDT","TRXUSDT","CELOUSDT","XTZUSDT","YGGUSDT","COMPUSDT","BTCUSDT","BNBUSDT","NOTUSDT","HBARUSDT","SHELLUSDT","1000000MOGUSDT","DOGEUSDT","CETUSUSDT","XRPUSDT","QTUMUSDT","BANDUSDT","ICPUSDT","KAITOUSDT","CATIUSDT","IMXUSDT","SUIUSDT","LTCUSDT","BRETTUSDT","BNTUSDT","NILUSDT","SPKUSDT","FILUSDT","HUMAUSDT","LQTYUSDT","JUPUSDT","PARTIUSDT","AUCTIONUSDT","MAGICUSDT","DOTUSDT"]
    client = BinanceBookTickerClient(symbols, on_ticker=example_on_ticker, spot=False)

    # 优雅退出支持
    loop = asyncio.get_running_loop()
    stop_event = asyncio.Event()

    def _signal_handler():
        if not stop_event.is_set():
            stop_event.set()
            asyncio.create_task(client.stop())

    for sig in (signal.SIGINT, signal.SIGTERM):
        try:
            loop.add_signal_handler(sig, _signal_handler)
        except NotImplementedError:
            # Windows may not fully support signal handlers in subprocess
            pass

    runner = asyncio.create_task(client.start())

    await stop_event.wait()
    await runner


if __name__ == "__main__":
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s | %(levelname)-8s | %(message)s",
        datefmt="%H:%M:%S",
    )
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("Interrupted by user")