"""
Multi-exchange (Bitget / Binance / Bybit / OKX) unified best bid/ask WebSocket collector.

Features:
  - Unified BestQuote dataclass
  - Per-exchange client subclasses with consistent lifecycle (connect -> subscribe -> receive)
  - Heartbeat + inactivity watchdog + exponential backoff reconnect
  - Graceful shutdown (Ctrl+C)
  - Easy to extend: implement _build_subscribe(), _handle_message(), heartbeat payload, endpoint

Exchanges covered (linear / USDT perp focus):
  - Bitget (v2 public ws)   : wss://ws.bitget.com/v2/ws/public
  - Binance USDT-M futures  : wss://fstream.binance.com/stream?streams=... (bookTicker)
  - Bybit linear (v5)       : wss://stream.bybit.com/v5/public/linear
  - OKX public (v5)         : wss://ws.okx.com:8443/ws/v5/public

Python: 3.10+
Dependencies: websockets==12.0

NOTE:
  - This is a demo framework. Production use should add:
      * Robust symbol validation via REST
      * Metrics (Prometheus)
      * Structured logging / log rotation
      * Rate limiting / jitter scheduling
      * Persistent queue (Kafka / Redis) in the on_quote callback
  - Adjust heartbeat/message timeout based on each exchange SLA.
"""

from __future__ import annotations

import asyncio
import json
import logging
import signal
import time
import random
from dataclasses import dataclass, field
from typing import Any, Dict, List, Optional, Callable, Awaitable

import websockets
from websockets import WebSocketClientProtocol

# --------------------------------------------------
# Unified Data Model
# --------------------------------------------------

@dataclass
class BestQuote:
    exchange: str
    symbol: str          # normalized symbol, e.g. BTCUSDT
    raw_symbol: str      # exchange-specific symbol (BTC-USDT-SWAP, etc.)
    bid: float
    bid_size: float
    ask: float
    ask_size: float
    ts_exchange: int     # exchange timestamp ms
    ts_local: int        # local receive time ms
    latency_ms: float
    extra: Dict[str, Any] = field(default_factory=dict)


# --------------------------------------------------
# Base Client
# --------------------------------------------------

class BaseWSClient:
    """
    Abstract base WebSocket client.
    Subclasses must implement:
      - name (str)
      - _connect()
      - _build_subscribe()
      - _handle_message(msg)
      - _send_heartbeat()
    """
    name: str = "base"

    def __init__(
        self,
        symbols: List[str],
        on_quote: Callable[[BestQuote], Awaitable[None]],
        heartbeat_interval: int = 20,
        message_timeout: int = 60,
        reconnect_base: int = 2,
        reconnect_max: int = 60,
    ):
        self.input_symbols = symbols[:]  # user provided
        self.on_quote = on_quote
        self.heartbeat_interval = heartbeat_interval
        self.message_timeout = message_timeout
        self.reconnect_base = reconnect_base
        self.reconnect_max = reconnect_max

        self._ws: Optional[WebSocketClientProtocol] = None
        self._stop_evt = asyncio.Event()
        self._last_msg_time = 0.0
        self._tasks: List[asyncio.Task] = []
        self._reconnect_attempt = 0

    # ------------- Abstract-ish (override where needed) ------------- #
    async def _connect(self) -> WebSocketClientProtocol:
        raise NotImplementedError

    async def _build_subscribe(self):
        """Send subscription messages after connect."""
        raise NotImplementedError

    async def _handle_message(self, msg: Any):
        """Parse a raw JSON-decoded message; call self._emit_quote when needed."""
        raise NotImplementedError

    async def _send_heartbeat(self):
        """Send heartbeat payload if required."""
        # Some exchanges may rely on native ping frames; override if JSON ping required.
        if self._ws:
            try:
                pong_waiter = await self._ws.ping()
                # Optionally: await pong_waiter
            except Exception as e:
                logging.debug("[%s] heartbeat ping error: %s", self.name, e)

    # ------------- Lifecycle ------------- #
    async def start(self):
        logging.info("[%s] starting with symbols=%s", self.name, self.input_symbols)
        while not self._stop_evt.is_set():
            try:
                await self._run_once()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logging.exception("[%s] run loop exception: %s", self.name, e)

            if self._stop_evt.is_set():
                break

            self._reconnect_attempt += 1
            delay = min(self.reconnect_base * (2 ** (self._reconnect_attempt - 1)), self.reconnect_max)
            jitter = random.uniform(0, delay * 0.25)
            wait_time = delay + jitter
            logging.warning("[%s] reconnecting in %.2f s (attempt %d)", self.name, wait_time, self._reconnect_attempt)
            try:
                await asyncio.wait_for(self._stop_evt.wait(), timeout=wait_time)
            except asyncio.TimeoutError:
                pass

        logging.info("[%s] stopped", self.name)

    async def stop(self):
        self._stop_evt.set()
        await self._close_ws()
        for t in self._tasks:
            if not t.done():
                t.cancel()
        await asyncio.gather(*self._tasks, return_exceptions=True)

    async def _close_ws(self):
        if self._ws:
            try:
                await self._ws.close()
            except Exception:
                pass
        self._ws = None

    async def _run_once(self):
        self._ws = await self._connect()
        self._reconnect_attempt = 0
        self._last_msg_time = time.time()
        await self._build_subscribe()

        recv_task = asyncio.create_task(self._recv_loop(), name=f"{self.name}-recv")
        hb_task = asyncio.create_task(self._heartbeat_loop(), name=f"{self.name}-hb")
        wd_task = asyncio.create_task(self._watchdog_loop(), name=f"{self.name}-wd")
        self._tasks = [recv_task, hb_task, wd_task]

        done, pending = await asyncio.wait(self._tasks, return_when=asyncio.FIRST_EXCEPTION)
        for d in done:
            if d.exception():
                raise d.exception()

    async def _recv_loop(self):
        assert self._ws
        ws = self._ws
        async for raw in ws:
            self._last_msg_time = time.time()
            try:
                if isinstance(raw, bytes):
                    raw = raw.decode()
                msg = json.loads(raw)
            except Exception:
                logging.debug("[%s] non-json frame: %s", self.name, raw)
                continue
            await self._handle_message(msg)

    async def _heartbeat_loop(self):
        while not self._stop_evt.is_set() and self._ws:
            await asyncio.sleep(self.heartbeat_interval)
            if self._ws:
                try:
                    await self._send_heartbeat()
                except Exception as e:
                    logging.debug("[%s] heartbeat error: %s", self.name, e)
                    break

    async def _watchdog_loop(self):
        while not self._stop_evt.is_set() and self._ws:
            await asyncio.sleep(5)
            idle = time.time() - self._last_msg_time
            if idle > self.message_timeout:
                logging.warning("[%s] inactivity %.1fs > %ss, closing to reconnect",
                                self.name, idle, self.message_timeout)
                await self._close_ws()
                break

    async def _emit_quote(
        self,
        symbol_norm: str,
        raw_symbol: str,
        bid: float,
        bid_size: float,
        ask: float,
        ask_size: float,
        ts_exchange: int,
        extra: Dict[str, Any] | None = None,
    ):
        ts_local = int(time.time() * 1000)
        latency = ts_local - ts_exchange if ts_exchange > 0 else -1
        quote = BestQuote(
            exchange=self.name,
            symbol=symbol_norm,
            raw_symbol=raw_symbol,
            bid=bid,
            bid_size=bid_size,
            ask=ask,
            ask_size=ask_size,
            ts_exchange=ts_exchange,
            ts_local=ts_local,
            latency_ms=latency,
            extra=extra or {},
        )
        await self.on_quote(quote)


# --------------------------------------------------
# Bitget Client (v2 public) - USDT-FUTURES (Perpetual)
# --------------------------------------------------

class BitgetClient(BaseWSClient):
    name = "bitget"

    WS_URL = "wss://ws.bitget.com/v2/ws/public"
    INST_TYPE = "USDT-FUTURES"  # If your doc states UMCBL, adapt accordingly.

    def __init__(self, symbols: List[str], on_quote: Callable[[BestQuote], Awaitable[None]]):
        # Shorter heartbeat interval because we send explicit JSON ping.
        super().__init__(symbols, on_quote, heartbeat_interval=15, message_timeout=45)

    async def _connect(self):
        logging.info("[bitget] connecting %s", self.WS_URL)
        return await websockets.connect(self.WS_URL, ping_interval=None, ping_timeout=None, max_queue=1024)

    async def _build_subscribe(self):
        # Each arg: {"instType":"USDT-FUTURES","channel":"ticker","instId":"BTCUSDT"}
        args = [
            {
                "instType": self.INST_TYPE,
                "channel": "ticker",
                "instId": s.upper().replace("-", "").replace("_", "")
            } for s in self.input_symbols
        ]
        req = {"op": "subscribe", "args": args}
        await self._ws.send(json.dumps(req))
        logging.info("[bitget] subscribed args=%s", args)

    async def _send_heartbeat(self):
        if self._ws:
            await self._ws.send(json.dumps({"op": "ping"}))

    async def _handle_message(self, msg: Any):
        # Control events
          # Error example: {"event":"error", "code":30016, ...}
        if msg.get("event") == "error":
            logging.error("[bitget] error: %s", msg)
            return

        # Heartbeat from server
        if msg.get("op") == "ping":
            await self._ws.send(json.dumps({"op": "pong"}))
            return
        if msg.get("op") == "pong":
            return
        if "ping" in msg:
            await self._ws.send(json.dumps({"pong": msg["ping"]}))
            return
        if "pong" in msg:
            return

        # Subscription ack
        if msg.get("op") in ("subscribe", "unsubscribe"):
            return

        # Ticker data
        if "arg" in msg and "data" in msg:
            arg = msg["arg"]
            if arg.get("channel") != "ticker":
                return
            for d in msg.get("data", []):
                raw_symbol = d.get("instId") or arg.get("instId")
                # Field names may vary (bidPr/askPr) vs (bestBid/bestAsk) depending on doc version
                bid = d.get("bidPr") or d.get("bestBid")
                ask = d.get("askPr") or d.get("bestAsk")
                bid_sz = d.get("bidSz") or d.get("bestBidSize")
                ask_sz = d.get("askSz") or d.get("bestAskSize")
                ts = d.get("ts") or d.get("systemTime")
                try:
                    await self._emit_quote(
                        symbol_norm=raw_symbol.replace("-", "").upper(),
                        raw_symbol=raw_symbol,
                        bid=float(bid or 0),
                        bid_size=float(bid_sz or 0),
                        ask=float(ask or 0),
                        ask_size=float(ask_sz or 0),
                        ts_exchange=int(ts) if ts else 0,
                        extra={"raw": d},
                    )
                except Exception as e:
                    logging.debug("[bitget] parse error: %s raw=%s", e, d)


# --------------------------------------------------
# Binance Futures (USDT-M) bookTicker
# --------------------------------------------------

class BinanceFuturesClient(BaseWSClient):
    name = "binance"

    BASE = "wss://fstream.binance.com/stream?streams="

    def __init__(self, symbols: List[str], on_quote: Callable[[BestQuote], Awaitable[None]]):
        super().__init__(symbols, on_quote, heartbeat_interval=30, message_timeout=90)

    async def _connect(self):
        # Streams: btcusdt@bookTicker/ethusdt@bookTicker
        streams = "/".join([f"{s.lower()}@bookTicker" for s in self.input_symbols])
        url = self.BASE + streams
        logging.info("[binance] connecting %s", url)
        return await websockets.connect(url, ping_interval=None, ping_timeout=None, max_queue=2048)

    async def _build_subscribe(self):
        # Using combined stream URL already subscribed (Binance combined streams).
        logging.info("[binance] combined stream subscribed automatically.")

    async def _handle_message(self, msg: Any):
        # Combined stream envelope: {"stream":"btcusdt@bookTicker","data":{...}}
        data = msg.get("data")
        if not data:
            return
        # Data fields: s=SYMBOL, b=bestBidPrice, B=bestBidQty, a=bestAskPrice, A=bestAskQty, E=event time, T=transaction time (maybe)
        try:
            symbol = data["s"]
            bid = float(data["b"])
            ask = float(data["a"])
            bid_sz = float(data["B"])
            ask_sz = float(data["A"])
            ts = int(data.get("E") or data.get("T") or 0)
            await self._emit_quote(
                symbol_norm=symbol.upper(),
                raw_symbol=symbol.upper(),
                bid=bid,
                bid_size=bid_sz,
                ask=ask,
                ask_size=ask_sz,
                ts_exchange=ts,
                extra={"raw": data},
            )
        except Exception as e:
            logging.debug("[binance] parse error %s raw=%s", e, msg)


# --------------------------------------------------
# Bybit Linear V5 tickers
# --------------------------------------------------

class BybitClient(BaseWSClient):
    name = "bybit"

    WS_URL = "wss://stream.bybit.com/v5/public/linear"

    def __init__(self, symbols: List[str], on_quote: Callable[[BestQuote], Awaitable[None]]):
        super().__init__(symbols, on_quote, heartbeat_interval=20, message_timeout=60)

    async def _connect(self):
        logging.info("[bybit] connecting %s", self.WS_URL)
        return await websockets.connect(self.WS_URL, ping_interval=None, ping_timeout=None, max_queue=1024)

    async def _build_subscribe(self):
        # {"op":"subscribe","args":["tickers.BTCUSDT","tickers.ETHUSDT"]}
        args = [f"tickers.{s.upper()}" for s in self.input_symbols]
        req = {"op": "subscribe", "args": args}
        await self._ws.send(json.dumps(req))
        logging.info("[bybit] subscribed %s", args)

    async def _send_heartbeat(self):
        if self._ws:
            await self._ws.send(json.dumps({"op": "ping"}))

    async def _handle_message(self, msg: Any):
        # Pong
        if msg.get("op") == "pong":
            return
        if msg.get("op") == "ping":
            await self._ws.send(json.dumps({"op": "pong"}))
            return

        topic = msg.get("topic")
        if not topic or not topic.startswith("tickers."):
            return
        data = msg.get("data")
        if not data:
            return
        # Snapshot or delta both contain best bid/ask (bid1Price, ask1Price)
        try:
            symbol = data.get("symbol") or topic.split(".", 1)[1]
            bid = float(data.get("bid1Price") or 0)
            ask = float(data.get("ask1Price") or 0)
            bid_sz = float(data.get("bid1Size") or 0)
            ask_sz = float(data.get("ask1Size") or 0)
            ts = int(data.get("ts") or data.get("timestamp") or 0)
            await self._emit_quote(
                symbol_norm=symbol.upper(),
                raw_symbol=symbol.upper(),
                bid=bid,
                bid_size=bid_sz,
                ask=ask,
                ask_size=ask_sz,
                ts_exchange=ts,
                extra={"raw": data},
            )
        except Exception as e:
            logging.debug("[bybit] parse error %s raw=%s", e, data)


# --------------------------------------------------
# OKX SWAP tickers
# --------------------------------------------------

class OKXClient(BaseWSClient):
    name = "okx"

    WS_URL = "wss://ws.okx.com:8443/ws/v5/public"

    def __init__(self, symbols: List[str], on_quote: Callable[[BestQuote], Awaitable[None]]):
        super().__init__(symbols, on_quote, heartbeat_interval=15, message_timeout=45)

    @staticmethod
    def _to_swap_inst(symbol: str) -> str:
        # Input could be BTCUSDT or already BTC-USDT-SWAP
        s = symbol.upper().replace("_", "").replace("-", "")
        if s.endswith("USDT"):
            base = s[:-4]  # remove USDT
            return f"{base}-USDT-SWAP"
        return symbol  # fallback

    async def _connect(self):
        logging.info("[okx] connecting %s", self.WS_URL)
        return await websockets.connect(self.WS_URL, ping_interval=None, ping_timeout=None, max_queue=1024)

    async def _build_subscribe(self):
        # {"op":"subscribe","args":[{"channel":"tickers","instId":"BTC-USDT-SWAP"}]}
        args = [{"channel": "tickers", "instId": self._to_swap_inst(s)} for s in self.input_symbols]
        req = {"op": "subscribe", "args": args}
        await self._ws.send(json.dumps(req))
        logging.info("[okx] subscribed %s", args)

    async def _send_heartbeat(self):
        if self._ws:
            await self._ws.send(json.dumps({"op": "ping"}))

    async def _handle_message(self, msg: Any):
        if msg.get("event") == "error":
            logging.error("[okx] error: %s", msg)
            return
        if msg.get("op") == "pong":
            return
        if msg.get("op") == "ping":
            await self._ws.send(json.dumps({"op": "pong"}))
            return

        if "arg" in msg and msg.get("arg", {}).get("channel") == "tickers":
            data_list = msg.get("data") or []
            for d in data_list:
                raw_inst = d.get("instId")
                bid = d.get("bidPx")
                ask = d.get("askPx")
                bid_sz = d.get("bidSz")
                ask_sz = d.get("askSz")
                ts = d.get("ts")
                try:
                    await self._emit_quote(
                        symbol_norm=raw_inst.replace("-", "").replace("SWAP", "").upper().replace("USDT", "USDT"),
                        raw_symbol=raw_inst,
                        bid=float(bid or 0),
                        bid_size=float(bid_sz or 0),
                        ask=float(ask or 0),
                        ask_size=float(ask_sz or 0),
                        ts_exchange=int(ts) if ts else 0,
                        extra={"raw": d},
                    )
                except Exception as e:
                    logging.debug("[okx] parse error %s raw=%s", e, d)


# --------------------------------------------------
# Aggregator / Runner
# --------------------------------------------------

class MultiExchangeRunner:
    def __init__(self, config: Dict[str, List[str]]):
        """
        config example:
          {
            "bitget": ["BTCUSDT","ETHUSDT"],
            "binance": ["BTCUSDT","ETHUSDT"],
            "bybit": ["BTCUSDT"],
            "okx": ["BTCUSDT","ETHUSDT"]
          }
        """
        self.config = config
        self.clients: List[BaseWSClient] = []
        self._stop_evt = asyncio.Event()

    async def on_quote(self, quote: BestQuote):
        # Unified output format - can redirect to queue/DB
        # Print concise line
        print(
            f"{quote.exchange.upper():7s} "
            f"{quote.symbol:10s} "
            f"B:{quote.bid:.2f} ({quote.bid_size:.4f})  "
            f"A:{quote.ask:.2f} ({quote.ask_size:.4f})  "
            f"ex_ts={quote.ts_exchange}  lat={quote.latency_ms}ms"
        )

    def _build_clients(self):
        for ex, symbols in self.config.items():
            if not symbols:
                continue
            ex_lower = ex.lower()
            if ex_lower == "bitget":
                self.clients.append(BitgetClient(symbols, self.on_quote))
            elif ex_lower == "binance":
                self.clients.append(BinanceFuturesClient(symbols, self.on_quote))
            elif ex_lower == "bybit":
                self.clients.append(BybitClient(symbols, self.on_quote))
            elif ex_lower == "okx":
                self.clients.append(OKXClient(symbols, self.on_quote))
            else:
                logging.warning("Unknown exchange key: %s (ignored)", ex)

    async def start(self):
        self._build_clients()
        tasks = [asyncio.create_task(c.start(), name=f"{c.name}-main") for c in self.clients]
        await self._stop_evt.wait()
        # Stop all
        await asyncio.gather(*(c.stop() for c in self.clients), return_exceptions=True)
        for t in tasks:
            if not t.done():
                t.cancel()
        await asyncio.gather(*tasks, return_exceptions=True)

    def stop(self):
        if not self._stop_evt.is_set():
            self._stop_evt.set()


# --------------------------------------------------
# Main Entrypoint
# --------------------------------------------------

async def main():
    # Example config (choose what you really need)
    config = {
        "bitget": ["OPUSDT","LDOUSDT","NEARUSDT","CRVUSDT","ZETAUSDT","LRCUSDT","ARBUSDT","LINKUSDT","BCHUSDT","MASKUSDT","ALGOUSDT","YFIUSDT","XLMUSDT","CROUSDT","ETHWUSDT","ETHUSDT","ETCUSDT","USDCUSDT","THETAUSDT","CHZUSDT","AXSUSDT","SANDUSDT","ADAUSDT","FLOWUSDT","AI16ZUSDT","INJUSDT","SUSHIUSDT","1INCHUSDT","MAJORUSDT","MYRIAUSDT","CFXUSDT","ZILUSDT","GMTUSDT","HMSTRUSDT","ORBSUSDT","ATOMUSDT","SOLUSDT","DOGSUSDT","ETHFIUSDT","SAHARAUSDT","KSMUSDT","AVAXUSDT","IDUSDT","BOMEUSDT","MINAUSDT","AAVEUSDT","LPTUSDT","IOTAUSDT","NEOUSDT","ACTUSDT","TONUSDT","TRXUSDT","CELOUSDT","XTZUSDT","YGGUSDT","COMPUSDT","BTCUSDT","BNBUSDT","NOTUSDT","HBARUSDT","SHELLUSDT","1000000MOGUSDT","DOGEUSDT","CETUSUSDT","XRPUSDT","QTUMUSDT","BANDUSDT","ICPUSDT","KAITOUSDT","CATIUSDT","IMXUSDT","SUIUSDT","LTCUSDT","BRETTUSDT","BNTUSDT","NILUSDT","SPKUSDT","FILUSDT","HUMAUSDT","LQTYUSDT","JUPUSDT","PARTIUSDT","AUCTIONUSDT","MAGICUSDT","DOTUSDT"],
        "binance": ["OPUSDT","LDOUSDT","NEARUSDT","CRVUSDT","ZETAUSDT","LRCUSDT","ARBUSDT","LINKUSDT","BCHUSDT","MASKUSDT","ALGOUSDT","YFIUSDT","XLMUSDT","CROUSDT","ETHWUSDT","ETHUSDT","ETCUSDT","USDCUSDT","THETAUSDT","CHZUSDT","AXSUSDT","SANDUSDT","ADAUSDT","FLOWUSDT","AI16ZUSDT","INJUSDT","SUSHIUSDT","1INCHUSDT","MAJORUSDT","MYRIAUSDT","CFXUSDT","ZILUSDT","GMTUSDT","HMSTRUSDT","ORBSUSDT","ATOMUSDT","SOLUSDT","DOGSUSDT","ETHFIUSDT","SAHARAUSDT","KSMUSDT","AVAXUSDT","IDUSDT","BOMEUSDT","MINAUSDT","AAVEUSDT","LPTUSDT","IOTAUSDT","NEOUSDT","ACTUSDT","TONUSDT","TRXUSDT","CELOUSDT","XTZUSDT","YGGUSDT","COMPUSDT","BTCUSDT","BNBUSDT","NOTUSDT","HBARUSDT","SHELLUSDT","1000000MOGUSDT","DOGEUSDT","CETUSUSDT","XRPUSDT","QTUMUSDT","BANDUSDT","ICPUSDT","KAITOUSDT","CATIUSDT","IMXUSDT","SUIUSDT","LTCUSDT","BRETTUSDT","BNTUSDT","NILUSDT","SPKUSDT","FILUSDT","HUMAUSDT","LQTYUSDT","JUPUSDT","PARTIUSDT","AUCTIONUSDT","MAGICUSDT","DOTUSDT"],
        "bybit": ["OPUSDT","LDOUSDT","NEARUSDT","CRVUSDT","ZETAUSDT","LRCUSDT","ARBUSDT","LINKUSDT","BCHUSDT","MASKUSDT","ALGOUSDT","YFIUSDT","XLMUSDT","CROUSDT","ETHWUSDT","ETHUSDT","ETCUSDT","USDCUSDT","THETAUSDT","CHZUSDT","AXSUSDT","SANDUSDT","ADAUSDT","FLOWUSDT","AI16ZUSDT","INJUSDT","SUSHIUSDT","1INCHUSDT","MAJORUSDT","MYRIAUSDT","CFXUSDT","ZILUSDT","GMTUSDT","HMSTRUSDT","ORBSUSDT","ATOMUSDT","SOLUSDT","DOGSUSDT","ETHFIUSDT","SAHARAUSDT","KSMUSDT","AVAXUSDT","IDUSDT","BOMEUSDT","MINAUSDT","AAVEUSDT","LPTUSDT","IOTAUSDT","NEOUSDT","ACTUSDT","TONUSDT","TRXUSDT","CELOUSDT","XTZUSDT","YGGUSDT","COMPUSDT","BTCUSDT","BNBUSDT","NOTUSDT","HBARUSDT","SHELLUSDT","1000000MOGUSDT","DOGEUSDT","CETUSUSDT","XRPUSDT","QTUMUSDT","BANDUSDT","ICPUSDT","KAITOUSDT","CATIUSDT","IMXUSDT","SUIUSDT","LTCUSDT","BRETTUSDT","BNTUSDT","NILUSDT","SPKUSDT","FILUSDT","HUMAUSDT","LQTYUSDT","JUPUSDT","PARTIUSDT","AUCTIONUSDT","MAGICUSDT","DOTUSDT"],
        "okx": ["OPUSDT","LDOUSDT","NEARUSDT","CRVUSDT","ZETAUSDT","LRCUSDT","ARBUSDT","LINKUSDT","BCHUSDT","MASKUSDT","ALGOUSDT","YFIUSDT","XLMUSDT","CROUSDT","ETHWUSDT","ETHUSDT","ETCUSDT","USDCUSDT","THETAUSDT","CHZUSDT","AXSUSDT","SANDUSDT","ADAUSDT","FLOWUSDT","AI16ZUSDT","INJUSDT","SUSHIUSDT","1INCHUSDT","MAJORUSDT","MYRIAUSDT","CFXUSDT","ZILUSDT","GMTUSDT","HMSTRUSDT","ORBSUSDT","ATOMUSDT","SOLUSDT","DOGSUSDT","ETHFIUSDT","SAHARAUSDT","KSMUSDT","AVAXUSDT","IDUSDT","BOMEUSDT","MINAUSDT","AAVEUSDT","LPTUSDT","IOTAUSDT","NEOUSDT","ACTUSDT","TONUSDT","TRXUSDT","CELOUSDT","XTZUSDT","YGGUSDT","COMPUSDT","BTCUSDT","BNBUSDT","NOTUSDT","HBARUSDT","SHELLUSDT","1000000MOGUSDT","DOGEUSDT","CETUSUSDT","XRPUSDT","QTUMUSDT","BANDUSDT","ICPUSDT","KAITOUSDT","CATIUSDT","IMXUSDT","SUIUSDT","LTCUSDT","BRETTUSDT","BNTUSDT","NILUSDT","SPKUSDT","FILUSDT","HUMAUSDT","LQTYUSDT","JUPUSDT","PARTIUSDT","AUCTIONUSDT","MAGICUSDT","DOTUSDT"],
    }

    runner = MultiExchangeRunner(config)

    loop = asyncio.get_running_loop()
    for sig in (signal.SIGINT, signal.SIGTERM):
        try:
            loop.add_signal_handler(sig, runner.stop)
        except NotImplementedError:
            pass

    await runner.start()


if __name__ == "__main__":
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s | %(levelname)-7s | %(message)s",
        datefmt="%H:%M:%S",
    )
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("Interrupted by user")