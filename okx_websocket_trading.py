#!/usr/bin/env python3
# filename: okx_websocket_trading.py

# ==============================================================================
#  1. 导入 (Imports)
# ==============================================================================
import asyncio
import json
import logging
import time
import hmac
import base64
from typing import Dict, Any

import websockets
from websockets.legacy.client import WebSocketClientProtocol
import redis.asyncio as redis

try:
    from api_keys import EXCHANGE_API_KEYS
except ImportError:
    print("错误：找不到 api_keys.py 文件。请确保该文件与本脚本在同一目录下。")
    exit()

# ==============================================================================
#  2. 全局配置 (Global Configuration)
# ==============================================================================
LOG_FORMAT = "%(asctime)s | %(levelname)-7s | %(module)s:%(lineno)-4d | %(message)s"
logging.basicConfig(level=logging.INFO, format=LOG_FORMAT, datefmt="%H:%M:%S")
logger = logging.getLogger("OkxWsOrderService")

IS_PAPER_TRADING = False
WS_PRIVATE_URL = "wss://wspap.okx.com:8443/ws/v5/private?brokerId=9999" if IS_PAPER_TRADING else "wss://ws.okx.com:8443/ws/v5/private"
REDIS_URL = "redis://localhost:6379/0"
REDIS_ORDER_CHANNEL = "trading_orders:okx"

if IS_PAPER_TRADING:
    logger.info("环境设置为: 模拟盘 (Paper Trading)")
else:
    logger.warning("环境设置为: 实盘 (Live Trading)")


# ==============================================================================
#  3. 核心模块: WebSocket 管理器 (OkxWebSocketManager)
# ==============================================================================
class OkxWebSocketManager:
    def __init__(self, api_key: str, secret: str, passphrase: str, url: str):
        self._api_key, self._secret, self._passphrase, self._url = api_key, secret, passphrase, url
        self._ws: WebSocketClientProtocol = None
        self._is_connected = False
        self._listener_task = None

    async def connect(self):
        while True:
            try:
                logger.info(f"正在尝试连接到 {self._url}...")
                self._ws = await websockets.connect(self._url, ping_interval=20, ping_timeout=20)
                await self._login()
                self._is_connected = True
                logger.info("WebSocket 已连接并认证成功。")
                if self._listener_task: self._listener_task.cancel()
                self._listener_task = asyncio.create_task(self._listen())
                await self._listener_task
            except (websockets.exceptions.ConnectionClosedError, ConnectionRefusedError) as e:
                logger.warning(f"WebSocket 连接断开: {e}. 5秒后将尝试重连...")
            except Exception as e:
                logger.error(f"WebSocket 连接时发生未知错误: {e}", exc_info=True)
                logger.warning("5秒后将尝试重连...")
            finally:
                self._is_connected = False
                await asyncio.sleep(5)

    async def _login(self):
        timestamp = str(time.time())
        message = timestamp + "GET" + "/users/self/verify"
        mac = hmac.new(bytes(self._secret, 'utf-8'), bytes(message, 'utf-8'), 'sha256')
        sign = base64.b64encode(mac.digest()).decode()
        login_payload = {"op": "login", "args": [{"apiKey": self._api_key, "passphrase": self._passphrase, "timestamp": timestamp, "sign": sign}]}
        await self._ws.send(json.dumps(login_payload))
        logger.info("登录请求已发送。")

    async def _listen(self):
        logger.info("WebSocket 消息监听任务已启动。")
        try:
            async for message in self._ws:
                if message == 'pong':
                    logger.debug("收到 pong 响应。")
                    continue
                data = json.loads(message)
                if "event" in data:
                    if data.get("event") == "login" and data.get("code") == "0":
                        logger.info(f"✅ WebSocket 登录成功回执: {data}")
                    elif data.get("event") == "error":
                        logger.error(f"❌ 收到 WebSocket 错误事件: {data}")
                elif data.get("op") == "order":
                    self._handle_order_response(data)
                else:
                    logger.debug(f"收到未处理的 WebSocket 消息: {data}")
        except websockets.exceptions.ConnectionClosed as e:
            logger.warning(f"监听任务因连接关闭而终止: {e}")
            raise

    def _handle_order_response(self, response: Dict[str, Any]):
        request_id = response.get("id", "N/A")
        if response.get("code") != "0":
            logger.error(f"❌ [请求失败] ID: {request_id}, Code: {response.get('code')}, 原因: {response.get('msg', 'N/A')}")
            return
        order_data = response.get('data', [{}])[0]
        cl_ord_id = order_data.get('clOrdId', 'N/A')
        if order_data.get('sCode') == '0':
            logger.info(f"✅ [下单成功] 订单ID: {order_data.get('ordId')},clorid: {cl_ord_id}")
        else:
            logger.error(f"❌ [下单失败] 客户ID: {cl_ord_id}, 错误码: {order_data.get('sCode')}, 原因: {order_data.get('sMsg', 'N/A')}")

    async def place_order(self, order_args: Dict[str, Any]):
        request_id = order_args.get("clOrdId", f"fallback_{int(time.time()*1000)}")
        order_payload = {
            "id": request_id,
            "op": "order",
            "args": [order_args]
        }
        if not self._is_connected or not self._ws:
            logger.error(f"无法发送订单 (ID: {request_id})：WebSocket 未连接。")
            return
        try:
            logger.info(f"-> 准备发送订单 (ID: {request_id}): {json.dumps(order_args)}")
            await self._ws.send(json.dumps(order_payload))
        except Exception as e:
            logger.error(f"发送订单消息时出错 (ID: {request_id}): {e}", exc_info=True)

    async def close(self):
        if self._listener_task: self._listener_task.cancel()
        if self._ws: await self._ws.close()
        logger.info("WebSocket 管理器已关闭。")


# ==============================================================================
#  4. 信号模块: Redis 指令监听器 (Redis Listener)
# ==============================================================================
async def listen_for_redis_orders(ws_manager: OkxWebSocketManager):
    """连接 Redis 并订阅下单频道，验证并直接传递订单字典。"""
    while True:
        try:
            logger.info(f"正在连接到 Redis ({REDIS_URL})...")
            r = await redis.from_url(REDIS_URL, decode_responses=True)
            pubsub = r.pubsub()
            await pubsub.subscribe(REDIS_ORDER_CHANNEL)
            logger.info(f"✅ 已成功订阅 Redis 频道: '{REDIS_ORDER_CHANNEL}'")
            async for message in pubsub.listen():
                if message['type'] != 'message':
                    continue
                logger.info(f"收到来自 Redis 的原始消息: {message['data']}")
                try:
                    order_details_dict = json.loads(message['data'])
                    
                    required_keys = ["instId", "tdMode", "side", "posSide", "ordType", "sz"]
                    if not all(key in order_details_dict for key in required_keys):
                        logger.warning(f"指令缺少必要字段, 需要: {required_keys}, 实际: {list(order_details_dict.keys())}")
                        continue
                    
                    if order_details_dict.get('posSide') not in ['long', 'short', 'net']:
                         logger.warning(f"指令中的 'posSide' 字段值无效: '{order_details_dict.get('posSide')}'")
                         continue
                    
                    await ws_manager.place_order(order_details_dict)

                except json.JSONDecodeError:
                    logger.error(f"无法解析来自 Redis 的 JSON 消息: {message['data']}")
                except Exception as e:
                    logger.error(f"处理 Redis 消息时发生错误: {e}", exc_info=True)
        except redis.exceptions.ConnectionError as e:
            logger.error(f"Redis 连接失败: {e}. 10秒后重试...")
            await asyncio.sleep(10)

# ==============================================================================
#  5. 主程序入口 (Main Execution)
# ==============================================================================
async def main():
    logger.info("启动 OKX SWAP WebSocket 下单服务 (双向持仓模式)...")
    credentials = EXCHANGE_API_KEYS.get("okx")
    if not credentials or "api_key" not in credentials:
        logger.error("错误：请在 api_keys.py 文件中正确配置您的 OKX API 信息。")
        return
    ws_manager = OkxWebSocketManager(
        api_key=credentials["api_key"], secret=credentials["secret"],
        passphrase=credentials["passphrase"], url=WS_PRIVATE_URL
    )
    try:
        await asyncio.gather(ws_manager.connect(), listen_for_redis_orders(ws_manager))
    finally:
        logger.info("服务正在关闭...")
        await ws_manager.close()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("收到用户中断信号 (Ctrl+C)，程序退出。")