#!/usr/bin/env python3
"""
测试日志配置
验证带日期的日志文件是否正常工作
"""

from websocket_logging import setup_websocket_logging, setup_daily_rotating_logger
from logging_setup import setup_loggers
import time

def test_websocket_logging():
    """测试WebSocket日志配置"""
    print("=" * 50)
    print("测试WebSocket日志配置")
    print("=" * 50)
    
    # 测试基本日志配置
    logger = setup_websocket_logging(logger_name="TestWebSocket")
    
    logger.info("这是一条信息日志")
    logger.warning("这是一条警告日志")
    logger.error("这是一条错误日志")
    
    print("WebSocket日志测试完成，请检查 ./logs 目录")

def test_daily_rotating_logger():
    """测试每日轮转日志"""
    print("=" * 50)
    print("测试每日轮转日志")
    print("=" * 50)
    
    logger = setup_daily_rotating_logger(logger_name="TestRotating")
    
    logger.info("这是轮转日志的信息消息")
    logger.warning("这是轮转日志的警告消息")
    logger.error("这是轮转日志的错误消息")
    
    print("轮转日志测试完成，请检查 ./logs 目录")

def test_original_logging():
    """测试原有的日志配置"""
    print("=" * 50)
    print("测试原有日志配置")
    print("=" * 50)
    
    decision_logger, output_logger, history_logger = setup_loggers()
    
    decision_logger.info("决策日志测试消息")
    output_logger.info("输出日志测试消息")
    history_logger.info("历史日志测试消息")
    
    print("原有日志测试完成，请检查 ./cross_exchange_re/log 目录")

if __name__ == "__main__":
    print("开始测试所有日志配置...")
    
    test_websocket_logging()
    time.sleep(1)
    
    test_daily_rotating_logger()
    time.sleep(1)
    
    test_original_logging()
    
    print("\n" + "=" * 50)
    print("所有日志测试完成！")
    print("请检查以下目录的日志文件：")
    print("1. ./logs/ - WebSocket日志文件")
    print("2. ./cross_exchange_re/log/ - 原有日志文件")
    print("=" * 50)
