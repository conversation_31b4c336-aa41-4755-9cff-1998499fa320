#!/usr/bin/env python3
"""
测试OKX单位转换功能
"""

from logging_setup import setup_loggers
import logging

# 设置日志
setup_loggers()
decision_logger = logging.getLogger('decision_logger')
output_logger = logging.getLogger('output_logger')

def test_okx_unit_conversion():
    """测试OKX单位转换"""
    print("=== 测试 OKX 单位转换 ===")
    
    try:
        from exchange import OkxExchangeHTTP
        from api_keys import EXCHANGE_API_KEYS
        
        # 创建OKX实例
        okx = OkxExchangeHTTP('OKX', 
                             api_key=EXCHANGE_API_KEYS['okx']['api_key'],
                             api_secret=EXCHANGE_API_KEYS['okx']['secret'],
                             passphrase=EXCHANGE_API_KEYS['okx']['passphrase'],
                             isTest=True)
        
        print("✅ OkxExchangeHTTP 实例化成功")
        
        # 测试连接
        connected = okx.connect()
        if connected:
            print("✅ OKX 连接成功")
            
            # 测试获取合约信息
            symbol = "BTCUSDT"
            min_qty, max_qty, ct_val = okx.get_symbol_info(symbol)
            
            if ct_val is not None:
                print(f"✅ {symbol} 合约信息:")
                print(f"   最小交易量: {min_qty} 币")
                print(f"   最大交易量: {max_qty} 币") 
                print(f"   合约面值: {ct_val} 币/张")
                
                # 测试单位转换
                test_coins = 0.1  # 0.1 BTC
                contracts = okx.coins_to_contracts(symbol, test_coins)
                coins_back = okx.contracts_to_coins(symbol, contracts)
                
                print(f"\n🔄 单位转换测试:")
                print(f"   {test_coins} 币 -> {contracts} 张")
                print(f"   {contracts} 张 -> {coins_back} 币")
                
                if abs(test_coins - coins_back) < 0.0001:
                    print("✅ 单位转换正确")
                else:
                    print(f"❌ 单位转换有误差: {test_coins} != {coins_back}")
                    
            else:
                print("❌ 获取合约信息失败")
        else:
            print("❌ OKX 连接失败")
            
    except Exception as e:
        print(f"❌ OKX 测试失败: {e}")

def test_other_exchanges_compatibility():
    """测试其他交易所的兼容性"""
    print("\n=== 测试其他交易所兼容性 ===")
    
    try:
        from exchange import BybitExchangeHTTP, BinanceExchangeHTTP, Bitgetv1HTTP
        from api_keys import EXCHANGE_API_KEYS
        
        # 测试Bybit
        try:
            bybit = BybitExchangeHTTP('Bybit', 
                                     api_key=EXCHANGE_API_KEYS['bybit']['api_key'],
                                     api_secret=EXCHANGE_API_KEYS['bybit']['secret'])
            
            if bybit.connect():
                min_qty, max_qty, ct_val = bybit.get_symbol_info('BTCUSDT')
                print(f"✅ Bybit: min={min_qty}, max={max_qty}, ct_val={ct_val}")
            else:
                print("❌ Bybit 连接失败")
        except Exception as e:
            print(f"❌ Bybit 测试失败: {e}")
        
        # 测试Binance
        try:
            binance = BinanceExchangeHTTP('Binance',
                                         api_key=EXCHANGE_API_KEYS['binance']['api_key'],
                                         api_secret=EXCHANGE_API_KEYS['binance']['secret'],
                                         isTest=True)
            
            if binance.connect():
                min_qty, max_qty, ct_val = binance.get_symbol_info('BTCUSDT')
                print(f"✅ Binance: min={min_qty}, max={max_qty}, ct_val={ct_val}")
            else:
                print("❌ Binance 连接失败")
        except Exception as e:
            print(f"❌ Binance 测试失败: {e}")
        
        # 测试Bitget
        try:
            bitget = Bitgetv1HTTP('Bitget',
                                 api_key=EXCHANGE_API_KEYS['bitget']['api_key'],
                                 api_secret=EXCHANGE_API_KEYS['bitget']['secret'],
                                 passphrase=EXCHANGE_API_KEYS['bitget']['passphrase'])
            
            if bitget.connect():
                min_qty, max_qty, ct_val = bitget.get_symbol_info('BTCUSDT')
                print(f"✅ Bitget: min={min_qty}, max={max_qty}, ct_val={ct_val}")
            else:
                print("❌ Bitget 连接失败")
        except Exception as e:
            print(f"❌ Bitget 测试失败: {e}")
            
    except Exception as e:
        print(f"❌ 兼容性测试失败: {e}")

def main():
    """主测试函数"""
    print("=== OKX 单位转换测试 ===\n")
    
    test_okx_unit_conversion()
    test_other_exchanges_compatibility()
    
    print("\n=== 测试完成 ===")
    print("如果看到合约面值信息，说明单位转换功能已实现")

if __name__ == "__main__":
    main()
