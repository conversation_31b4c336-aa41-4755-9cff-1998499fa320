# filename: publisher_example.py

import redis
import json
from order_publisher import WsSignal
from realtime_data_manager import realtime_data_manager

REDIS_CHANNEL = "trading_orders:okx"



# --- 创建一个交易信号 ---
# 场景: 在 KAITO-USDT 永续合约市场上，用市价单买入开多10个单位
open_long_order = WsSignal(
    instId="KAITOUSDT", # OKX 永续合约通常用 -
    tdMode="cross",      # 仓位模式: 逐仓 'isolated' 或 全仓 'cross'
    side="buy",          # 动作: 'buy' 或 'sell'
    ordType="market",    # 订单类型: 'market' (市价), 'limit' (限价)
    sz=10.0,             # 数量
)

# --- 将信号对象转换为 OKX 专用的字典 ---
okx_order_dict = open_long_order.to_okx_dict()

# --- 序列化为 JSON 并发布 ---
message_json = json.dumps(okx_order_dict)

try:
    realtime_data_manager.redis_client.publish(REDIS_CHANNEL, message_json)
    print(f"✅ 成功向频道 '{REDIS_CHANNEL}' 发送OKX格式指令: {message_json}")
except Exception as e:
    print(f"❌ 发送指令失败: {e}")

from order_publisher import okx_place_order, WsSignal,bitget_place_order,bybit_place_order,binance_place_order
open_long_order = WsSignal(
    instId="ETHUSDT", # OKX 永续合约通常用 -
    tdMode="cross",      # 仓位模式: 逐仓 'isolated' 或 全仓 'cross'
    side="buy",          # 动作: 'buy' 或 'sell'
    ordType="market",    # 订单类型: 'market' (市价), 'limit' (限价)
    sz=1.0,             # 数量
)


okx_place_order(open_long_order)

bitget_place_order(open_long_order)

binance_place_order(open_long_order)