import os
import asyncio
import json
import time
import hmac
import hashlib
import signal
import sys
from typing import Optional, Dict, Any
import httpx
import websockets
from websockets import ConnectionClosed
from exchange import BinanceExchangeHTTP

from api_keys import EXCHANGE_API_KEYS

# ------------ 可配置常量 -------------
FUTURES_REST_BASE = "https://fapi.binance.com"          # U本位合约主网
FUTURES_WS_BASE = "wss://fstream.binance.com/ws"

# 如需测试网，把上面两行替换为：
# FUTURES_REST_BASE = "https://testnet.binancefuture.com"
# FUTURES_WS_BASE = "wss://stream.binancefuture.com/ws"

LISTEN_KEY_CREATE_PATH = "/fapi/v1/listenKey"

API_KEY = EXCHANGE_API_KEYS["binance"]["api_key"]
API_SECRET = EXCHANGE_API_KEYS["binance"]["secret"]

if not API_KEY or not API_SECRET:
    print("请设置环境变量 BINANCE_API_KEY 与 BINANCE_API_SECRET")
    sys.exit(1)

# 订单接口路径
ORDER_ENDPOINT = "/fapi/v1/order"

# ------------ 工具函数 -------------
def sign_params(params: Dict[str, Any], secret: str) -> str:
    """
    对参数进行 querystring 排序拼接并 HMAC SHA256 签名。
    """
    query = "&".join(f"{k}={params[k]}" for k in sorted(params.keys()))
    signature = hmac.new(secret.encode(), query.encode(), hashlib.sha256).hexdigest()
    return signature

def epoch_ms() -> int:
    return int(time.time() * 1000)

# ------------ 主类 -------------
class BinanceFuturesUserStream:
    def __init__(
        self,
        rest_base: str = FUTURES_REST_BASE,
        ws_base: str = FUTURES_WS_BASE,
        keepalive_interval: int = 60 * 30 - 30,  # 30 分钟要求内刷新，提前一些
        recv_window: int = 5000,
    ):
        self.rest_base = rest_base
        self.ws_base = ws_base
        self.keepalive_interval = keepalive_interval
        self.recv_window = recv_window

        self.listen_key: Optional[str] = None
        self._http = httpx.AsyncClient(
            timeout=10.0,
            headers={"X-MBX-APIKEY": API_KEY}
        )
        self._ws = None
        self._stop = False
        self._reconnect_attempt = 0

    # ----------- ListenKey 相关 -----------
    async def create_listen_key(self):
        r = await self._http.post(f"{self.rest_base}{LISTEN_KEY_CREATE_PATH}")
        r.raise_for_status()
        self.listen_key = r.json()["listenKey"]
        print(f"[INFO] 创建 listenKey: {self.listen_key}")

    async def keepalive_listen_key(self):
        """
        定期维持 listenKey 有效性（PUT）。
        """
        while not self._stop:
            await asyncio.sleep(self.keepalive_interval)
            if not self.listen_key:
                continue
            try:
                r = await self._http.put(
                    f"{self.rest_base}{LISTEN_KEY_CREATE_PATH}",
                    params={"listenKey": self.listen_key},
                )
                if r.status_code == 200:
                    print(f"[KEEPALIVE] listenKey 保持成功 @ {time.strftime('%X')}")
                else:
                    print(f"[KEEPALIVE] 失败: {r.status_code} {r.text}")
            except Exception as e:
                print(f"[KEEPALIVE] 异常: {e}")

    # ----------- WebSocket -----------
    async def connect_ws(self):
        assert self.listen_key, "listenKey 未创建"
        url = f"{self.ws_base}/{self.listen_key}"
        print(f"[INFO] 连接 WebSocket: {url}")
        self._ws = await websockets.connect(url, ping_interval=15, ping_timeout=10)
        self._reconnect_attempt = 0
        print("[INFO] WebSocket 已连接")

    async def handle_messages(self):
        try:
            async for raw in self._ws:
                try:
                    data = json.loads(raw)
                except json.JSONDecodeError:
                    print(f"[WARN] JSON 解析失败: {raw}")
                    continue
                self.dispatch_event(data)
        except ConnectionClosed as e:
            print(f"[WARN] WS 连接关闭: {e.code} {e.reason}")
            await self.reconnect()
        except Exception as e:
            print(f"[ERROR] WS 异常: {e}")
            await self.reconnect()

    # ----------- 事件分发 -----------
    def dispatch_event(self, data: dict):
        event_type = data.get("e")
        if event_type == "ORDER_TRADE_UPDATE":
            self.handle_order_trade_update(data)
        elif event_type == "ACCOUNT_UPDATE":
            self.handle_account_update(data)
        elif event_type == "MARGIN_CALL":
            print("[MARGIN_CALL]", json.dumps(data, ensure_ascii=False))
        elif event_type == "ACCOUNT_CONFIG_UPDATE":
            print("[ACCOUNT_CONFIG_UPDATE]", json.dumps(data, ensure_ascii=False))
        elif event_type == "STRATEGY_UPDATE":
            print("[STRATEGY_UPDATE]", json.dumps(data, ensure_ascii=False))
        else:
            print(f"[EVENT 未处理] {event_type}: {data}")

    # ----------- 具体事件处理 -----------
    def handle_order_trade_update(self, root: dict):
        """
        Futures: 订单更新在 root['o'] 子对象里。
        官方文档: https://binance-docs.github.io/apidocs/futures/en/#event-order-update
        """
        o = root.get("o", {})
        parsed = {
            "事件时间": self._fmt_ts(root.get("E")),
            "撮合时间": self._fmt_ts(root.get("T")),
            "交易对": o.get("s"),
            "方向": o.get("S"),               # BUY / SELL
            "订单类型": o.get("o"),            # LIMIT / MARKET / ...
            "时效类型": o.get("f"),            # GTC / IOC / FOK / GTX
            "原始数量": o.get("q"),
            "原始价格": o.get("p"),
            "累计成交量": o.get("z"),
            "最近成交量": o.get("l"),
            "最新成交价格": o.get("L"),
            "累计实收(quote)": o.get("Z"),     # 一些推送版本可能无此字段
            "订单状态": o.get("X"),            # NEW / PARTIALLY_FILLED / FILLED / CANCELED / EXPIRED
            "执行类型": o.get("x"),            # NEW / TRADE / CANCELED / EXPIRED 等
            "是否挂单方": o.get("m"),           # True=Maker
            "订单ID": o.get("i"),
            "客户端订单ID": o.get("c"),
            "成交ID": o.get("t"),
            "保证金模式仓位侧": o.get("ps"),    # BOTH / LONG / SHORT
            "触发价/条件价": o.get("sp"),       # Stop Price
            "激活价格(ap)": o.get("ap"),
            "条件类型(wt)": o.get("wt"),        # eg CONTRACT_PRICE
            "原始订单类型(ot)": o.get("ot"),    # eg LIMIT
            "是否条件单触发(cp)": o.get("cp"),
            "真实盈亏(rp)": o.get("rp"),       # Realized PnL
            "手续费资产": o.get("N"),
            "手续费数量": o.get("n"),
            "reduceOnly(R)": o.get("R"),
            "策略ID(si)": o.get("si"),
            "策略类型(ss)": o.get("ss"),
            "触发保护(pP)": o.get("pP"),
        }
        print("[ORDER_TRADE_UPDATE]")
        for k, v in parsed.items():
            print(f"  {k}: {v}")

    def handle_account_update(self, root: dict):
        """
        ACCOUNT_UPDATE 账户余额与持仓更新:
        root['a'] = {
          "m": "ORDER" | "FUNDING" | ...
          "B": [...余额数组...]
          "P": [...持仓数组...]
        }
        """
        a = root.get("a", {})
        print("[ACCOUNT_UPDATE]")
        print(f"  事件时间: {self._fmt_ts(root.get('E'))}")
        print(f"  变更原因: {a.get('m')}")
        balances = a.get("B", [])
        positions = a.get("P", [])
        print("  余额更新:")
        for b in balances:
            # b: {"a": "USDT", "wb": "100.********", "cw": "100.********", "bc": "0"}
            print(f"    资产: {b.get('a')} 钱包余额: {b.get('wb')} 可用余额: {b.get('cw')} 余额变更: {b.get('bc')}")
        print("  持仓更新:")
        for p in positions:
            # p: {"s":"BTCUSDT","pa":"0","ep":"0.00000","cr":"0.********","up":"0","mt":"isolated","iw":"0","ps":"BOTH"}
            print(
                f"    交易对:{p.get('s')} 持仓数量:{p.get('pa')} 开仓均价:{p.get('ep')} "
                f"未实现盈亏:{p.get('up')} 保证金模式:{p.get('mt')} 仓位方向:{p.get('ps')}"
            )

    @staticmethod
    def _fmt_ts(ts: Optional[int]):
        if not ts:
            return ts
        return time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(ts / 1000))

    # ----------- 重连 -----------
    async def reconnect(self):
        if self._stop:
            return
        self._reconnect_attempt += 1
        backoff = min(60, 2 ** self._reconnect_attempt)
        print(f"[RECONNECT] attempt={self._reconnect_attempt} {backoff=}")
        await asyncio.sleep(backoff)
        try:
            # 重新创建 listenKey（也可尝试复用旧的，这里简单化）
            await self.create_listen_key()
            await self.connect_ws()
            await self.handle_messages()
        except Exception as e:
            print(f"[RECONNECT ERROR] {e}")
            await self.reconnect()

    # ----------- 外部运行入口 -----------
    async def run(self):
        await self.create_listen_key()
        keep_task = asyncio.create_task(self.keepalive_listen_key())
        try:
            await self.connect_ws()
            await self.handle_messages()
        finally:
            keep_task.cancel()
            await self.close()

    async def close(self):
        self._stop = True
        try:
            if self._ws:
                await self._ws.close()
        except:
            pass
        try:
            if self.listen_key:
                # Futures 文档未提供删除 listenKey 接口，现货有 delete。这里不做删除。
                pass
        except Exception as e:
            print(f"[CLOSE] 清理异常: {e}")
        await self._http.aclose()
        print("[INFO] 资源已清理")

    # ----------- 示例：签名下单 -----------
    async def place_order(
        self,
        symbol: str,
        side: str,
        order_type: str,
        quantity: str,
        price: Optional[str] = None,
        time_in_force: Optional[str] = "GTC",
        reduce_only: Optional[bool] = None,
        position_side: Optional[str] = None,  # BOTH/LONG/SHORT（双开模式/单向模式）
    ):
        """
        下一个普通限价或市价单示例（签名）。
        必需字段:
          symbol, side=BUY/SELL, type=LIMIT/MARKET, quantity
        限价单需 price & timeInForce
        """
        params = {
            "symbol": symbol.upper(),
            "side": side.upper(),
            "type": order_type.upper(),
            "quantity": quantity,
            "timestamp": epoch_ms(),
            "recvWindow": self.recv_window,
        }
        if order_type.upper() == "LIMIT":
            if not price:
                raise ValueError("限价单必须提供 price")
            params["price"] = price
            params["timeInForce"] = time_in_force

        if reduce_only is not None:
            params["reduceOnly"] = "true" if reduce_only else "false"
        if position_side:
            params["positionSide"] = position_side

        # 生成签名
        signature = sign_params(params, API_SECRET)
        params["signature"] = signature

        try:
            r = await self._http.post(
                f"{self.rest_base}{ORDER_ENDPOINT}",
                params=params  # futures 下单使用 query string
            )
            data = r.json()
            if r.status_code == 200:
                print("[ORDER PLACED]", json.dumps(data, ensure_ascii=False))
            else:
                print(f"[ORDER ERROR] {r.status_code} {data}")
        except Exception as e:
            print(f"[ORDER EXCEPTION] {e}")

# ----------- 优雅退出 -----------
def install_signal_handlers(loop, stream_obj: BinanceFuturesUserStream):
    def _handler(sig, frame):
        print(f"[SIGNAL] 收到 {sig}, 准备退出...")
        loop.create_task(stream_obj.close())
        for task in asyncio.all_tasks(loop):
            task.cancel()
    signal.signal(signal.SIGINT, _handler)
    signal.signal(signal.SIGTERM, _handler)

# ----------- 主入口 -----------
async def main():
    stream = BinanceFuturesUserStream()
    loop = asyncio.get_running_loop()
    install_signal_handlers(loop, stream)

    # 并行启动：监听用户数据流
    # 如需在启动后测试下单，你可以延迟几秒后调用 place_order
    async def delayed_order():
        await asyncio.sleep(5)
        # 示例：限价卖单
        # 注意：symbol 必须与账户可交易，否则会报错
        # 请在真实环境中根据实际合约标的与价格调整
        # await stream.place_order(
        #     symbol="BTCUSDT",
        #     side="SELL",
        #     order_type="LIMIT",
        #     quantity="0.001",
        #     price="80000",  # 随便示例价
        #     time_in_force="GTC",
        #     reduce_only=False
        # )
        # 'linear',symbol,'sell','market',buy_order_qty,sell_exchange,'open'
        BinanceExchangeHTTP.place_limit_order(category='linear',
                                symbol='SUSHIUSDT',
                                side='BUY',
                                orderType='LIMIT',
                                qty=10,
                                price=0.8)

    # 如果不需要自动下单测试，可以注释掉此任务
    # order_task = asyncio.create_task(delayed_order())

    await stream.run()
    # await order_task

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        pass
