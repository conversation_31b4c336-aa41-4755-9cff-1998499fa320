from typing import Any, Dict, Union
from shared_imports import asyncio, Config,datetime,redis,json
import pandas as pd
## 状态管理类
class TradingState:

    def __init__(self):
        self.shared_data = {}
        self.lock = asyncio.Lock()
        self.symbols = []
        self.okx_symbols = []
        self.redis_client = redis.Redis(host = 'localhost', port = 6379, db=0, decode_responses=True)
        self.okx_symbols = [symbol.replace('USDT','-USDT').replace('USDT','USDT-SWAP') for symbol in self.symbols]

    def init_symbols(self):
        # exchanges = ['Okx', 'Bybit']

        load_data = pd.read_csv('./common_symbols.csv')
        self.symbols = load_data['symbols']

        self.okx_symbols = [symbol.replace('USDT', '-USDT-SWAP') for symbol in self.symbols]
        

        
        exchanges = ['Binance', 'Okx', 'Bitget', 'Bybit']

        self.shared_data = {
            symbol: {exchange: {"bid": None, "ask": None              
            } for exchange in exchanges}for symbol in self.symbols
        }
    
    # def get_common_symbols(self):
    #     exchanges = ['Okx', 'Bybit']
    #     all_data = {}
    #     for exchange in exchanges:
    #         all_data[exchange] = {}

        
state = TradingState()
state.init_symbols()
# print(state.symbols)

# print('....')

# print(state.okx_symbols)
res = 'NIL-USDT-SWAP' in state.okx_symbols
print(res)