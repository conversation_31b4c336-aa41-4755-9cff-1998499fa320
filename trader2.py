from abc import ABC, abstractmethod
import json
from time import sleep
import time
from typing import Dict
import uuid
import redis
from trading_state import TradingState
from config_copy import Config
from api_keys import EXCHANGE_API_KEYS
from logging_setup import logging, setup_loggers
from exchange import Bitgetv1HTTP,BitgetV2HTTP,OkxExchangeHTTP,BybitExchangeHTTP,BaseExchangeHTTP,BinanceExchangeHTTP
from dataclasses import dataclass
import math
import sys
import json

setup_loggers()
decision_logger = logging.getLogger('decision_logger')
history_logger = logging.getLogger('history_logger')

class ExchangeAccount:
    """
    管理单个交易所账户的USDT本位财务状态。
    所有价值单位均为USDT。
    """
    def __init__(self, name: str, initial_usdt_capital: float = 0.0):
        self.name = name
        self.initial_usdt_capital = initial_usdt_capital
        
        # 这些值将由API定期更新。初始状态下，总值和可用余额等于初始资本。
        self.account_usdt_value: float = initial_usdt_capital
        self.available_usdt_balance: float = initial_usdt_capital

    def update_balance(self, account_usdt_value: float, available_usdt_balance: float):
        """
        使用从交易所API获取的最新USDT余额数据来更新账户状态。
        """
        self.account_usdt_value = account_usdt_value
        self.available_usdt_balance = available_usdt_balance

    @property
    def pnl_usdt(self) -> float:
        """
        实时计算此交易所的USDT盈亏。
        计算公式: 当前账户USDT总值 - 初始USDT本金
        """
        return self.account_usdt_value - self.initial_usdt_capital

    @property
    def used_margin_usdt(self) -> float:
        """
        实时计算当前已使用的USDT保证金。
        计算公式: 账户USDT总值 - 可用USDT余额
        """
        return self.account_usdt_value - self.available_usdt_balance
    
class PortfolioManager:
    """
    聚合所有ExchangeAccount的数据，提供整个投资组合的USDT本位视图。
    """
    def __init__(self, initial_usdt_capitals: dict[str, float]):
        self.accounts: dict[str, ExchangeAccount] = {
            name: ExchangeAccount(name, capital)
            for name, capital in initial_usdt_capitals.items()
        }

    def update_exchange_balance(self, exchange_name: str, account_usdt_value: float, available_usdt_balance: float):
        """
        查找对应的交易所账户并更新其USDT余额。
        这是更新投资组合状态的唯一入口。
        """
        if exchange_name in self.accounts:
            self.accounts[exchange_name].update_balance(account_usdt_value, available_usdt_balance)
        else:
            print(f"警告: 尝试更新一个不存在的交易所账户 '{exchange_name}'")

    @property
    def total_initial_usdt_capital(self) -> float:
        """计算所有账户的总初始USDT本金。"""
        return sum(account.initial_usdt_capital for account in self.accounts.values())

    @property
    def total_usdt_balance(self) -> float:
        """计算所有账户当前的USDT总值。"""
        return sum(account.account_usdt_value for account in self.accounts.values())

    @property
    def total_pnl_usdt(self) -> float:
        """计算整个投资组合的总USDT盈亏。"""
        return self.total_usdt_balance - self.total_initial_usdt_capital

    @property
    def roi_percentage(self) -> float:
        """计算整体的投资回报率 (ROI) 百分比。"""
        initial_capital = self.total_initial_usdt_capital
        if initial_capital == 0:
            return 0.0
        return (self.total_pnl_usdt / initial_capital) * 100

    def to_dict(self) -> dict:
        """
        将整个投资组合的状态序列化为字典，以便持久化或用于API响应。
        键名清晰地反映了USDT单位。
        """
        return {
            "total_initial_usdt_capital": self.total_initial_usdt_capital,
            "total_usdt_balance": self.total_usdt_balance,
            "total_pnl_usdt": self.total_pnl_usdt,
            "roi_percentage": self.roi_percentage,
            "exchange_balances": {
                name: {
                    "initial_usdt_capital": acc.initial_usdt_capital,
                    "account_usdt_value": acc.account_usdt_value,
                    "available_usdt_balance": acc.available_usdt_balance,
                    "used_margin_usdt": acc.used_margin_usdt,
                    "pnl_usdt": acc.pnl_usdt,
                }
                for name, acc in self.accounts.items()
            }
        }


class TradeHistoryManager:
    def __init__(self,redis_client):
        self.redis_client = redis_client
        self.trade_history = {}
        self._load_from_redis()

    def _load_from_redis(self):
        decision_logger.info("从redis加载历史数据")
        all_trade_ids = self.redis_client.zrange("trades:by_time", 0, -1)

        for trade_id in all_trade_ids:
            trade_data = self.redis_client.hgetall(f'trade:detail:{trade_id}')

            open_record = json.loads(trade_data.get('open_record', '{}'))
            close_record = json.loads(trade_data.get('close_record', '{}')) if trade_data.get('close_record') else None

            if open_record:
                self.trade_history[trade_id] = {
                    "open": open_record,
                    "close": close_record
                }
        decision_logger.info(f"加载完成，共 {len(self.trade_history)} 笔交易")

class OrderSignal:
    def __init__(self,category:str,
                 symbol: str,
                 side: str,  # buy/sell
                orderType: str,  # market/limit, not supporting limit yet TODO
                quantity: float,
                exchange: str,
                action: str
                ):
        self.category = category
        self.symbol = symbol
        self.orderType = orderType
        self.side = side
        self.quantity = quantity
        self.exchange = exchange
        self.id = uuid.uuid4()
        self.timestamp = time.time()
        self.action = action
   

class TraderState:

    def __init__(self):
        self.active_trades = []
        self.trade_history = {}
        self.exchanges = {'Bybit': None, 'Okx': None, 'Bitget': None, 'Binance': None}
        self.test_exchanges = ['Bybit','Okx','Bitget','Binance']
        self.redis_client = redis.Redis(host = 'localhost', port = 6379, db=0, decode_responses=True)
        self.trade_history_manager = TradeHistoryManager(self.redis_client)
        self.portfolio_manager = PortfolioManager(Config.INITIAL_USDT_CAPITALS)

    def generate_and_store_from_history(self):
        """
        从 self.trade_history 生成 API 所需的格式，并存入 Redis。
        这是一个 O(M) 操作, M是独立交易的数量。
        """
        trade_pairs = []
        summary = {
            'total_trades': len(self.trade_history), # 总交易数就是 self.trade_history 的长度
            'open_trades': 0,
            'profitable_trades': 0,
            'total_pnl': 0.0
        }

        # 遍历 self.trade_history 中的每一笔配对好的交易
        for trade_id, trade_data in self.trade_history.items():
            open_record = trade_data['open']
            close_record = trade_data['close']

            open_buy_detail = open_record['real']['buy_record']
            open_sell_detail = open_record['real']['sell_record']

            trade_pair = {
                'symbol': open_buy_detail['symbol'],
                'buy_exchange': open_buy_detail['exchange'],
                'sell_exchange': open_sell_detail['exchange'],
                'open_buy_price': open_buy_detail['avg_price'],
                'open_sell_price': open_sell_detail['avg_price'],
                'open_time': open_buy_detail['our_time'],
                'status': 'OPEN',
                'pnl': 0.0,
                'close_time': None,
                'close_buy_price': None,
                'close_sell_price': None
            }

            if close_record:
                close_buy_detail = close_record['real']['buy_record']
                close_sell_detail = close_record['real']['sell_record']
                
                # TODO: 替换为真实的 PnL 逻辑
                pnl = close_record.get('virtual', {}).get('pnl', 0.0) 

                trade_pair.update({
                    'status': 'CLOSED',
                    'close_time': close_buy_detail['our_time'],
                    'close_buy_price': close_buy_detail['avg_price'],
                    'close_sell_price': close_sell_detail['avg_price'],
                    'pnl': pnl
                })
                
                summary['total_pnl'] += pnl
                if pnl >= 0:
                    summary['profitable_trades'] += 1
            else:
                summary['open_trades'] += 1
                # TODO: 更新实时 PnL
                # trade_pair['pnl'] = open_record.get('virtual', {}).get('current_pnl', 0.0)

            trade_pairs.append(trade_pair)

        final_data_to_store = {
            'trade_pairs': trade_pairs,
            'summary': summary,
            'last_updated': time.time()
        }

        self.redis_client.set("trading:formatted_history", json.dumps(final_data_to_store))
        print(f"已从 self.trade_history 生成并存储最新交易历史到 Redis。")

    def update_and_save(self, full_trade_record):
        """
        增量更新Redis和内存（更健壮的版本）。
        """
        # Use .get() to safely access keys that might not exist.
        trade_id = full_trade_record.get('trade_id')
        action = full_trade_record.get('action')

        # ✅ **Crucial Check 1: Ensure trade_id and action exist**
        if not trade_id or not action:
            # Using logger instead of print
            decision_logger.error(f"记录缺少 'trade_id' 或 'action'。跳过: {full_trade_record}")
            return

        # 1. 更新内存
        self.update_trade_history(full_trade_record)

        # 2. 增量更新Redis
        pipe = self.redis_client.pipeline()
        
        trade_detail_key = f"trade:detail:{trade_id}"
        summary_key = "trading:summary"

        if action == 'open':
            # Safely get the open_time using nested .get() calls
            open_time = full_trade_record.get('real', {}).get('buy_record', {}).get('our_time')
            
            # ✅ **Crucial Check 2: Ensure the timestamp for ZADD exists**
            if open_time is None:
                decision_logger.error(f"开仓记录 {trade_id} 缺少 'our_time'。无法更新Redis。")
                return

            pipe.hset(trade_detail_key, "open_record", json.dumps(full_trade_record))
            pipe.hset(trade_detail_key, "status", "OPEN")
            pipe.zadd("trades:by_time", {trade_id: open_time})
            pipe.hincrby(summary_key, "total_trades", 1)
            pipe.hincrby(summary_key, "open_trades", 1)

        elif action == 'close':
            close_time = full_trade_record.get('real', {}).get('sell_record', {}).get('our_time')
            pnl = full_trade_record.get('virtual', {}).get('pnl', 0.0) # This part is already safe

            # ✅ **Crucial Check 3: Ensure the timestamp for ZADD exists**
            if close_time is None:
                decision_logger.error(f"平仓记录 {trade_id} 缺少 'our_time'。无法更新Redis。")
                return

            pipe.hset(trade_detail_key, "close_record", json.dumps(full_trade_record))
            pipe.hset(trade_detail_key, "status", "CLOSED")
            pipe.hset(trade_detail_key, "pnl", pnl)
            pipe.zadd("trades:by_time", {trade_id: close_time})
            pipe.hincrby(summary_key, "open_trades", -1)
            pipe.hincrbyfloat(summary_key, "total_pnl", pnl)
            if pnl >= 0:
                pipe.hincrby(summary_key, "profitable_trades", 1)

        # 执行所有命令
        pipe.execute()
        # Using logger instead of print
        decision_logger.info(f"增加Redis中的交易: {trade_id}")

    def update_trade_history(self, full_trade_record):
        """
        高效地更新内存中的 trade_self.trade_history。
        这是一个 O(1) 操作。
        """
        trade_id = full_trade_record['trade_id']
        action = full_trade_record['action']

        if action == 'open':
            # 如果是开仓，创建一个新条目
            if trade_id in self.trade_history_manager.trade_history:
                print(f"警告: 收到重复的开仓ID {trade_id}，将覆盖。")
            self.trade_history_manager.trade_history[trade_id] = {
                "open": full_trade_record,
                "close": None
            }
            print(f"新交易已开仓: {trade_id}")

        elif action == 'close':
            # 如果是平仓，更新现有条目
            if trade_id in self.trade_history_manager.trade_history:
                self.trade_history_manager.trade_history[trade_id]['close'] = full_trade_record
                print(f"交易已平仓: {trade_id}")
            else:
                # 异常情况：收到了一个没有开仓记录的平仓ID
                print(f"错误: 收到未知的平仓ID {trade_id}，无法匹配。")

    def calculate_trade_amount(self, symbol, buy_exchange_api:BaseExchangeHTTP, buy_price, sell_exchange_api:BaseExchangeHTTP,sell_price):
        """计算交易数量 - 带缓存命中检测"""
        decision_logger.info(f"🔍 开始计算交易数量: {symbol}, 买方:{buy_exchange_api.exchange_name}, 卖方:{sell_exchange_api.exchange_name}")

        # 获取余额信息 - 检测缓存命中
        decision_logger.info(f"📊 获取 {buy_exchange_api.exchange_name} 余额信息...")
        buy_has_cache = buy_exchange_api._balance_cache is not None
        decision_logger.info(f"   余额缓存状态: {'✅ 有缓存，将使用缓存' if buy_has_cache else '❌ 无缓存，将调用API'}")

        _, buy_available = buy_exchange_api.get_balance_info(force_update=False)
        if buy_available == '' or buy_available is None:
            decision_logger.error(f'获取 {buy_exchange_api.exchange_name} 余额失败')
            return 0
        decision_logger.info(f"   ✅ {buy_exchange_api.exchange_name} 可用余额: {buy_available}")

        decision_logger.info(f"📊 获取 {sell_exchange_api.exchange_name} 余额信息...")
        sell_has_cache = sell_exchange_api._balance_cache is not None
        decision_logger.info(f"   余额缓存状态: {'✅ 有缓存，将使用缓存' if sell_has_cache else '❌ 无缓存，将调用API'}")

        _, sell_available = sell_exchange_api.get_balance_info(force_update=False)
        if sell_available == '' or sell_available is None:
            decision_logger.error(f'获取 {sell_exchange_api.exchange_name} 余额失败')
            return 0
        decision_logger.info(f"   ✅ {sell_exchange_api.exchange_name} 可用余额: {sell_available}")
        
        buy_available = float(buy_available)
        sell_available = float(sell_available)
        usdt_available = min(buy_available,sell_available)
        buy_available = float(usdt_available) * 0.85
        buy_amount_draft = buy_available / buy_price
        sell_available = float(usdt_available) * 0.85
        sell_amount_draft = sell_available / sell_price
        order_amount = min(buy_amount_draft,sell_amount_draft)

        # 获取符号信息 - 检测缓存命中
        decision_logger.info(f"📋 获取 {buy_exchange_api.exchange_name} {symbol} 符号信息...")
        buy_symbol_loaded = buy_exchange_api._symbol_info_loaded
        buy_symbol_cached = symbol in buy_exchange_api._symbol_info_cache if buy_symbol_loaded else False
        decision_logger.info(f"   符号缓存状态: {'✅ 已加载' if buy_symbol_loaded else '❌ 未加载'}, {'✅ 有缓存' if buy_symbol_cached else '❌ 无缓存'}")

        buy_ct_val = None
        if buy_exchange_api.exchange_name == 'Okx':
            buy_lot,max_buy_q,buy_ct_val = buy_exchange_api.get_symbol_info(symbol)
        else:
            buy_lot,max_buy_q,_ = buy_exchange_api.get_symbol_info(symbol)
        decision_logger.info(f"   ✅ {buy_exchange_api.exchange_name} {symbol}: min_qty={buy_lot}, max_qty={max_buy_q}, ct_val={buy_ct_val}")
        if buy_lot == None:
            buy_lot = 1.0

        decision_logger.info(f"📋 获取 {sell_exchange_api.exchange_name} {symbol} 符号信息...")
        sell_symbol_loaded = sell_exchange_api._symbol_info_loaded
        sell_symbol_cached = symbol in sell_exchange_api._symbol_info_cache if sell_symbol_loaded else False
        decision_logger.info(f"   符号缓存状态: {'✅ 已加载' if sell_symbol_loaded else '❌ 未加载'}, {'✅ 有缓存' if sell_symbol_cached else '❌ 无缓存'}")

        sell_ct_val = None
        if sell_exchange_api.exchange_name == 'Okx':
            sell_lot,max_sell_q,sell_ct_val = sell_exchange_api.get_symbol_info(symbol)
        else:
            sell_lot,max_sell_q,_ = sell_exchange_api.get_symbol_info(symbol)
        decision_logger.info(f"   ✅ {sell_exchange_api.exchange_name} {symbol}: min_qty={sell_lot}, max_qty={max_sell_q}, ct_val={sell_ct_val}")
        if sell_lot == None:
            sell_lot = 1.0


        # our_lot_size = buy_lot, sell_lot 的最小公倍数    
        our_lot_size = buy_exchange_api.lcm(buy_lot,sell_lot)
        if our_lot_size == 0:
            return 0
        order_amount = math.floor(order_amount / our_lot_size) * our_lot_size

        # 检查最大交易量限制，避免None值比较
        if max_buy_q is not None:
            order_amount = min(order_amount, max_buy_q)
        if max_sell_q is not None:
            order_amount = min(order_amount, max_sell_q)

        # 如果任一交易所的最大交易量为None，记录警告
        if max_buy_q is None or max_sell_q is None:
            decision_logger.warning(f"交易量限制查询失败: max_buy_q={max_buy_q}, max_sell_q={max_sell_q}")

        # OKX 合约面值修正：当包含 OKX 时，需要考虑张数取整对实际建仓数量的影响
        if buy_exchange_api.exchange_name == 'Okx' or sell_exchange_api.exchange_name == 'Okx':
            if buy_exchange_api.exchange_name == 'Okx' and buy_ct_val is not None:
                # 买方是 OKX，计算实际可交易的张数
                okx_contracts = int(order_amount / buy_ct_val)  # 向下取整到整张
                actual_order_amount = okx_contracts * buy_ct_val  # 实际建仓数量
                decision_logger.info(f"OKX买方张数修正: {order_amount:.6f} 币 -> {okx_contracts} 张 -> {actual_order_amount:.6f} 币")
                order_amount = actual_order_amount

            elif sell_exchange_api.exchange_name == 'Okx' and sell_ct_val is not None:
                # 卖方是 OKX，计算实际可交易的张数
                okx_contracts = int(order_amount / sell_ct_val)  # 向下取整到整张
                actual_order_amount = okx_contracts * sell_ct_val  # 实际建仓数量
                decision_logger.info(f"OKX卖方张数修正: {order_amount:.6f} 币 -> {okx_contracts} 张 -> {actual_order_amount:.6f} 币")
                order_amount = actual_order_amount

            # 如果修正后的数量为0或过小，返回0
            if order_amount <= 0:
                decision_logger.warning("OKX张数修正后交易量为0，跳过此次套利")
                return 0

        # now put into orderbook and get the number of trades within slippage
        # TODO

        # 缓存命中总结
        decision_logger.info(f"💡 缓存命中总结:")
        decision_logger.info(f"   余额缓存: {buy_exchange_api.exchange_name}={'✅命中' if buy_has_cache else '❌未命中'}, {sell_exchange_api.exchange_name}={'✅命中' if sell_has_cache else '❌未命中'}")
        decision_logger.info(f"   符号缓存: {buy_exchange_api.exchange_name}={'✅命中' if buy_symbol_cached else '❌未命中'}, {sell_exchange_api.exchange_name}={'✅命中' if sell_symbol_cached else '❌未命中'}")
        decision_logger.info(f"🎯 最终交易数量: {order_amount}")

        return order_amount
        # return 100
    
    def get_current_position_size(self):
        return len(self.active_trades)

    def _handle_partial_open_failure(self, successful_exchange, successful_record, failure_reason):
        """处理建仓部分失败的情况"""
        decision_logger.error(f"🚨 建仓部分失败处理: {failure_reason}")
        decision_logger.error(f"成功的交易所: {successful_exchange}")
        decision_logger.error(f"成功的订单记录: {successful_record}")

        # 尝试自动处理
        if successful_record and 'orderid' in successful_record:
            orderid = successful_record['orderid']
            decision_logger.error(f"尝试撤销订单: {orderid}")

            # TODO: 实现自动撤销
            cancel_success = self._attempt_cancel_order(successful_exchange, orderid)

            if cancel_success:
                decision_logger.info(f"✅ 成功撤销订单 {orderid}")
            else:
                decision_logger.error(f"❌ 撤销订单失败 {orderid}")
                decision_logger.error("⚠️ 存在单边仓位风险，需要手动处理！")

                # 记录风险仓位
                self._record_risk_position(successful_exchange, successful_record, failure_reason)
        else:
            decision_logger.error("⚠️ 无法获取订单ID，需要手动检查和处理！")

    def _attempt_cancel_order(self, exchange_name, orderid):
        """尝试撤销订单"""
        try:
            exchange_api = self.exchanges[exchange_name]

            # TODO: 实现各交易所的撤销订单API
            # if hasattr(exchange_api, 'cancel_order'):
            #     return exchange_api.cancel_order(orderid)

            decision_logger.warning(f"撤销订单功能尚未实现: {exchange_name}")
            return False

        except Exception as e:
            decision_logger.error(f"撤销订单异常: {e}")
            return False

    def _record_risk_position(self, exchange_name, order_record, risk_reason):
        """记录风险仓位"""
        risk_position = {
            'timestamp': time.time(),
            'exchange': exchange_name,
            'order_record': order_record,
            'risk_reason': risk_reason,
            'status': 'NEEDS_MANUAL_HANDLING'
        }

        # TODO: 保存到风险仓位数据库
        decision_logger.error(f"🚨 记录风险仓位: {risk_position}")

        # TODO: 发送告警通知
        # self._send_risk_alert(risk_position)

    def refresh_all_balances(self):
        """强制刷新所有交易所的余额缓存"""
        decision_logger.info("强制刷新所有交易所余额...")

        for exchange_name, exchange_api in self.exchanges.items():
            if exchange_api is not None:
                decision_logger.info(f"刷新 {exchange_name} 余额...")
                balance = exchange_api.get_balance_info(force_update=True)
                if balance and balance[0] is not None:
                    decision_logger.info(f"{exchange_name} 余额: 总资产={balance[0]}, 可用={balance[1]}")
                else:
                    decision_logger.error(f"{exchange_name} 余额获取失败")

        decision_logger.info("余额刷新完成")

    def load_all_symbol_info(self):
        """加载所有交易所的符号信息到缓存"""
        decision_logger.info("开始加载所有交易所的符号信息...")

        for exchange_name, exchange_api in self.exchanges.items():
            if exchange_api is not None:
                decision_logger.info(f"加载 {exchange_name} 符号信息...")
                try:
                    exchange_api.load_all_symbol_info()
                    symbol_count = len(exchange_api._symbol_info_cache)
                    decision_logger.info(f"{exchange_name} 成功加载 {symbol_count} 个符号信息")
                except Exception as e:
                    decision_logger.error(f"{exchange_name} 符号信息加载失败: {e}")

        decision_logger.info("符号信息加载完成")

    def _handle_partial_close_failure(self, successful_exchange, failed_exchange,
                                    successful_record, failed_record, failure_reason):
        """处理平仓部分失败的情况"""
        decision_logger.error(f"平仓部分失败处理: {failure_reason}")
        decision_logger.error(f"成功的交易所: {successful_exchange}")
        decision_logger.error(f"失败的交易所: {failed_exchange}")

        # TODO: 实现风险处理逻辑
        # 1. 记录部分平仓状态
        # 2. 尝试重新平仓失败的部分
        # 3. 如果重试失败，标记为风险仓位
        # 4. 发送告警通知

        decision_logger.error("⚠️ 部分平仓失败，存在风险敞口，需要手动处理！")
    
    def open_position(self,symbol, buy_exchange, sell_exchange,data) -> bool:
        """open positions, add to trade history and active trades return True if success, False if fail"""
        # TODO
        buy_price = data[symbol][buy_exchange]['ask']
        sell_price = data[symbol][sell_exchange]['bid']
        succeed = False
        buy_exchange_api = self.exchanges[buy_exchange]
        sell_exchange_api = self.exchanges[sell_exchange]
        trade_amount = self.calculate_trade_amount(symbol,buy_exchange_api,buy_price,sell_exchange_api,sell_price)

        if trade_amount == 0:
            return False
        buy_amount = trade_amount
        sell_amount = trade_amount

        # connect to real api
        # ideally 
        buy_order_signal = OrderSignal('linear',symbol,'buy','market',trade_amount,buy_exchange,'open')
        buy_succeed, buy_record = self.unified_place_order(buy_order_signal)
        # buy_record = buy_exchange.long(opportunity)
        # TODO parse buy_res or fix it in unified place order or exchange.place_order same for sell

        sell_order_signal = OrderSignal('linear',symbol,'sell','market',trade_amount,sell_exchange,'open')
        sell_succeed, sell_record = self.unified_place_order(sell_order_signal)
        # sell_record = sell_exchange.short(opportunity)
        # TODO

        # 处理建仓失败情况
        if not buy_succeed:
            decision_logger.error(f'买单@{buy_exchange} 失败. info: {buy_record}')

        if not sell_succeed:
            decision_logger.error(f'卖单@{sell_exchange} 失败. info: {sell_record}')

        # 检查是否需要处理部分成功的情况
        if buy_succeed and not sell_succeed:
            decision_logger.error(f"建仓部分失败：买单成功但卖单失败，需要撤销买单")
            # TODO: 实现买单撤销逻辑
            self._handle_partial_open_failure(buy_exchange, buy_record, "sell_failed")
            return False

        elif not buy_succeed and sell_succeed:
            decision_logger.error(f"建仓部分失败：卖单成功但买单失败，需要撤销卖单")
            # TODO: 实现卖单撤销逻辑
            self._handle_partial_open_failure(sell_exchange, sell_record, "buy_failed")
            return False

        elif not buy_succeed and not sell_succeed:
            decision_logger.error(f"建仓完全失败：买单和卖单都失败")
            return False

        # 只有在两个订单都成功时才继续
        if buy_succeed and sell_succeed:
            # 解析订单结果获取基本信息
            buy_avg_price, buy_filled_qty, buy_order_id, buy_status = self.parse_order_result(buy_exchange, buy_record)
            sell_avg_price, sell_filled_qty, sell_order_id, sell_status = self.parse_order_result(sell_exchange, sell_record)

            # 对于需要查询详情的交易所（OKX, Bitget），获取真实成交信息
            if buy_status == 'PENDING':
                decision_logger.info(f"查询{buy_exchange}买单详情，订单ID: {buy_order_id}")
                import time
                time.sleep(1)  # 等待订单处理
                buy_avg_price, buy_filled_qty, buy_status = self.get_order_details(
                    buy_exchange, buy_exchange_api, buy_order_id, symbol)

            if sell_status == 'PENDING':
                decision_logger.info(f"查询{sell_exchange}卖单详情，订单ID: {sell_order_id}")
                import time
                time.sleep(1)  # 等待订单处理
                sell_avg_price, sell_filled_qty, sell_status = self.get_order_details(
                    sell_exchange, sell_exchange_api, sell_order_id, symbol)

            # 验证成交信息
            if buy_avg_price and sell_avg_price and buy_filled_qty and sell_filled_qty:
                decision_logger.info(f"真实建仓信息:")
                decision_logger.info(f"  买入: {buy_exchange} {buy_filled_qty:.6f} @ {buy_avg_price:.6f}")
                decision_logger.info(f"  卖出: {sell_exchange} {sell_filled_qty:.6f} @ {sell_avg_price:.6f}")
                decision_logger.info(f"  理论价差: {sell_price - buy_price:.6f}")
                decision_logger.info(f"  实际价差: {sell_avg_price - buy_avg_price:.6f}")

                # 使用真实成交信息
                actual_buy_amount = buy_filled_qty
                actual_sell_amount = sell_filled_qty
                actual_buy_price = buy_avg_price
                actual_sell_price = sell_avg_price
            else:
                decision_logger.warning("无法获取完整的成交信息，使用理论价格")
                actual_buy_amount = trade_amount
                actual_sell_amount = trade_amount
                actual_buy_price = buy_price
                actual_sell_price = sell_price

            # freeze capitals
            # update balances
            # TODO

            virtual_trade_record = {'symbol':symbol,
                            'buy_exchange':buy_exchange,
                            'buy_price': buy_price,  # 使用计算成交价
                            'buy_amount': buy_amount,  # 使用计算成交量
                            'sell_price': sell_price,  # 使用计算成交价
                            'sell_exchange':sell_exchange,
                            'sell_amount': sell_amount,  # 使用计算成交量
                            'time_stamp':time.time()
                            }

            full_trade_record = {'action':'open',
                                'real':{
                                        'buy_record':buy_record,
                                        'sell_record':sell_record,
                                        'buy_order_id': buy_order_id,
                                        'sell_order_id': sell_order_id,
                                        'buy_avg_price': actual_buy_price,
                                        'sell_avg_price': actual_sell_price,
                                        'buy_filled_qty': actual_buy_amount,
                                        'sell_filled_qty': actual_sell_amount},
                                'virtual': virtual_trade_record,
                                'time_stamp': time.time()
            }
            self.update_and_save(full_trade_record)

            self.active_trades.append(full_trade_record)
            return True
        return False
    
    def open_position_test_Aug21(self, symbol, buy_exchange, sell_exchange, data) -> bool:
        """open positions, add to trade history and active trades return True if success, False if fail"""
        # TODO
        buy_price = data[symbol][buy_exchange]['ask']
        sell_price = data[symbol][sell_exchange]['bid']
        succeed = False
        buy_exchange_api = self.exchanges[buy_exchange]
        sell_exchange_api = self.exchanges[sell_exchange]
        trade_amount = self.calculate_trade_amount(symbol,buy_exchange_api,buy_price,sell_exchange_api,sell_price)
        decision_logger.info(f'货币: {symbol}, {buy_price}@{buy_exchange}, {sell_price}@{sell_exchange})')
       
        decision_logger.info(f'计算得出总交易量(开/平): {trade_amount}')

        if trade_amount == 0:
            decision_logger.info(f'计算交易量为0，不交易')
            return False, {"trade_amount": 0}
        buy_amount = trade_amount
        sell_amount = trade_amount

        
        buy_record = {}
        sell_record = {}

        # connect to real api
        buy_order_signal = OrderSignal('linear',symbol,'buy','market',trade_amount,buy_exchange,'open')
        buy_succeed, buy_orderid = self.unified_place_order(buy_order_signal)

        sell_order_signal = OrderSignal('linear',symbol,'sell','market',trade_amount,sell_exchange,'open')
        sell_succeed, sell_orderid = self.unified_place_order(sell_order_signal)

        if buy_succeed and buy_orderid:
            buy_record = self.exchanges[buy_order_signal.exchange].get_order_detail(buy_orderid)
        else:
            decision_logger.error(f"{buy_order_signal.exchange} 下单失败或缺少订单ID")

        if sell_succeed and sell_orderid:
            sell_record = self.exchanges[sell_order_signal.exchange].get_order_detail(sell_orderid)
        else:
            decision_logger.error(f"{sell_order_signal.exchange} 下单失败或缺少订单ID")

        # 处理建仓失败情况
        if not buy_succeed:
            decision_logger.error(f'买单@{buy_exchange} 失败. info: {buy_record}')

        if not sell_succeed:
            decision_logger.error(f'卖单@{sell_exchange} 失败. info: {sell_record}')

        # 检查是否需要处理部分成功的情况
        if buy_succeed and not sell_succeed:
            decision_logger.error(f"建仓部分失败：买单成功但卖单失败，需要撤销买单")
            self._handle_partial_open_failure(buy_exchange, buy_record, "sell_failed")
            return False, {"buy_succeed": True, "sell_succeed": False, "action": "partial_failure_handled"}

        elif not buy_succeed and sell_succeed:
            decision_logger.error(f"建仓部分失败：卖单成功但买单失败，需要撤销卖单")
            self._handle_partial_open_failure(sell_exchange, sell_record, "buy_failed")
            return False, {"buy_succeed": False, "sell_succeed": True, "action": "partial_failure_handled"}

        elif not buy_succeed and not sell_succeed:
            decision_logger.error(f"建仓完全失败：买单和卖单都失败")
            return False, {"buy_succeed": False, "sell_succeed": False, "action": "complete_failure"}

        # 只有在两个订单都成功时才继续
        if buy_succeed and sell_succeed:
            actual_buy_amount = trade_amount

            # 安全地获取订单ID
            buy_orderid = None
            sell_orderid = None

            print(f'buy_record: {buy_record}')
            if buy_record and 'orderid' in buy_record:
                buy_orderid = buy_record['orderid']
            else:
                decision_logger.error(f"买单记录中没有orderid字段: {buy_record}")
                return False

            if sell_record and 'orderid' in sell_record:
                sell_orderid = sell_record['orderid']
            else:
                decision_logger.error(f"卖单记录中没有orderid字段: {sell_record}")
                return False

            # 获取订单详情
            buy_detail = buy_exchange_api.get_order_detail(buy_orderid)
            sell_detail = sell_exchange_api.get_order_detail(sell_orderid)

            # 检查订单详情是否获取成功
            if not buy_detail:
                decision_logger.error(f"无法获取买单详情，订单ID: {buy_orderid}")
                return False

            if not sell_detail:
                decision_logger.error(f"无法获取卖单详情，订单ID: {sell_orderid}")
                return False


            # 使用真实成交信息
            actual_buy_price = buy_detail.get('avg_price', buy_price)
            actual_sell_price = sell_detail.get('avg_price', sell_price)
            actual_buy_amount = buy_detail.get('exec_qty', buy_amount)
            actual_sell_amount = sell_detail.get('exec_qty', sell_amount)

            # 记录真实建仓信息
            decision_logger.info(f"真实建仓信息:")
            decision_logger.info(f"  买入: {buy_exchange} {actual_buy_amount:.6f} @ {actual_buy_price:.6f}")
            decision_logger.info(f"  卖出: {sell_exchange} {actual_sell_amount:.6f} @ {actual_sell_price:.6f}")
            decision_logger.info(f"  理论价差: {sell_price - buy_price:.6f}")
            decision_logger.info(f"  实际价差: {actual_sell_price - actual_buy_price:.6f}")

            # freeze capitals
            # update balances
            # TODO

            open_spread, open_spread_pct = calculate_opening_spread(buy_price, sell_price)
            target_threshold = float(open_spread_pct) * float(Config.MAGIC_THRESHOLD)
            virtual_trade_record = {
                'symbol':symbol,
                'buy_exchange':buy_exchange,
                'buy_price': buy_price,  # 使用计算成交价
                'buy_amount': buy_amount,  # 使用计算成交量
                'sell_price': sell_price,  # 使用计算成交价
                'sell_exchange':sell_exchange,
                'sell_amount': sell_amount,  # 使用计算成交量
                'target_threshold': target_threshold,
                'time_stamp':time.time()
            }

            full_trade_record = {
                'trade_id':str(uuid.uuid4()),  # 转换为字符串
                'action':'open',
                'real':{
                        'buy_record':buy_record,
                        'sell_record':sell_record},
                'virtual': virtual_trade_record,
                'time_stamp': time.time()
            }
            self.update_and_save(full_trade_record)


            self.active_trades.append(full_trade_record)
            history_log = json.dumps(full_trade_record,indent=4)
            history_logger.info(f'新纪录:\n{history_log}')

            # current_position
            virtual_rec = full_trade_record['virtual']
            actual_spread = virtual_rec['sell_price'] - virtual_rec['buy_price']
            actual_spread_pct = 2 * actual_spread / (virtual_rec['sell_price'] + virtual_rec['buy_price'])

            current_position = {
                                "symbol": virtual_rec['symbol'],
                                "best_buy_exchange": virtual_rec['buy_exchange'],
                                "best_buy_price": virtual_rec['buy_price'],
                                "best_sell_exchange": virtual_rec['sell_exchange'],
                                "best_sell_price": virtual_rec['sell_price'],
                                "open_spread_pct": actual_spread_pct,
                                "trade_time": time.time()
                                }
            # redis
            trader_state.redis_client.set("trading:current_position",json.dumps(current_position))

            # balance info update
            for exchange_name in [buy_exchange,sell_exchange]:
                account_usdt_value = Config.INITIAL_USDT_CAPITALS[exchange_name]
                _, available_usdt_balance = self.exchanges[exchange_name].get_balance_info()
                self.portfolio_manager.update_exchange_balance(
                    exchange_name=exchange_name,
                    account_usdt_value=account_usdt_value,
                    available_usdt_balance=available_usdt_balance
                )
    
            # 3. (可选但推荐) 更新后，立即将最新的整体状态持久化到Redis
            latest_state = self.portfolio_manager.to_dict()
            self.redis_client.set("trading:balance", json.dumps(latest_state))
            return True, full_trade_record
        
        else:
            decision_logger.info('开仓失败')
            hist = {'buy_succeed': buy_succeed,
                        'sell_succeed': sell_succeed}
            history_log = json.dumps(hist)
            history_logger.info(f'新纪录:\n{history_log}')

            # balance info update
            for exchange_name in [buy_exchange,sell_exchange]:
                account_usdt_value = Config.INITIAL_USDT_CAPITALS[exchange_name]
                _, available_usdt_balance = self.exchanges[exchange_name].get_balance_info()
                self.portfolio_manager.update_exchange_balance(
                    exchange_name=exchange_name,
                    account_usdt_value=account_usdt_value,
                    available_usdt_balance=available_usdt_balance
                )
    
            # 3. (可选但推荐) 更新后，立即将最新的整体状态持久化到Redis
            latest_state = self.portfolio_manager.to_dict()
            self.redis_client.set("trading:balance", json.dumps(latest_state))
        
            return False, hist
    
    
    def close_position(self, symbol, prev_buy_exchange, prev_sell_exchange):
        """
        prev_buy_exchange: 之前购入symbol (做多) 的交易所
        prev_sell_exchange: 之前出售symbol (做空) 的交易所

        TODO
        也许active trades里面存的类型为 Possition，然后open/close position直接操作于position上会更容易维护？
        """
        succeed = False

        #TODO
        trade_amount = self.calculate_trade_amount(symbol,self.exchanges[prev_buy_exchange],self.exchanges[prev_sell_exchange])
        if trade_amount == 0:
            return False
        buy_amount = trade_amount
        sell_amount = trade_amount
        # ideally
        #TODO
        close_buy_order_signal = OrderSignal('linear',symbol,'sell','market',trade_amount,prev_buy_exchange,'close')
        close_buy_succeed, close_buy_record = self.unified_place_order(close_buy_order_signal)

        close_sell_order_signal = OrderSignal('linear',symbol,'buy','market',trade_amount,prev_sell_exchange,'close')
        close_sell_succeed, close_sell_record = self.unified_place_order(close_sell_order_signal)

        # 处理平仓失败情况
        if not close_buy_succeed:
            decision_logger.error(f'平仓买单@{prev_buy_exchange} 失败. info: {close_buy_record}')

        if not close_sell_succeed:
            decision_logger.error(f'平仓卖单@{prev_sell_exchange} 失败. info: {close_sell_record}')

        # 检查平仓部分失败情况
        if close_buy_succeed and not close_sell_succeed:
            decision_logger.error(f"平仓部分失败：{prev_buy_exchange}平仓成功但{prev_sell_exchange}平仓失败")
            self._handle_partial_close_failure(prev_buy_exchange, prev_sell_exchange,
                                             close_buy_record, close_sell_record, "sell_close_failed")
            return False

        elif not close_buy_succeed and close_sell_succeed:
            decision_logger.error(f"平仓部分失败：{prev_sell_exchange}平仓成功但{prev_buy_exchange}平仓失败")
            self._handle_partial_close_failure(prev_sell_exchange, prev_buy_exchange,
                                             close_sell_record, close_buy_record, "buy_close_failed")
            return False

        elif not close_buy_succeed and not close_sell_succeed:
            decision_logger.error(f"平仓完全失败：两个交易所都平仓失败")
            return False

        # 只有在两个平仓订单都成功时才继续
        if close_buy_succeed and close_sell_succeed:
            # free previous frozen capitals
            virtual_trade_record = {'symbol': symbol,
                                    'prev_buy_exchange': prev_buy_exchange,
                                    'prev_sell_exchange': prev_sell_exchange,
                                    'buy_exchange': prev_sell_exchange,
                                    'buy_amount': buy_amount,
                                    'sell_exchange': prev_buy_exchange,
                                    'sell_amount': sell_amount,
                                    'time_stamp':time.time()

            }
            full_trade_record = {
                                'action': 'close',
                                'real':{
                                        'sell_record':close_sell_record,
                                        'buy_record':close_buy_record,
                                        },
                                'virtual': virtual_trade_record,
                                'timestamp':time.time()        }
            
            self.update_and_save(full_trade_record)


            self.active_trades.pop()
            history_log = json.dumps(full_trade_record,indent=4)
            history_logger.info(f'新纪录:\n{history_log}')
            return True
        decision_logger.info('平仓失败')
        return False
    
    def close_position_testAug21(self, symbol, prev_buy_exchange, prev_sell_exchange)-> bool:
        """
        prev_buy_exchange: 之前购入symbol (做多) 的交易所
        prev_sell_exchange: 之前出售symbol (做空) 的交易所

        """
        succeed = False

        # 买多少平多少
        trade_amount = self.active_trades[0]['virtual']['buy_amount']
        # ideally
        close_sell_record = {}
        close_buy_record = {}

        close_buy_order_signal = OrderSignal('linear',symbol,'sell','market',trade_amount,prev_buy_exchange,'close')
        close_buy_succeed, close_buy_orderid = self.unified_place_order(close_buy_order_signal)

        close_sell_order_signal = OrderSignal('linear',symbol,'buy','market',trade_amount,prev_sell_exchange,'close')
        close_sell_succeed, close_sell_orderid = self.unified_place_order(close_sell_order_signal)

        if close_buy_succeed and close_buy_orderid:
            close_buy_record = self.exchanges[close_buy_order_signal.exchange].get_order_detail(close_buy_orderid)
        else:
            decision_logger.error(f"{close_buy_order_signal.exchange} 下单失败或缺少订单ID")

        if close_sell_succeed and close_sell_orderid:
            close_sell_record = self.exchanges[close_sell_order_signal.exchange].get_order_detail(close_sell_orderid)
        else:
            decision_logger.error(f"{close_sell_order_signal.exchange} 下单失败或缺少订单ID")

        if not close_buy_succeed:
            decision_logger.info(f'平多失败, info: {close_buy_record}')

        if not close_sell_succeed:
            decision_logger.info(f'平空失败, info: {close_sell_record}')

        # if all succeed
        succeed = True
        if succeed:
            # free previous frozen capitals
            virtual_trade_record = {
                'symbol': symbol,
                'prev_buy_exchange': prev_buy_exchange,
                'prev_sell_exchange': prev_sell_exchange,
                'buy_exchange': prev_sell_exchange,
                'buy_amount': trade_amount,
                'sell_exchange': prev_buy_exchange,
                'sell_amount': trade_amount,
                'time_stamp':time.time()
            }
            full_trade_record = {
                'trade_id': self.active_trades[0]['trade_id'], 
                'action': 'close',
                'real':{
                        'sell_record':close_sell_record,
                        'buy_record':close_buy_record,
                        },
                'virtual': virtual_trade_record,
                'timestamp':time.time() 
            }
            self.update_and_save(full_trade_record)
            self.active_trades.pop()
            history_log = json.dumps(full_trade_record,indent=4)
            history_logger.info(f'新纪录:\n{history_log}')

            # redis
            trader_state.redis_client.delete("trading:current_position")
            # balance info update
            for exchange_name in [prev_buy_exchange,prev_sell_exchange]:
                account_usdt_value = Config.INITIAL_USDT_CAPITALS[exchange_name]
                _, available_usdt_balance = self.exchanges[exchange_name].get_balance_info()
                self.portfolio_manager.update_exchange_balance(
                    exchange_name=exchange_name,
                    account_usdt_value=account_usdt_value,
                    available_usdt_balance=available_usdt_balance
                )
    
            # 3. (可选但推荐) 更新后，立即将最新的整体状态持久化到Redis
            latest_state = self.portfolio_manager.to_dict()
            self.redis_client.set("trading:balance", json.dumps(latest_state))
            return True, full_trade_record
        # balance info update
        for exchange_name in [prev_buy_exchange,prev_sell_exchange]:
            account_usdt_value = Config.INITIAL_USDT_CAPITALS[exchange_name]
            _, available_usdt_balance = self.exchanges[exchange_name].get_balance_info()
            self.portfolio_manager.update_exchange_balance(
                exchange_name=exchange_name,
                account_usdt_value=account_usdt_value,
                available_usdt_balance=available_usdt_balance
            )

        # 3. (可选但推荐) 更新后，立即将最新的整体状态持久化到Redis
        latest_state = self.portfolio_manager.to_dict()
        self.redis_client.set("trading:balance", json.dumps(latest_state))
        return False, {"close_buy_succeed":close_buy_succeed,
                       "close_sell_succeed": close_sell_succeed}

    def can_open_positions(self) -> bool:
        return Config.MAX_POSITION_SIZE > len(self.active_trades)
    
    def check_open_conditions(self,max_spread_pct) -> bool:
        """return True of False, if should open -> True"""
        # TODO maybe also check balance
        return max_spread_pct >= Config.MIN_SPREAD_PCT_THRESHOLD
    
    def monitor_active_trades(self, data):
        for active_trade in self.active_trades:
            # calculate current_spread_pct but fixing the buy_exchange and sell_exchange.
            # have to sell@buy_exchange and buy@sell_exchange to close positions
            symbol = active_trade['virtual']['symbol']
            prev_buy = active_trade['virtual']['buy_exchange']
            bid_from_prev_buy = data[symbol][prev_buy]['bid']
            prev_sell = active_trade['virtual']['sell_exchange']
            ask_from_prev_sell = data[symbol][prev_sell]['ask']
            current_spread, current_spread_pct = calculate_closing_spread(bid_from_prev_buy, ask_from_prev_sell)

        return current_spread, current_spread_pct
    
    def check_close_conditions(self, spread_pct, target_threshold):
        """return True, threshold if meet the threshold, else False, threshold"""
        # TODO
        return spread_pct < target_threshold
                
    def unified_place_order(self, order_signal:OrderSignal) :
        # select an exchange and place order
        exchange_api = self.exchanges[order_signal.exchange]

        orderid = None
        succeed = False
        res = None

        # 格式化数量，避免浮点精度问题
        formatted_qty = round(order_signal.quantity, 6)

        if order_signal.exchange == 'Okx':
            # OKX需要将币的数量转换为张数
            contracts_qty = exchange_api.coins_to_contracts(order_signal.symbol, order_signal.quantity)
            if contracts_qty is None or contracts_qty <= 0:
                decision_logger.error(f"OKX单位转换失败: {order_signal.quantity} 币 -> {contracts_qty} 张")
                return False, None

            decision_logger.info(f"OKX下单转换: {order_signal.quantity} 币 -> {contracts_qty} 张")
            succeed, res = exchange_api.place_order(category=order_signal.category,
                                            symbol=order_signal.symbol,
                                            side=order_signal.side,
                                            orderType=order_signal.orderType,
                                            qty=contracts_qty,action=order_signal.action)
            if succeed and res:
                orderid = res['data'][0]['clOrdId']
        elif order_signal.exchange == 'Bitget':
            # Bitget使用币的数量，直接下单
            succeed, res = exchange_api.place_order(category=order_signal.category,
                                            symbol=order_signal.symbol,
                                            side=order_signal.side,
                                            orderType=order_signal.orderType,
                                            qty=formatted_qty,action=order_signal.action)
            if succeed and res:
                orderid = res['data']['clientOid'] # 使用统一的 orderid 字段
        elif order_signal.exchange == 'Binance':
            # Binance使用币的数量，直接下单
            succeed, res = exchange_api.place_order(category=order_signal.category,
                                            symbol=order_signal.symbol,
                                            side=order_signal.side,
                                            orderType=order_signal.orderType,
                                            qty=formatted_qty)
            if succeed and res:
                orderid = res['clientOrderId']
        else:
            # Bybit使用币的数量，直接下单
            succeed, res = exchange_api.place_order(category=order_signal.category,
                                symbol=order_signal.symbol,
                                side=order_signal.side,
                                orderType=order_signal.orderType,
                                qty=str(formatted_qty))  # Bybit 需要字符串格式
            if succeed and res:
                orderid = res['result']['orderLinkId']

        return succeed, orderid
        # 只有在下单成功且有订单ID时才查询订单详情
        # if succeed and orderid:
        #     try:
        #         order_detail = exchange_api.get_order_detail(orderid)
        #         return succeed, order_detail
        #     except Exception as e:
        #         decision_logger.error(f"获取订单详情失败: {e}")
        #         return succeed, res  # 返回原始下单响应
        # else:
        #     decision_logger.error(f"{order_signal.exchange} 下单失败或缺少订单ID")
        #     return succeed, res
    
    def find_opportunity(self,data):
        """return opportunity: symbol, and its spread, buy_exchange(ask), sell_exchange(bid), None if error"""
        max_spread_pct = 0
        best_opportunity = None
        best_buy_exchange = None
        best_sell_exchange = None
        best_exchange_data = None
        # go over data for each piece:
        for symbol, exchange_data in data.items():
        # calculate spread, check if it's greatest
            spread, spread_pct,buy_exchange,sell_exchange = calculate_symbol_spread(exchange_data)
            if spread_pct > max_spread_pct:
                max_spread_pct = spread_pct
                best_opportunity = symbol
                best_buy_exchange = buy_exchange
                best_sell_exchange = sell_exchange
                best_exchange_data = exchange_data
            
            if spread is None:
                decision_logger.info('数据不完整，跳过')
                continue

        latest_opportunity = {
            "symbol": best_opportunity,
            "best_buy_exchange": best_buy_exchange,
            "best_buy_price": data[best_opportunity][best_buy_exchange]["ask"],
            "best_sell_exchange": best_sell_exchange,
            "best_sell_price": data[best_opportunity][best_sell_exchange]["bid"],
            "open_spread_pct": max_spread_pct,
            "time_stamp_opportunity": time.time()
        }
        self.redis_client.set("trading:latest_opportunity",json.dumps(latest_opportunity))
        return best_opportunity, max_spread_pct, best_buy_exchange, best_sell_exchange,best_exchange_data



    def parse_order_result(self, exchange_name, order_result):
        """解析各交易所的订单结果，提取成交价格和数量
        返回: (avg_price, filled_qty, order_id, status)
        """
        if not order_result:
            return None, None, None, 'FAILED'

        try:
            if exchange_name == 'Binance':
                # Binance 返回格式
                avg_price = float(order_result.get('avgPrice', '0'))
                filled_qty = float(order_result.get('executedQty', '0'))
                order_id = order_result.get('orderId')
                status = order_result.get('status', 'UNKNOWN')
                return avg_price, filled_qty, order_id, status

            elif exchange_name == 'Bybit':
                # Bybit 返回格式
                if order_result.get('retCode') == 0 and order_result.get('result'):
                    result = order_result['result']
                    avg_price = float(result.get('avgPrice', '0'))
                    filled_qty = float(result.get('cumExecQty', '0'))
                    order_id = result.get('orderId')
                    status = result.get('orderStatus', 'UNKNOWN')
                    return avg_price, filled_qty, order_id, status

            elif exchange_name == 'Okx':
                # OKX 返回格式
                if order_result.get('code') == '0' and order_result.get('data'):
                    data = order_result['data'][0]
                    # OKX 下单后需要查询订单详情获取成交信息
                    order_id = data.get('ordId')
                    # 这里返回订单ID，实际成交信息需要后续查询
                    return None, None, order_id, 'PENDING'

            elif exchange_name == 'Bitget':
                # Bitget v2 返回格式
                if order_result.get('code') == '00000' and order_result.get('data'):
                    data = order_result['data']
                    order_id = data.get('orderId')
                    # Bitget 下单后也需要查询订单详情
                    return None, None, order_id, 'PENDING'

        except Exception as e:
            decision_logger.error(f"解析{exchange_name}订单结果失败: {e}")

        return None, None, None, 'FAILED'

    def get_order_details(self, exchange_name, exchange_api, order_id, symbol):
        """查询订单详情获取真实成交信息
        返回: (avg_price, filled_qty, status)
        """
        if not order_id:
            return None, None, 'FAILED'

        try:
            if exchange_name == 'Okx':
                # OKX 查询订单详情
                symbol_converted = exchange_api._symbol_convert(symbol)
                response = exchange_api.session.trade.get_order(instId=symbol_converted, ordId=order_id)
                if response.get('code') == '0' and response.get('data'):
                    order_data = response['data'][0]
                    avg_price = float(order_data.get('avgPx', '0'))
                    filled_qty = float(order_data.get('accFillSz', '0'))
                    status = order_data.get('state', 'UNKNOWN')
                    return avg_price, filled_qty, status

            elif exchange_name == 'Bitget':
                # Bitget v2 查询订单详情
                symbol_converted = exchange_api._convert_symbol(symbol)
                response = exchange_api.base_api.get("/api/v2/mix/order/detail", {
                    "symbol": symbol_converted,
                    "orderId": order_id
                })
                if response.get('code') == '00000' and response.get('data'):
                    order_data = response['data']
                    avg_price = float(order_data.get('priceAvg', '0'))
                    filled_qty = float(order_data.get('baseVolume', '0'))
                    status = order_data.get('state', 'UNKNOWN')
                    return avg_price, filled_qty, status

        except Exception as e:
            decision_logger.error(f"查询{exchange_name}订单详情失败: {e}")

        return None, None, 'FAILED'

def calculate_symbol_spread(exchange_data):
    """return spread,spread_pct, buy_exchange(min_ask['exchange']),sell_exchange(max_bid['exchange'])
        buy_exchange = exchange that gives ask price.
        """
    # find max ask min bid 对应的交易所
    # 在最便宜的卖家 买入
    # 在出价最高的买家 卖出
    max_bid = {"exchange": None, "price": Config.NEG_INF}
    min_ask = {"exchange": None, "price": Config.POS_INF}

    for exchange, data in exchange_data.items():

        if data["bid"] is not None and data["bid"] > max_bid["price"]:
            max_bid = {"exchange": exchange, "price": data["bid"]}
 
        # 检查卖价是否存在且比当前最小值低
        if data["ask"] is not None and data["ask"] < min_ask["price"]:
            min_ask = {"exchange": exchange, "price": data["ask"]}

    if max_bid['price'] is not Config.NEG_INF and min_ask['price'] is not Config.POS_INF:
        max_bid_price = max_bid['price']
        min_ask_price = min_ask['price']
        spread = max_bid_price - min_ask_price
        spread_pct = 2 * spread / (max_bid_price + min_ask_price)
        return spread,spread_pct, min_ask['exchange'], max_bid['exchange']
    
    decision_logger.info("calculate_symbol_spread error")
    return None, None, None, None
    
    # calculate_spread(max_bid,min_ask)


def calculate_spread(price_a, price_b):
    spread = price_a - price_b
    spread_pct = 2 * spread / (price_a + price_b)
    return spread, spread_pct

def calculate_opening_spread(ask_from_buy, bid_from_sell):
    #这个有可能反了 # 复查结束 ok
    spread =  bid_from_sell - ask_from_buy
    spread_pct = 2 * spread / (bid_from_sell + ask_from_buy)
    return spread, spread_pct

def calculate_closing_spread(bid_from_prev_buy, ask_from_prev_sell):
    #这个有可能反了需要复查 #复查结束 ok 确实反了
    spread =  ask_from_prev_sell - bid_from_prev_buy
    spread_pct =2 * spread / (bid_from_prev_buy + ask_from_prev_sell)
    return spread, spread_pct

def connect_to_sim_bybit(trader_state:TraderState):
    exchange_name = 'Bybit'
    bybit_exchange = BybitExchangeHTTP('Bybit', api_key=EXCHANGE_API_KEYS['bybit']['api_key'],api_secret=EXCHANGE_API_KEYS['bybit']['secret'],isTest=True)

    is_connected = bybit_exchange.connect()
    if is_connected:
        decision_logger.info(f'Bybit session is connected.')

    trader_state.exchanges['Bybit'] = bybit_exchange
    total_equity, total_available_balance = bybit_exchange.get_balance_info()
    if total_equity is None or total_available_balance is None:
        return False
    
    #新增
    trader_state.portfolio_manager.update_exchange_balance(exchange_name,Config.INITIAL_USDT_CAPITALS['Bybit'],total_available_balance)

    trader_state.balance_info["exchange_balances"][exchange_name]['available'] = total_available_balance
    print(f'{total_equity}, {total_available_balance}')
    if total_available_balance:
        decision_logger.info(f'Bybit 剩余可用资金: {total_available_balance}')
        return True
    else:
        decision_logger.info(f'Bybit 剩余可用资金查询失败')
        # 不应该能到这里
        return False

    # 示例， 买比特币
    # bybit_exchange.place_order(category="linear",symbol="BTCUSDT",side="buy",orderType="market",qty="0.001")

def connect_to_real_bybit(trader_state):
    bybit_exchange = BybitExchangeHTTP('Bybit', api_key=EXCHANGE_API_KEYS['bybit']['api_key'],api_secret=EXCHANGE_API_KEYS['bybit']['secret']) 
    is_connected = bybit_exchange.connect()
    if is_connected:
        decision_logger.info(f'Bybit已连接')
    trader_state.exchanges['Bybit'] = bybit_exchange
    
    total_equity, total_available_balance = bybit_exchange.get_balance_info()

    print(f'{total_equity}, {total_available_balance}')
    if total_equity is not None:
        decision_logger.info(f'Bybit 剩余可用资金: {total_available_balance}')
    else:
        decision_logger.info(f'Bybit 剩余可用资金查询失败')
    exchange_name = 'Bybit'
    trader_state.portfolio_manager.update_exchange_balance(exchange_name,Config.INITIAL_USDT_CAPITALS['Bybit'],total_available_balance)

def connect_to_real_bitget(trader_state):
    # demo, 无法连接
    # api_key = "bg_3a6ae37342a327fa91fe66ec806a3231"
    # api_secret = "ab448cbf5be214d5b1b047bee7d922fdf05bf32f61e2be178bce72fcbc536c86"

    # real
    api_key = EXCHANGE_API_KEYS['bitget']['api_key']
    api_secret = EXCHANGE_API_KEYS['bitget']['secret']
    api_passphrase = EXCHANGE_API_KEYS['bitget']['passphrase']

    # client = Client(api_key, api_secret, passphrase=api_passphrase)
    # result = client.mix_get_accounts(productType='UMCBL')
    # print(result)
    # 尝试使用 Bitget v2 API，如果不可用则回退到 v1
    try:
        bitget_exchange = BitgetV2HTTP('Bitget',api_key=api_key,api_secret=api_secret,passphrase=api_passphrase)
        decision_logger.info("使用 Bitget v2 API")
    except Exception as e:
        decision_logger.warning(f"Bitget v2 API 不可用，回退到 v1: {e}")
        bitget_exchange = Bitgetv1HTTP('Bitget',api_key=api_key,api_secret=api_secret,passphrase=api_passphrase)
    is_connected = bitget_exchange.connect()
    if is_connected:
        decision_logger.info(f'Bitget已连接')
    trader_state.exchanges['Bitget'] = bitget_exchange
    
    total_equity, total_available_balance = bitget_exchange.get_balance_info()
    
    if total_equity is not None:
        decision_logger.info(f'Bitget 剩余可用资金: {total_available_balance}')
    else:
        decision_logger.info(f'Bitget 剩余可用资金查询失败')
    exchange_name = 'Bitget'
    trader_state.portfolio_manager.update_exchange_balance(exchange_name,Config.INITIAL_USDT_CAPITALS['Bitget'],total_available_balance)
        
    # succ, place_order_response = bitget_exchange.place_order('linear','BTCUSDT','buy','market','0.001')
    # decision_logger.info(place_order_response)

def connect_to_sim_bitget_v1(trader_state:TraderState):

    api_key = EXCHANGE_API_KEYS['bitget']['api_key']
    api_secret = EXCHANGE_API_KEYS['bitget']['secret']
    api_passphrase = EXCHANGE_API_KEYS['bitget']['passphrase']

    # client = Client(api_key, api_secret, passphrase=api_passphrase)
    # result = client.mix_get_accounts(productType='UMCBL')
    # print(result)
    # 尝试使用 Bitget v2 API，如果不可用则回退到 v1
    try:
        bitget_exchange = BitgetV2HTTP('Bitget',api_key=api_key,api_secret=api_secret,passphrase=api_passphrase)
        decision_logger.info("使用 Bitget v2 API")
    except Exception as e:
        decision_logger.warning(f"Bitget v2 API 不可用，回退到 v1: {e}")
        bitget_exchange = Bitgetv1HTTP('Bitget',api_key=api_key,api_secret=api_secret,passphrase=api_passphrase)
    is_connected = bitget_exchange.connect()
    if is_connected:
        decision_logger.info(f'Bitget已连接')
    trader_state.exchanges['Bitget'] = bitget_exchange
    
    total_equity, total_available_balance = bitget_exchange.get_balance_info()
    if total_equity is not None:
        decision_logger.info(f'Bitget 剩余可用资金: {total_available_balance}')
    else:
        decision_logger.info(f'Bitget 剩余可用资金查询失败')
        
    # succeed, place_order_response = bitget_exchange.place_order('linear','BTCUSDT','buy','market','0.001')
    # decision_logger.info(place_order_response)

def connect_to_okx(trader_state):
        # 模拟
    exchange_name = 'Okx'

    # api = OkxRestClient('85919a48-8a5a-484c-9b3a-e0403d469b1f', '965C4759E597A89C83FA08D6B4DF4AC1', 'cntest001@O',simulation=True)
    okx_exchange = OkxExchangeHTTP('Okx',EXCHANGE_API_KEYS['okx']['api_key'], EXCHANGE_API_KEYS['okx']['secret'], EXCHANGE_API_KEYS['okx']['passphrase'])

    # okx_exchange = OkxExchangeHTTP('Okx','ecc247b6-7c2d-4d97-a751-81c0fb828a6b', '3A67F1CA4D528D5010880BD0E37348E6', 'cntest001@O')
    okx_exchange.connect()
    trader_state.exchanges['Okx'] = okx_exchange
    total_equity, total_available_balance = okx_exchange.get_balance_info()

    if total_equity is not None:
        decision_logger.info(f'Okx 剩余可用资金: {total_available_balance}')
    else:
        decision_logger.info(f'Okx 剩余可用资金查询失败')
    trader_state.portfolio_manager.update_exchange_balance(exchange_name,Config.INITIAL_USDT_CAPITALS['Okx'],total_available_balance)

    # ret =okx_exchange.place_order('linear','BTCUSDT','buy','market','0.01')
    # ret_code = ret['code']
    # ret_message = ret['data'][0]['sMsg']
    # print('hello')

def connect_to_binance(trader_state):
    binance_exchange = BinanceExchangeHTTP('Binance',EXCHANGE_API_KEYS['binance']['api_key'], EXCHANGE_API_KEYS['binance']['secret'])
    binance_exchange.connect()
    trader_state.exchanges['Binance'] = binance_exchange
    total_equity, total_available_balance = binance_exchange.get_balance_info()
    if total_equity is not None:
        decision_logger.info(f'Binance 剩余可用资金: {total_available_balance}')
    else:
        decision_logger.info(f'Binance 剩余可用资金查询失败')
    exchange_name = 'Binance'
    trader_state.portfolio_manager.update_exchange_balance(exchange_name,Config.INITIAL_USDT_CAPITALS['Binance'],total_available_balance)

def connect_to_sim_okx(trader_state:TraderState):
        # 模拟
    # api = OkxRestClient('85919a48-8a5a-484c-9b3a-e0403d469b1f', '965C4759E597A89C83FA08D6B4DF4AC1', 'cntest001@O',simulation=True)
    exchange_name = 'Okx'
    okx_exchange = OkxExchangeHTTP(exchange_name,EXCHANGE_API_KEYS['okx']['api_key'], EXCHANGE_API_KEYS['okx']['secret'], EXCHANGE_API_KEYS['okx']['passphrase'], isTest=True)

    # okx_exchange = OkxExchangeHTTP('Okx','ecc247b6-7c2d-4d97-a751-81c0fb828a6b', '3A67F1CA4D528D5010880BD0E37348E6', 'cntest001@O')
    okx_exchange.connect()
    trader_state.exchanges['Okx'] = okx_exchange
    total_equity, total_available_balance = okx_exchange.get_balance_info()

    if total_equity is None or total_available_balance is None:
        return False
    
    trader_state.set_balance(exchange_name,total_available_balance)
    trader_state.calculate_total_exchange_capital(exchange_name)
    if total_equity:
        decision_logger.info(f'Okx 剩余可用资金: {total_available_balance}')
        return True
    else:
        decision_logger.info(f'Okx 剩余可用资金查询失败')
        return False


def connect_to_exchanges(trader_state:TraderState) ->  bool:
    if not connect_to_sim_bybit(trader_state):
        return False
    # connect_to_sim_bitget_v1(trader_state)
    if not connect_to_sim_okx(trader_state):
        return False

    return True

if __name__ == "__main__":
    
    trader_state = TraderState()
    connect_to_real_bybit(trader_state)
    connect_to_real_bitget(trader_state)
    connect_to_okx(trader_state)
    connect_to_binance(trader_state)
    latest_portfolio_state = trader_state.portfolio_manager.to_dict()
    state_json = json.dumps(latest_portfolio_state, indent=2)
    
    trader_state.redis_client.set("trading:balance", state_json)

    # 初始化缓存 - 在主循环开始前建立缓存
    decision_logger.info("=== 初始化缓存系统 ===")

    # 1. 加载所有符号信息
    decision_logger.info("加载符号信息缓存...")
    trader_state.load_all_symbol_info()

    # 2. 刷新余额缓存
    decision_logger.info("初始化余额缓存...")
    trader_state.refresh_all_balances()

    decision_logger.info("=== 缓存系统初始化完成，开始交易循环 ===")

    # accounts conncected
    while True:
        raw_data = trader_state.redis_client.get("trading:exchange_data")
        data = json.loads(raw_data)
        # print(data)
        if trader_state.can_open_positions():
            # decision_logger.info(f'空仓，寻找开仓机会')
            # only check new opp when we have more positions
            symbol, max_spread_pct, buy_exchange, sell_exchange,best_exchange_data = trader_state.find_opportunity(data)
            if symbol is not None:
                
                # decision_logger.info(f'扫描最佳机会，当前最佳币种/开仓价差:{symbol}/{max_spread_pct:.5f}. exchange_data:{best_exchange_data}')

                # sleep(2) #调试 find opportunity
                # continue

                # if (sell_exchange != 'Bitget') and (buy_exchange != 'Bitget'):
                #     decision_logger.info(f'跳过不含bitget的结果')
                #     sleep(1)
                #     continue

                # if (sell_exchange != 'Okx') and (buy_exchange != 'Okx'):
                #     decision_logger.info(f'跳过不含okx的结果')
                #     sleep(1)
                #     continue

                # if (sell_exchange != 'Bybit') and (buy_exchange != 'Bybit'):
                #     decision_logger.info(f'跳过不含Bybit的结果')
                #     sleep(1)
                #     continue

                # if (sell_exchange != 'Binance') and (buy_exchange != 'Binance'):
                #     decision_logger.info(f'跳过不含Binance的结果')
                #     sleep(1)
                #     continue

                # if (sell_exchange == 'Bitget') or (buy_exchange == 'Bitget'):
                #     decision_logger.info(f'跳过含bitget的结果')
                #     sleep(1)
                #     continue

                # if (sell_exchange == 'Okx') or (buy_exchange == 'Okx'):
                #     decision_logger.info(f'跳过含okx的结果')
                #     sleep(1)
                #     continue

                # if (sell_exchange == 'Bybit') or (buy_exchange == 'Bybit'):
                #     decision_logger.info(f'跳过含Bybit的结果')
                #     sleep(1)
                #     continue

                # if (sell_exchange == 'Binance') or (buy_exchange == 'Binance'):
                #     decision_logger.info(f'跳过含Binance的结果')
                #     sleep(1)
                #     continue

                if (buy_exchange not in trader_state.test_exchanges) or (sell_exchange not in trader_state.test_exchanges):
                    decision_logger.info(f'最佳开仓价差:{symbol}/{max_spread_pct:.5f}. {symbol}@{buy_exchange}/{sell_exchange},  exchange_data:{best_exchange_data}. 跳过')
                    sleep(1)
                    continue
                should_open_position = trader_state.check_open_conditions(max_spread_pct)
                if should_open_position:
                    decision_logger.info(f'符合开仓条件，当前开仓价差比:{max_spread_pct:.5f}'
                                         f'币种: {symbol}. 买@{buy_exchange}, ask:{data[symbol][buy_exchange]["ask"]},bid:{data[symbol][buy_exchange]["bid"]}.'
                                        f'卖@{sell_exchange}, ask:{data[symbol][sell_exchange]["ask"]},bid:{data[symbol][sell_exchange]["bid"]}.')
                    success, record = trader_state.open_position_test_Aug21(symbol, buy_exchange, sell_exchange, data)

                    if success:
                        decision_logger.info(f'开仓成功')
                        # virtual_rec = record['virtual']
                        # actual_spread = virtual_rec['sell_price'] - virtual_rec['buy_price']
                        # actual_spread_pct = 2 * actual_spread / (virtual_rec['sell_price'] + virtual_rec['buy_price'])

                    else:
                        # failed
                        decision_logger.info(f'开仓失败')
                        break
            # opp is None
            else:
                decision_logger.info(f'数据不完整，无法计算')

        else:
            # decision_logger.info(f'有活跃仓位')
            #TODO 暂时设定：开仓的话这个循环不监视现有仓位，因为我们现在没有超过1仓位并且我想要避免加入仓位之后立刻监视，因为数据还未刷新
            # 假设数据已经刷新
            # monitor active trades
            current_spread, current_spread_pct = trader_state.monitor_active_trades(data)
            # check close_conditions

            target_threshold =  trader_state.active_trades[0]['virtual']['target_threshold']
            # decision_logger.info(f'当前清仓价差比:{current_spread_pct:.5f}, 目标价差比: {target_threshold:.5f}')
            
            should_close_position = trader_state.check_close_conditions(current_spread_pct, target_threshold)
            if should_close_position:

                decision_logger.info(f'符合平仓条件, 条件: current_spread_pct: {current_spread_pct:.6f} <= target_threshold: {target_threshold:.6f}')
                # close_postion
                record = trader_state.active_trades[0]['virtual']
                symbol = record['symbol']
                prev_buy_exchange = record['buy_exchange']
                prev_sell_exchange = record['sell_exchange']

                success, close_info = trader_state.close_position_testAug21(symbol,prev_buy_exchange,prev_sell_exchange)
                if success:
                    decision_logger.info(f'平仓成功')
                else:
                    decision_logger.info(f'close info: {close_info}')
                break
        sleep(0.01)