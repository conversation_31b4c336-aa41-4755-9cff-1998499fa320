# 币安WebSocket订单推送修复方案

## 问题分析

通过分析代码和币安官方文档，发现币安websocket无法获取订单交易更新推送的主要问题：

### 1. listenKey过期问题
- 币安的listenKey只有60分钟有效期
- 原代码没有续期机制，导致连接在60分钟后失效
- 需要每30-40分钟发送PUT请求延长有效期

### 2. 消息处理逻辑不完整
- 缺少对`listenKeyExpired`事件的处理
- 错误处理不够详细，难以调试问题
- 日志记录不够详细

### 3. 连接稳定性问题
- ping间隔过长（180秒），可能导致连接不稳定
- 缺少对连接状态的详细监控

## 修复方案

### 1. 增强listenKey管理

#### 新增函数：
- `_keepalive_binance_listen_key()`: 延长listenKey有效期
- `_binance_keepalive_task()`: 后台任务，每30分钟自动续期

```python
async def _keepalive_binance_listen_key(api_key: str, listen_key: str) -> bool:
    """延长listenKey有效期60分钟"""
    endpoint = "/fapi/v1/listenKey"
    url = BINANCE_REST_URL_LIVE + endpoint
    headers = {'X-MBX-APIKEY': api_key}
    data = {'listenKey': listen_key}
    
    async with aiohttp.ClientSession() as session:
        async with session.put(url, headers=headers, data=data) as response:
            return response.status == 200
```

### 2. 改进消息处理

#### 新增专门的消息处理函数：
- `_handle_binance_order_update()`: 处理ORDER_TRADE_UPDATE事件
- `_handle_binance_account_update()`: 处理ACCOUNT_UPDATE事件

#### 关键改进：
- 添加对`listenKeyExpired`事件的处理
- 增强错误处理和日志记录
- 只在实际有成交量时更新订单填充信息

```python
async def _handle_binance_order_update(msg: dict):
    """处理ORDER_TRADE_UPDATE事件"""
    order_data = msg.get("o", {})
    symbol = order_data.get("s")
    side = order_data.get("S")
    filled_qty = order_data.get("z")  # 累计成交数量
    
    # 只在有实际成交时更新
    if all([symbol, side, filled_qty]) and float(filled_qty) > 0:
        realtime_data_manager.update_order_fill(BINANCE_EXCHANGE_NAME, symbol, side, filled_qty)
```

### 3. 提升连接稳定性

#### 连接参数优化：
- 将ping间隔从180秒改为60秒
- 添加ping超时设置（30秒）
- 改进重连逻辑

```python
async with websockets.connect(ws_url, ping_interval=60, ping_timeout=30) as ws:
    # 启动keepalive任务
    keepalive_task = asyncio.create_task(_binance_keepalive_task(api_key, listen_key))
```

### 4. 增强日志和调试

#### 日志改进：
- 添加更详细的连接状态日志
- 记录listenKey获取和续期状态
- 添加订单事件的详细信息记录
- 减少第三方库的日志噪音

```python
LOG_FORMAT = "%(asctime)s | %(levelname)-7s | %(name)s | %(message)s"
logging.getLogger("websockets").setLevel(logging.WARNING)
logging.getLogger("aiohttp").setLevel(logging.WARNING)
```

## 主要修改文件

### trades_ws.py
1. **新增函数**：
   - `_keepalive_binance_listen_key()`: listenKey续期
   - `_binance_keepalive_task()`: 后台续期任务
   - `_handle_binance_order_update()`: 订单更新处理
   - `_handle_binance_account_update()`: 账户更新处理
   - `_test_binance_api_key()`: API密钥验证

2. **修改函数**：
   - `_get_binance_listen_key()`: 增强错误处理和日志
   - `binance_listener()`: 完全重构，添加keepalive机制

3. **配置优化**：
   - 改进日志格式和级别设置
   - 优化websocket连接参数

## 预期效果

修复后的币安websocket连接应该能够：

1. **稳定连接**：通过keepalive机制保持长期连接
2. **及时推送**：正确接收和处理ORDER_TRADE_UPDATE事件
3. **自动恢复**：在连接断开时自动重连
4. **详细日志**：提供充分的调试信息

## 使用建议

1. **测试环境**：建议先在测试网络验证修复效果
2. **监控日志**：关注连接状态和订单推送日志
3. **API权限**：确保API密钥有期货交易权限
4. **网络稳定**：确保网络连接稳定，避免频繁重连

## 注意事项

1. **API限制**：注意币安API的请求频率限制
2. **权限要求**：确保API密钥有用户数据流权限
3. **时区问题**：注意时间戳的时区处理
4. **资源清理**：确保在程序退出时正确清理keepalive任务
