#!/usr/bin/env python3
"""
trades_ws.py 日志配置示例
展示如何在WebSocket交易脚本中使用带日期的日志文件
"""

import asyncio
import json
import logging
import time
from websocket_logging import setup_websocket_logging, setup_daily_rotating_logger

# 方式1: 使用基本的带日期时间的日志文件
def setup_basic_logging():
    """设置基本的带日期时间的日志"""
    logger = setup_websocket_logging(
        log_dir='./logs/trades',
        logger_name="MultiExchangeListener",
        include_console=True
    )
    return logger

# 方式2: 使用每日轮转的日志文件
def setup_rotating_logging():
    """设置每日轮转的日志"""
    logger = setup_daily_rotating_logger(
        log_dir='./logs/trades',
        base_filename='websocket_trades',
        logger_name="MultiExchangeListener"
    )
    return logger

# 方式3: 自定义日志配置
def setup_custom_logging():
    """设置自定义日志配置"""
    import os
    from datetime import datetime
    
    log_dir = './logs/trades'
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    # 生成带日期时间的文件名
    current_datetime = datetime.now().strftime('%Y-%m-%d_%H-%M-%S')
    log_filename = f'trades_ws_{current_datetime}.log'
    log_filepath = os.path.join(log_dir, log_filename)
    
    # 创建日志记录器
    logger = logging.getLogger("MultiExchangeListener")
    logger.setLevel(logging.INFO)
    
    # 清除现有处理器
    logger.handlers.clear()
    
    # 设置格式
    formatter = logging.Formatter(
        "%(asctime)s | %(levelname)-7s | %(name)s | %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S"
    )
    
    # 文件处理器
    file_handler = logging.FileHandler(log_filepath, encoding='utf-8')
    file_handler.setFormatter(formatter)
    logger.addHandler(file_handler)
    
    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # 设置第三方库日志级别
    logging.getLogger("websockets").setLevel(logging.WARNING)
    logging.getLogger("aiohttp").setLevel(logging.WARNING)
    
    logger.info(f"自定义日志系统初始化完成，日志文件: {log_filepath}")
    return logger

# 模拟WebSocket监听器
async def mock_exchange_listener(exchange_name: str, logger: logging.Logger):
    """模拟交易所监听器"""
    for i in range(5):
        logger.info(f"[{exchange_name}] 模拟接收到消息 #{i+1}")
        
        # 模拟订单更新
        if i % 2 == 0:
            logger.info(f"[{exchange_name}] ### Order Update ### : 模拟订单数据")
        
        # 模拟位置更新
        if i % 3 == 0:
            logger.info(f"[{exchange_name}] <<< Position Update >>> : 模拟位置数据")
        
        await asyncio.sleep(1)

async def test_logging_methods():
    """测试不同的日志配置方法"""
    print("测试方式1: 基本带日期时间的日志")
    logger1 = setup_basic_logging()
    await mock_exchange_listener("Binance", logger1)
    
    await asyncio.sleep(2)
    
    print("\n测试方式2: 每日轮转日志")
    logger2 = setup_rotating_logging()
    await mock_exchange_listener("Bybit", logger2)
    
    await asyncio.sleep(2)
    
    print("\n测试方式3: 自定义日志配置")
    logger3 = setup_custom_logging()
    await mock_exchange_listener("OKX", logger3)

if __name__ == "__main__":
    print("=" * 60)
    print("WebSocket交易日志配置示例")
    print("=" * 60)
    print("此示例展示了三种不同的日志配置方法：")
    print("1. 基本的带日期时间的日志文件")
    print("2. 每日轮转的日志文件")
    print("3. 自定义日志配置")
    print("=" * 60)
    
    try:
        asyncio.run(test_logging_methods())
        print("\n" + "=" * 60)
        print("测试完成！请检查 ./logs/trades/ 目录下的日志文件")
        print("=" * 60)
    except KeyboardInterrupt:
        print("\n测试被中断")
    except Exception as e:
        print(f"\n测试过程中发生错误: {e}")

# 在trades_ws.py中的使用示例：
"""
# 在trades_ws.py文件顶部添加：
from websocket_logging import setup_websocket_logging

# 替换原有的日志配置：
logger = setup_websocket_logging(
    log_dir='./logs/trades',
    logger_name="MultiExchangeListener",
    include_console=True
)

# 或者使用轮转日志：
from websocket_logging import setup_daily_rotating_logger
logger = setup_daily_rotating_logger(
    log_dir='./logs/trades',
    base_filename='websocket_trades',
    logger_name="MultiExchangeListener"
)
"""
