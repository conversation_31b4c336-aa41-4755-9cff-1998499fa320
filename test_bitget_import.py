#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 Bitget v2 API 导入
"""

print("=== 测试 Bitget v2 API 导入 ===")

# 测试导入
try:
    import bitget.v2.mix.order_api as bitgetOrderApi
    import bitget.v2.mix.account_api as bitgetAccountApi
    import bitget.v2.mix.market_api as bitgetMarketApi
    import bitget.bitget_api as bitgetBaseApi
    BITGET_V2_AVAILABLE = True
    print("✅ Bitget v2 API 导入成功")
except ImportError as e:
    BITGET_V2_AVAILABLE = False
    print(f"⚠️ Bitget v2 API 导入失败: {e}")
except Exception as e:
    BITGET_V2_AVAILABLE = False
    print(f"⚠️ Bitget v2 API 导入异常: {e}")

print(f"BITGET_V2_AVAILABLE = {BITGET_V2_AVAILABLE}")

if BITGET_V2_AVAILABLE:
    print("\n=== 测试 API 类初始化 ===")
    try:
        # 测试基础API
        base_api = bitgetBaseApi.BitgetApi("test_key", "test_secret", "test_passphrase")
        print("✅ BitgetApi 初始化成功")
        
        # 测试各种API
        order_api = bitgetOrderApi.OrderApi("test_key", "test_secret", "test_passphrase")
        print("✅ OrderApi 初始化成功")
        
        account_api = bitgetAccountApi.AccountApi("test_key", "test_secret", "test_passphrase")
        print("✅ AccountApi 初始化成功")
        
        market_api = bitgetMarketApi.MarketApi("test_key", "test_secret", "test_passphrase")
        print("✅ MarketApi 初始化成功")
        
    except Exception as e:
        print(f"❌ API 初始化失败: {e}")

print("\n=== 测试 BitgetV2HTTP 类 ===")
try:
    from exchange import BitgetV2HTTP
    print("✅ BitgetV2HTTP 类导入成功")
    
    # 创建实例（使用测试密钥）
    bitget_v2 = BitgetV2HTTP('Bitget', 'test_key', 'test_secret', 'test_passphrase')
    print("✅ BitgetV2HTTP 实例创建成功")
    
except Exception as e:
    print(f"❌ BitgetV2HTTP 测试失败: {e}")
    import traceback
    traceback.print_exc()

print("\n=== 测试完成 ===")
