import time
from trader import TraderState, unified_place_order,OrderSignal
from logging_setup import setup_loggers
# from trader import connect_to_bitget
from exchange import Bitgetv1HTTP
import uuid
from datetime import datetime
from datetime import timedelta

trader_state = TraderState()
decision_logger, output_logger = setup_loggers()
decision_logger.info(f'模拟开始')
# 实盘
api_key = "bg_021b8d909fb2f072394cf55b363d7da3"
api_secret = "b29812f4139de84d53ff56d2575ed6f2741e8268bbf0e1c47e65cc049495f952"
api_passphrase = "test001B"
bitget_exchange = Bitgetv1HTTP('Bitget',api_key=api_key,api_secret=api_secret,passphrase=api_passphrase)


# bitget_exchange = BitgetExchangeHTTP('bitget','ecc247b6-7c2d-4d97-a751-81c0fb828a6b', '3A67F1CA4D528D5010880BD0E37348E6', 'cntest001@O',isTest=False)
bitget_exchange.connect()
trader_state.exchanges['bitget'] = bitget_exchange
total_equity, total_available_balance = bitget_exchange.get_balance_info()
if total_equity:
    decision_logger.info(f'bitget 剩余可用资金: {total_available_balance}')
else:
    decision_logger.info(f'bitget 剩余可用资金查询失败')

# acc_configs = bitget_exchange.session.account.get_account_config()
# set_lv_response= bitget_exchange.session.account.set_account_level("2")
# acc_mode = bitget_exchange._get_position_mode()
# bitget_exchange.session.account.set_position_mode('net_mode')
# acc_mode = bitget_exchange._get_position_mode()

bitget_exchange_api = trader_state.exchanges['bitget']
buy_order_signal = OrderSignal('linear','QTUMUSDT','buy','market','10','bitget')


bitget_buy_res = unified_place_order(trader_state,buy_order_signal)

print(bitget_buy_res)
"""INFO:output_logger:Bitget status code: 400. Error Message: The order amount exceeds the balance
(False, None)"""


sell_order_signal = OrderSignal('linear','QTUMUSDT','sell','market','10','bitget')
bitget_sell_res = unified_place_order(trader_state, sell_order_signal)

print(bitget_sell_res)
"""INFO:output_logger:Bitget status code: 400. Error Message: The order amount exceeds the balance
(False, None)"""
# bitget 获取余额速率限制 10次/秒
"""startTime	String	Yes	startTime，eg：*************. (For Managed Sub-Account, the StartTime cannot be earlier than the binding time)
endTime	String	Yes	endTime，eg：************* Maximum interval 90 days"""

# for time, use uTime(unix system time?)

# 1. 计算时间范围
end_time_ms = int(time.time() * 1000) # 当前时间(毫秒)
start_dt = datetime.utcnow() - timedelta(days=30)
start_time_ms = int(start_dt.timestamp() * 1000) # 一个月前的时间(毫秒)
history = bitget_exchange.session.mix_get_productType_history_orders(productType='umcbl',startTime=start_time_ms,endTime=end_time_ms,pageSize=10)
print(history)
{'code': '00000', 'msg': 'success', 'requestTime': *************, 'data': {'nextFlag': False, 'endId': None, 'orderList': None}}
bitget_exchange_api.get_balance_info()