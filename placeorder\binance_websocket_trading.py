#!/usr/bin/env python3
# 文件名: binance_websocket_trading.py

import asyncio
import json
import logging
import time
import hmac
import hashlib
from typing import Dict, Any
from uuid import uuid4

import websockets
from websockets.legacy.client import WebSocketClientProtocol
import redis.asyncio as redis

try:
    from api_keys import EXCHANGE_API_KEYS
except ImportError:
    print("错误：找不到 api_keys.py 文件。")
    exit()

# --- 全局配置 ---
LOG_FORMAT = "%(asctime)s | %(levelname)-7s | %(module)s:%(lineno)-4d | %(message)s"
logging.basicConfig(level=logging.DEBUG, format=LOG_FORMAT, datefmt="%H:%M:%S")
logger = logging.getLogger("BinanceWsOrderService")

# U本位合约交易 WebSocket 地址 (实盘)
WS_URL = "wss://fstream.binance.com/ws"
# U本位合约交易 WebSocket 地址 (测试网)
# WS_URL = "wss://stream.binancefuture.com/ws"

REDIS_URL = "redis://localhost:6379/0"
REDIS_ORDER_CHANNEL = "trading_orders:binance"

class BinanceWebSocketManager:
    def __init__(self, api_key: str, secret_key: str, url: str):
        self._api_key, self._secret_key, self._url = api_key, secret_key, url
        self._ws: WebSocketClientProtocol = None
        self._is_connected = False
        self._listener_task = None
        self._pending_requests = {} # 用于匹配请求和响应

    async def connect(self):
        while True:
            try:
                logger.info(f"正在尝试连接到 Binance: {self._url}...")
                self._ws = await websockets.connect(self._url, ping_interval=20, ping_timeout=20)
                self._is_connected = True
                logger.info("Binance WebSocket 已连接。该接口无需登录指令。")
                if self._listener_task: self._listener_task.cancel()
                self._listener_task = asyncio.create_task(self._listen())
                await self._listener_task
            except (websockets.exceptions.ConnectionClosedError, ConnectionError) as e:
                logger.warning(f"Binance WebSocket 连接丢失: {e}。5秒后将尝试重连...")
            except Exception as e:
                logger.error(f"连接过程中发生未知错误: {e}", exc_info=True)
            finally:
                self._is_connected = False
                # 清理所有待处理的请求，让它们失败
                for future in self._pending_requests.values():
                    future.set_exception(ConnectionError("连接已断开，请求失败"))
                self._pending_requests.clear()
                await asyncio.sleep(5)

    async def _listen(self):
        logger.info("Binance WebSocket 消息监听任务已启动。")
        try:
            async for message in self._ws:
                data = json.loads(message)
                request_id = data.get("id")
                # 检查是否是某个请求的响应
                if request_id and request_id in self._pending_requests:
                    future = self._pending_requests.pop(request_id)
                    future.set_result(data) # 将完整响应传递回去
                else:
                    logger.debug(f"收到未处理的 Binance 消息: {data}")
        except websockets.exceptions.ConnectionClosed as e:
            logger.warning(f"Binance 监听任务因连接关闭而终止: {e}")
            raise

    def _generate_signature(self, params: Dict[str, Any]) -> str:
        """为请求参数生成签名。"""
        query_string = "&".join([f"{key}={params[key]}" for key in sorted(params.keys())])
        return hmac.new(self._secret_key.encode('utf-8'), query_string.encode('utf-8'), hashlib.sha256).hexdigest()

    async def place_order(self, order_params: Dict[str, Any]):
        if not self._is_connected or not self._ws:
            logger.error(f"无法下单 (ClientID: {order_params.get('newClientOrderId')}): WebSocket 未连接。")
            return
            
        request_id = str(uuid4()) # 生成一个唯一的请求ID
        
        # 准备完整的请求参数
        params = order_params.copy()
        params['apiKey'] = self._api_key
        params['timestamp'] = int(time.time() * 1000)
        params['signature'] = self._generate_signature(params)

        payload = {
            "id": request_id,
            "method": "order.place",
            "params": params
        }
        
        try:
            logger.info(f"-> 准备发送 Binance 订单 (ReqID: {request_id}): {json.dumps(payload)}")
            
            # 创建一个 Future 对象等待响应
            future = asyncio.get_event_loop().create_future()
            self._pending_requests[request_id] = future
            
            await self._ws.send(json.dumps(payload))
            
            # 等待响应，设置超时时间
            response = await asyncio.wait_for(future, timeout=10)
            self._handle_order_response(response)

        except asyncio.TimeoutError:
            logger.error(f"❌ [Binance 下单超时] ReqID: {request_id}, 服务器未在10秒内响应。")
            self._pending_requests.pop(request_id, None)
        except Exception as e:
            logger.error(f"发送 Binance 订单时出错 (ReqID: {request_id}): {e}", exc_info=True)
            self._pending_requests.pop(request_id, None)
            
    def _handle_order_response(self, response: Dict[str, Any]):
        request_id = response.get("id", "N/A")
        if response.get("status") == 200:
            result = response.get("result", {})
            order_id = result.get("orderId", "N/A")
            client_oid = result.get("clientOrderId", "N/A")
            logger.info(f"✅ [Binance 下单成功] ReqID: {request_id}, OrderID: {order_id}, ClientID: {client_oid}")
        else:
            error = response.get("error", {})
            code = error.get("code", "N/A")
            msg = error.get("msg", "未知错误")
            logger.error(f"❌ [Binance 下单失败] ReqID: {request_id}, 错误码: {code}, 原因: {msg}")

async def listen_for_redis_orders(ws_manager: BinanceWebSocketManager):
    while True:
        try:
            logger.info(f"正在连接到 Redis ({REDIS_URL})...")
            r = await redis.from_url(REDIS_URL, decode_responses=True)
            pubsub = r.pubsub()
            await pubsub.subscribe(REDIS_ORDER_CHANNEL)
            logger.info(f"✅ 已成功订阅 Redis 频道: '{REDIS_ORDER_CHANNEL}'")
            async for message in pubsub.listen():
                if message['type'] != 'message': continue
                logger.info(f"从 Redis 收到消息: {message['data']}")
                try:
                    order_details_dict = json.loads(message['data'])
                    await ws_manager.place_order(order_details_dict)
                except json.JSONDecodeError:
                    logger.error(f"无法解析来自 Redis 的 JSON 消息: {message['data']}")
                except Exception as e:
                    logger.error(f"处理 Redis 消息时出错: {e}", exc_info=True)
        except redis.exceptions.ConnectionError as e:
            logger.error(f"Redis 连接失败: {e}。10秒后将重试...")
            await asyncio.sleep(10)

async def main():
    logger.info("启动 Binance WebSocket 下单服务...")
    credentials = EXCHANGE_API_KEYS.get("binance")
    if not credentials or "api_key" not in credentials:
        logger.error("错误：请在 api_keys.py 文件中正确配置您的 Binance API 信息。")
        return
    ws_manager = BinanceWebSocketManager(
        api_key=credentials["api_key"], secret_key=credentials["secret"], url=WS_URL
    )
    try:
        await asyncio.gather(ws_manager.connect(), listen_for_redis_orders(ws_manager))
    finally:
        logger.info("服务正在关闭...")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("收到关闭信号 (Ctrl+C)，程序退出。")