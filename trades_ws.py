#!/usr/bin/env python3
"""
Multi-Exchange Private WebSocket Listener
-----------------------------------------
A robust, single-file script to concurrently listen to private channels (positions and orders)
for Bybit, OKX, and Bitget using asyncio.
"""

# --- Consolidated Imports ---
import asyncio
import json
import logging
import time
import hmac
import hashlib
import random
import signal
import sys
import base64

import aiohttp

# --- Core Dependencies ---
import websockets
from okx import OkxSocketClient
from api_keys import EXCHANGE_API_KEYS
from order_publisher import WsSignal,bitget_place_order,bybit_place_order

# --- Global Configuration ---
LOG_FORMAT = "%(asctime)s | %(levelname)-7s | %(name)s | %(message)s"
logging.basicConfig(level=logging.INFO, format=LOG_FORMAT, datefmt="%H:%M:%S")
logger = logging.getLogger("MultiExchangeListener")

# Set more detailed logging for debugging websocket issues
logging.getLogger("websockets").setLevel(logging.WARNING)  # Reduce websockets noise
logging.getLogger("aiohttp").setLevel(logging.WARNING)     # Reduce aiohttp noise

from realtime_data_manager import realtime_data_manager


# ==============================================================================
# SECTION 1: BYBIT LOGIC
# ==============================================================================

BYBIT_EXCHANGE_NAME = "Bybit"
BYBIT_WS_URL_DEMO = "wss://stream-demo.bybit.com/v5/private"
BYBIT_WS_URL_LIVE = "wss://stream.bybit.com/v5/private"

def get_bybit_auth_args(api_key: str, secret_key: str) -> list:
    expires = int((time.time() + 5) * 1000)
    signature_payload = f"GET/realtime{expires}".encode('utf-8')
    secret_key_bytes = secret_key.encode('utf-8')
    signature = hmac.new(secret_key_bytes, signature_payload, hashlib.sha256).hexdigest()
    return [api_key, expires, signature]

async def bybit_listener():
    """Listens to Bybit's private channels."""
    try:
        credentials = EXCHANGE_API_KEYS[BYBIT_EXCHANGE_NAME.lower()]
        api_key = credentials["api_key"]
        secret = credentials["secret"]
    except KeyError:
        logger.error(f"[{BYBIT_EXCHANGE_NAME}] API keys not found in api_keys.py.")
        return

    reconnect_attempt = 0
    while True:
        try:
            reconnect_attempt += 1
            logger.info(f"[{BYBIT_EXCHANGE_NAME}] Attempting to connect (attempt #{reconnect_attempt})...")
            async with websockets.connect(
                BYBIT_WS_URL_LIVE,
                ping_interval=18,
                ping_timeout=10
            ) as ws:
                logger.info(f"[{BYBIT_EXCHANGE_NAME}] WebSocket connection established.")

                # Authentication
                auth_args = get_bybit_auth_args(api_key, secret)
                await ws.send(json.dumps({"op": "auth", "args": auth_args}))
                auth_response = json.loads(await asyncio.wait_for(ws.recv(), timeout=10))
                if not auth_response.get("success"):
                    raise ConnectionError(f"Authentication Failed: {auth_response.get('ret_msg')}")
                logger.info(f"[{BYBIT_EXCHANGE_NAME}] Authentication successful.")

                # Subscription
                sub_req = {"op": "subscribe", "args": ["position.linear", "order.linear"]}
                await ws.send(json.dumps(sub_req))
                
                reconnect_attempt = 0 # Reset after successful connection
                logger.info(f"[{BYBIT_EXCHANGE_NAME}] Entering main message loop...")
                
                async for raw_msg in ws:
                    last_msg_time = time.time()
                    msg = json.loads(raw_msg)
                    if "topic" in msg:
                        if msg["topic"] == "position.linear":
                            logger.info(f"[{BYBIT_EXCHANGE_NAME}] <<< Position Update >>> : {msg.get('data')}")
                        elif msg["topic"] == "order.linear":
                            # msg.get('data')
                            orders = msg.get('data', [])
                            logger.info(f"[{BYBIT_EXCHANGE_NAME}] ### Order Update ### : {orders}")
                            for order in orders:
                                symbol = order.get('symbol')
                                side = order.get('side')
                                filled_qty = order.get('cumExecQty') # cumExecQty 是累计成交数量
                                
                                logger.info(f"[{BYBIT_EXCHANGE_NAME}] 时间差: {float(order.get('updatedTime'))/1000 - last_msg_time}")
                                if all([symbol, side, filled_qty]):
                                    realtime_data_manager.update_order_fill(BYBIT_EXCHANGE_NAME, symbol, side, filled_qty)
                                    # asyncio.sleep(0.2)
                                    # open_long_order = WsSignal(
                                    #     instId=symbol,
                                    #     tdMode="cross",      # 仓位模式: 逐仓 'isolated' 或 全仓 'cross'
                                    #     side="Sell" if side == "Buy" else "Buy",          # 动作: 'buy' 或 'sell'
                                    #     ordType="market",    # 订单类型: 'market' (市价), 'limit' (限价)
                                    #     sz=float(filled_qty),             # 数量
                                    #     reduceOnly = 'true',
                                    #     action='close',
                                    # )
                                    # bybit_place_order(open_long_order)
                            logger.info(f"[{BYBIT_EXCHANGE_NAME}] ### Order Update ### : {msg.get('data')}")
        
        except websockets.exceptions.ConnectionClosedError as e:
            logger.error(f"[{BYBIT_EXCHANGE_NAME}] Connection closed unexpectedly: {e}")
        except ConnectionError as e:
            logger.error(f"[{BYBIT_EXCHANGE_NAME}] Connection error: {e}")
        except asyncio.CancelledError:
            logger.info(f"[{BYBIT_EXCHANGE_NAME}] Task cancelled.")
            break
        except Exception as e:
            logger.error(f"[{BYBIT_EXCHANGE_NAME}] An unexpected error occurred ({type(e).__name__}): {e}", exc_info=False)
        
        delay = min(60, (2 ** reconnect_attempt)) + random.random()
        logger.info(f"[{BYBIT_EXCHANGE_NAME}] Reconnecting in {delay:.2f} seconds...")
        await asyncio.sleep(delay)


# ==============================================================================
# SECTION 2: OKX LOGIC
# ==============================================================================

OKX_EXCHANGE_NAME = "Okx"
OKX_URL_PRIVATE_PAPER = "wss://wspap.okx.com:8443/ws/v5/private"
OKX_URL_PRIVATE_LIVE = "wss://ws.okx.com:8443/ws/v5/private"

def handle_okx_message(s: str):
    """Callback function to process messages from the OKX SDK."""
    try:
        data = json.loads(s)
    except json.JSONDecodeError:
        logger.warning(f"[{OKX_EXCHANGE_NAME}] Received non-JSON message: {s}")
        return

    if "event" in data:
        if data["event"] == "subscribe": logger.info(f"[{OKX_EXCHANGE_NAME}] ✅ Subscribed to: {data.get('arg')}")
        elif data["event"] == "login": logger.info(f"[{OKX_EXCHANGE_NAME}] ✅ Authentication successful.")
        elif data["event"] == "error": logger.error(f"[{OKX_EXCHANGE_NAME}] ❌ Received error: {data}")
    elif "arg" in data and "channel" in data["arg"]:
        channel = data["arg"]["channel"]
        if channel == "positions":
            logger.info(f"[{OKX_EXCHANGE_NAME}] <<< Position Update >>> : {data.get('data', [])}")
        elif channel == "orders":
            orders = data.get('data', [])
            logger.info(f"[{OKX_EXCHANGE_NAME}] ### Order Update ### : {orders}")
            for order in orders:
                # OKX的symbol格式为 BTC-USDT-SWAP, 我们将其标准化
                symbol = order.get('instId', '').replace('-USDT-SWAP', 'USDT')
                side = order.get('side')
                filled_qty = order.get('accFillSz') # accFillSz 是累计成交数量
                
                if all([symbol, side, filled_qty]):
                    realtime_data_manager.update_order_fill(OKX_EXCHANGE_NAME, symbol, side, filled_qty)

async def okx_listener():
    """Listens to OKX's private channels using its SDK."""
    try:
        credentials = EXCHANGE_API_KEYS[OKX_EXCHANGE_NAME.lower()]
        api_key, secret, passphrase = credentials["api_key"], credentials["secret"], credentials["passphrase"]
    except KeyError:
        logger.error(f"[{OKX_EXCHANGE_NAME}] API keys not found in api_keys.py.")
        return

    reconnect_attempt = 0
    while True:
        ws_client = None
        try:
            reconnect_attempt += 1
            logger.info(f"[{OKX_EXCHANGE_NAME}] Attempting to connect (attempt #{reconnect_attempt})...")
            
            subscription_args = [
                {"channel": "positions", "instType": "SWAP", "extraParams": json.dumps({"updateInterval": "0"})},
                {"channel": "orders", "instType": "SWAP"}
            ]
            
            ws_client = OkxSocketClient(apikey=api_key, apisecret=secret, passphrase=passphrase, private_url=OKX_URL_PRIVATE_LIVE)
            await ws_client.private.start()
            await ws_client.private.subscribe(subscription_args, callback=handle_okx_message)
            
            reconnect_attempt = 0 # Reset after successful connection
            logger.info(f"[{OKX_EXCHANGE_NAME}] Entering main message loop (via SDK)...")
            
            while True: await asyncio.sleep(3600) # Keep the task alive

        except asyncio.CancelledError:
            logger.info(f"[{OKX_EXCHANGE_NAME}] Task cancelled.")
            break
        except Exception as e:
            logger.error(f"[{OKX_EXCHANGE_NAME}] An unexpected error occurred ({type(e).__name__}): {e}", exc_info=False)
        finally:
            if ws_client: await ws_client.private.close()

        delay = min(60, (2 ** reconnect_attempt)) + random.random()
        logger.info(f"[{OKX_EXCHANGE_NAME}] Reconnecting in {delay:.2f} seconds...")
        await asyncio.sleep(delay)


# ==============================================================================
# SECTION 3: BITGET LOGIC (FINAL ROBUST VERSION)
# ==============================================================================

BITGET_EXCHANGE_NAME = "Bitget"
BITGET_WS_URL_PAPER = "wss://wspap.bitget.com/v2/ws/private"
BITGET_WS_URL_LIVE = "wss://ws.bitget.com/v2/ws/private"

# Proactive ping interval: We will send a ping if we hear nothing for this long.
BITGET_PING_INTERVAL_S = 25
# Inactivity timeout: Final safety net if pings fail or server is unresponsive.
BITGET_INACTIVITY_TIMEOUT_S = 75

def get_bitget_auth_args(api_key: str, secret_key: str, passphrase: str) -> list:
    timestamp = str(int(time.time()))
    pre_hash_string = f"{timestamp}GET/user/verify"
    mac = hmac.new(secret_key.encode('utf-8'), pre_hash_string.encode('utf-8'), hashlib.sha256)
    signature = base64.b64encode(mac.digest()).decode('utf-8')
    return [{"apiKey": api_key, "passphrase": passphrase, "timestamp": timestamp, "sign": signature}]

async def bitget_listener():
    """Listens to Bitget's private channels with proactive heartbeats."""
    try:
        credentials = EXCHANGE_API_KEYS[BITGET_EXCHANGE_NAME.lower()]
        api_key, secret, passphrase = credentials["api_key"], credentials["secret"], credentials["passphrase"]
    except KeyError:
        logger.error(f"[{BITGET_EXCHANGE_NAME}] API keys not found in api_keys.py.")
        return

    reconnect_attempt = 0
    while True:
        ws: websockets.client.WebSocketClientProtocol | None = None
        connection_established_time = 0.0
        try:
            reconnect_attempt += 1
            logger.info(f"[{BITGET_EXCHANGE_NAME}] Attempting to connect (attempt #{reconnect_attempt})...")
            
            # We set a long timeout on the connection itself
            async with websockets.connect(BITGET_WS_URL_LIVE, open_timeout=30, close_timeout=10) as ws:
                connection_established_time = time.time()
                logger.info(f"[{BITGET_EXCHANGE_NAME}] WebSocket connection established successfully.")

                # Authentication
                auth_args = get_bitget_auth_args(api_key, secret, passphrase)
                await ws.send(json.dumps({"op": "login", "args": auth_args}))
                auth_response = json.loads(await asyncio.wait_for(ws.recv(), timeout=10))
                if auth_response.get("event") != "login" or auth_response.get("code") != 0:
                    raise ConnectionError(f"Authentication Failed: {auth_response.get('msg')}")
                logger.info(f"[{BITGET_EXCHANGE_NAME}] Authentication successful.")

                # Subscription
                sub_req = {"op": "subscribe", "args": [
                    {"instType": "USDT-FUTURES", "channel": "positions", "instId": "default"},
                    {"instType": "USDT-FUTURES", "channel": "orders", "instId": "default"}
                ]}
                await ws.send(json.dumps(sub_req))
                
                reconnect_attempt = 0
                logger.info(f"[{BITGET_EXCHANGE_NAME}] Entering main message loop...")
                last_message_time = time.time()

                while True:
                    try:
                        # Wait for a message, but with our shorter proactive ping timeout
                        raw_msg = await asyncio.wait_for(ws.recv(), timeout=BITGET_PING_INTERVAL_S)
                        last_message_time = time.time() # Update time on ANY message received

                        if raw_msg == 'pong':
                            logger.info(f"[{BITGET_EXCHANGE_NAME}] <- RECV: Pong")
                            continue
                        
                        # The server might still send its own ping, we must reply
                        if raw_msg == 'ping':
                            await ws.send('pong')
                            logger.info(f"[{BITGET_EXCHANGE_NAME}] <- RECV: ping, -> SENT: pong")
                            continue
                        
                        msg = json.loads(raw_msg)
                        # ... (message processing logic remains the same) ...
                        if "event" in msg:
                            if msg["event"] == "subscribe": logger.info(f"[{BITGET_EXCHANGE_NAME}] ✅ Subscribed to: {msg.get('arg')}")
                            elif msg["event"] == "error": logger.error(f"[{BITGET_EXCHANGE_NAME}] ❌ Received error: {msg}")
                        elif "action" in msg:
                            channel = msg.get("arg", {}).get("channel")
                            action = msg.get("action")
                            if channel == "positions":
                                logger.info(f"[{BITGET_EXCHANGE_NAME}] <<< Position Update ({action}) >>> : {msg.get('data', [])}")
                            elif channel == "orders":
                                orders = msg.get('data', [])
                                logger.info(f"[{BITGET_EXCHANGE_NAME}] ### Order Update ({action}) ### : {orders}")
                                for order in orders:
                                    if order.get('status') in ['filled', 'partial_fill']:
                                        symbol, side, filled_qty = order.get('instId'), order.get('side'), order.get('accBaseVolume')
                                        if all([symbol, side, filled_qty]):
                                            logger.info(f"[{BITGET_EXCHANGE_NAME}] 时间差: {float(order.get('uTime'))/1000 - last_message_time}")
                                            realtime_data_manager.update_order_fill(BITGET_EXCHANGE_NAME, symbol, side, filled_qty)
                                            # asyncio.sleep(0.2)
                                            # open_long_order = WsSignal(
                                            #     instId=symbol,
                                            #     tdMode="cross",      # 仓位模式: 逐仓 'isolated' 或 全仓 'cross'
                                            #     side="sell" if side == "buy" else "buy",          # 动作: 'buy' 或 'sell'
                                            #     ordType="market",    # 订单类型: 'market' (市价), 'limit' (限价)
                                            #     sz=float(filled_qty),             # 数量
                                            #     reduceOnly = 'true',
                                            #     action='close',
                                            # )
                                            # bitget_place_order(open_long_order)

                    except asyncio.TimeoutError:
                        # Our 25s timer expired, so it's time to send our own ping
                        # logger.info(f"[{BITGET_EXCHANGE_NAME}] No message received for {BITGET_PING_INTERVAL_S}s. Sending proactive ping.")
                        await ws.send('ping')
                        # We expect a pong back quickly
                        await asyncio.wait_for(ws.recv(), timeout=10)


        except websockets.exceptions.ConnectionClosedError as e:
            logger.error(f"[{BITGET_EXCHANGE_NAME}] Connection closed unexpectedly. Code: {e.code}, Reason: '{e.reason}'")
        except ConnectionError as e:
            logger.error(f"[{BITGET_EXCHANGE_NAME}] Connection error: {e}")
        except asyncio.CancelledError:
            logger.info(f"[{BITGET_EXCHANGE_NAME}] Task cancelled.")
            break
        except Exception as e:
            logger.error(f"[{BITGET_EXCHANGE_NAME}] An unexpected error occurred ({type(e).__name__}): {e}", exc_info=True)
        
        finally:
            if connection_established_time > 0:
                uptime = time.time() - connection_established_time
                logger.info(f"[{BITGET_EXCHANGE_NAME}] Connection was stable for {uptime:.2f} seconds.")
            
            delay = min(60, (2 ** reconnect_attempt)) + random.random()
            logger.info(f"[{BITGET_EXCHANGE_NAME}] Reconnecting in {delay:.2f} seconds...")
            await asyncio.sleep(delay)

# ================== BINANCE REVISED SECTION ==================
import httpx
import hashlib
import hmac
from typing import Optional, Dict, Any

BINANCE_EXCHANGE_NAME = "Binance"
BINANCE_REST_URL_LIVE = "https://fapi.binance.com"
BINANCE_WS_URL_LIVE = "wss://fstream.binance.com/ws/"  # will append listenKey

# 如果需要测试网，切换：
# BINANCE_REST_URL_LIVE = "https://testnet.binancefuture.com"
# BINANCE_WS_URL_LIVE = "wss://stream.binancefuture.com/ws/"

# 可配置
BINANCE_KEEPALIVE_INTERVAL = 25 * 60          # 25 分钟刷新
BINANCE_NO_MESSAGE_WARN = 120                 # 120 秒内无任何消息则告警
BINANCE_LOG_RAW_MESSAGES = True               # 调试阶段设 True
BINANCE_MAX_LISTEN_KEY_RETRY = 5

def _binance_ts_ms():
    return int(time.time() * 1000)

def _binance_sign(params: Dict[str, Any], secret: str) -> str:
    qs = "&".join(f"{k}={params[k]}" for k in sorted(params))
    return hmac.new(secret.encode(), qs.encode(), hashlib.sha256).hexdigest()

async def _binance_get_listen_key(session: httpx.AsyncClient, api_key: str) -> Optional[str]:
    headers = {"X-MBX-APIKEY": api_key}
    for attempt in range(1, BINANCE_MAX_LISTEN_KEY_RETRY + 1):
        try:
            r = await session.post("/fapi/v1/listenKey", headers=headers)
            if r.status_code == 200:
                lk = r.json().get("listenKey")
                if lk:
                    logger.info(f"[{BINANCE_EXCHANGE_NAME}] Got listenKey={lk[:12]}...")
                    return lk
            logger.error(f"[{BINANCE_EXCHANGE_NAME}] listenKey attempt {attempt} failed: {r.status_code} {r.text}")
        except Exception as e:
            logger.error(f"[{BINANCE_EXCHANGE_NAME}] listenKey attempt {attempt} exception: {e}")
        await asyncio.sleep(1.5 * attempt)
    return None

async def _binance_keepalive_loop(session: httpx.AsyncClient, api_key: str, listen_key: str, stop_evt: asyncio.Event):
    headers = {"X-MBX-APIKEY": api_key}
    while not stop_evt.is_set():
        try:
            await asyncio.wait_for(stop_evt.wait(), timeout=BINANCE_KEEPALIVE_INTERVAL)
            if stop_evt.is_set():
                break
        except asyncio.TimeoutError:
            pass
        try:
            r = await session.put("/fapi/v1/listenKey", headers=headers, params={"listenKey": listen_key})
            if r.status_code == 200:
                logger.info(f"[{BINANCE_EXCHANGE_NAME}] Keepalive OK ({listen_key[:10]})")
            else:
                logger.warning(f"[{BINANCE_EXCHANGE_NAME}] Keepalive failed {r.status_code} {r.text}")
        except Exception as e:
            logger.error(f"[{BINANCE_EXCHANGE_NAME}] Keepalive exception: {e}")

async def _binance_place_test_order(session: httpx.AsyncClient, api_key: str, secret: str,
                                    symbol="BTCUSDT", side="BUY", order_type="LIMIT",
                                    qty="0.001", price="50000", tif="GTC"):
    """
    仅作为触发 ORDER_TRADE_UPDATE 的测试。请根据账户实际情况调整价格与数量。
    """
    endpoint = "/fapi/v1/order"
    headers = {"X-MBX-APIKEY": api_key}
    params = {
        "symbol": symbol,
        "side": side,
        "type": order_type,
        "quantity": qty,
        "price": price,
        "timeInForce": tif,
        "timestamp": _binance_ts_ms(),
        "recvWindow": 5000
    }
    params["signature"] = _binance_sign(params, secret)
    try:
        r = await session.post(endpoint, params=params, headers=headers)
        if r.status_code == 200:
            logger.info(f"[{BINANCE_EXCHANGE_NAME}] Test order placed: {r.json()}")
        else:
            logger.error(f"[{BINANCE_EXCHANGE_NAME}] Test order error {r.status_code} {r.text}")
    except Exception as e:
        logger.error(f"[{BINANCE_EXCHANGE_NAME}] Test order exception: {e}")

async def binance_listener():
    try:
        creds = EXCHANGE_API_KEYS[BINANCE_EXCHANGE_NAME.lower()]
        api_key = creds["api_key"]
        secret = creds.get("secret")  # 需要下单才用
    except KeyError:
        logger.error(f"[{BINANCE_EXCHANGE_NAME}] API keys not found.")
        return

    reconnect_attempt = 0
    # 使用持久 httpx 会话（base_url 避免重复写域名）
    async with httpx.AsyncClient(base_url=BINANCE_REST_URL_LIVE, timeout=10.0) as http_session:
        while True:
            listen_key = None
            keepalive_task = None
            stop_evt = asyncio.Event()
            last_msg_time = time.time()
            event_counters = {
                "TOTAL_RAW": 0,
                "ORDER_TRADE_UPDATE": 0,
                "ACCOUNT_UPDATE": 0,
                "OTHER": 0
            }

            try:
                reconnect_attempt += 1
                logger.info(f"[{BINANCE_EXCHANGE_NAME}] Connecting attempt #{reconnect_attempt}")

                listen_key = await _binance_get_listen_key(http_session, api_key)
                if not listen_key:
                    raise ConnectionError("Failed to obtain listenKey after retries.")

                ws_url = BINANCE_WS_URL_LIVE + listen_key
                logger.info(f"[{BINANCE_EXCHANGE_NAME}] WebSocket URL: {ws_url}")

                async with websockets.connect(
                    ws_url,
                    ping_interval=20,
                    ping_timeout=10,
                    close_timeout=5
                ) as ws:
                    logger.info(f"[{BINANCE_EXCHANGE_NAME}] WebSocket connected.")
                    reconnect_attempt = 0

                    # 启动 keepalive
                    keepalive_task = asyncio.create_task(_binance_keepalive_loop(http_session, api_key, listen_key, stop_evt))

                    # 可选：启动一次测试下单（只在第一次连接）
                    # if secret and reconnect_attempt == 0:
                    #     asyncio.create_task(_binance_place_test_order(http_session, api_key, secret))

                    # 监控“长时间无任何消息”
                    async def watchdog():
                        while not stop_evt.is_set():
                            await asyncio.sleep(10)
                            if time.time() - last_msg_time > BINANCE_NO_MESSAGE_WARN:
                                logger.warning(f"[{BINANCE_EXCHANGE_NAME}] {BINANCE_NO_MESSAGE_WARN}s no messages. "
                                               f"Check: (1) 是否下单 (2) API权限 (3) 是否交易同一账户 (4) 是否正确 USDT-M/COIN-M.")
                                last_msg_time = time.time()
                    wd_task = asyncio.create_task(watchdog())

                    async for raw in ws:
                        last_msg_time = time.time()
                        event_counters["TOTAL_RAW"] += 1

                        # 原始消息调试
                        if BINANCE_LOG_RAW_MESSAGES:
                            logger.info(f"[{BINANCE_EXCHANGE_NAME}] RAW: {raw}")

                        try:
                            msg = json.loads(raw)
                        except json.JSONDecodeError:
                            logger.error(f"[{BINANCE_EXCHANGE_NAME}] JSON decode error: {raw}")
                            continue

                        et = msg.get("e")
                        if et == "ORDER_TRADE_UPDATE":
                            event_counters["ORDER_TRADE_UPDATE"] += 1
                            logger.info(f"[{BINANCE_EXCHANGE_NAME}] 时间差: {float(msg['o']['T'])/1000 - last_msg_time}")
                            await _handle_binance_order_update(msg)  # 复用你原来的
                        elif et == "ACCOUNT_UPDATE":
                            event_counters["ACCOUNT_UPDATE"] += 1
                            await _handle_binance_account_update(msg)
                        elif et == "listenKeyExpired":
                            logger.warning(f"[{BINANCE_EXCHANGE_NAME}] listenKeyExpired received, will reconnect.")
                            break
                        else:
                            event_counters["OTHER"] += 1
                            logger.info(f"[{BINANCE_EXCHANGE_NAME}] Other event type={et} msg={msg}")

                    wd_task.cancel()
                    try:
                        await wd_task
                    except asyncio.CancelledError:
                        pass

            except asyncio.CancelledError:
                logger.info(f"[{BINANCE_EXCHANGE_NAME}] Task cancelled.")
                break
            except Exception as e:
                logger.error(f"[{BINANCE_EXCHANGE_NAME}] Exception: {type(e).__name__} {e}", exc_info=False)
            finally:
                stop_evt.set()
                if keepalive_task and not keepalive_task.done():
                    keepalive_task.cancel()
                    try:
                        await keepalive_task
                    except asyncio.CancelledError:
                        pass
                logger.info(f"[{BINANCE_EXCHANGE_NAME}] Event counters: {event_counters}")

            delay = min(15, (2 ** reconnect_attempt)) + random.random()
            logger.info(f"[{BINANCE_EXCHANGE_NAME}] Reconnecting in {delay:.2f}s...")
            await asyncio.sleep(delay)


async def _binance_keepalive_task(api_key: str, listen_key: str):
    """
    Background task to keep the listenKey alive by sending PUT requests every 30 minutes.
    """
    try:
        while True:
            await asyncio.sleep(30 * 60)  # Wait 30 minutes
            success = await _keepalive_binance_listen_key(api_key, listen_key)
            if not success:
                logger.warning(f"[{BINANCE_EXCHANGE_NAME}] Failed to extend listenKey, connection may expire soon")
    except asyncio.CancelledError:
        logger.debug(f"[{BINANCE_EXCHANGE_NAME}] Keepalive task cancelled")


async def _handle_binance_order_update(msg: dict):
    """Handle ORDER_TRADE_UPDATE events from Binance."""
    try:
        order_data = msg.get("o", {})

        # Log the full order data for debugging
        logger.info(f"[{BINANCE_EXCHANGE_NAME}] ### Order Update ### : {order_data}")

        symbol = order_data.get("s")
        side = order_data.get("S")  # 'BUY' or 'SELL'
        filled_qty = order_data.get("z")  # Cumulative filled quantity
        execution_type = order_data.get("x")  # Execution type
        order_status = order_data.get("X")  # Order status

        # Log key order information
        logger.info(f"[{BINANCE_EXCHANGE_NAME}] Order: {symbol} {side} | Status: {order_status} | Exec: {execution_type} | Filled: {filled_qty}")

        # Only update if we have valid data and there's actual fill
        if all([symbol, side, filled_qty]) and float(filled_qty) > 0:
            realtime_data_manager.update_order_fill(BINANCE_EXCHANGE_NAME, symbol, side, filled_qty)
            logger.info(f"[{BINANCE_EXCHANGE_NAME}] Updated order fill: {symbol} {side} {filled_qty}")

    except Exception as e:
        logger.error(f"[{BINANCE_EXCHANGE_NAME}] Error handling order update: {e}")


async def _handle_binance_account_update(msg: dict):
    """Handle ACCOUNT_UPDATE events from Binance."""
    try:
        update_data = msg.get("a", {})
        positions = update_data.get("P", [])
        balances = update_data.get("B", [])

        if positions:
            logger.info(f"[{BINANCE_EXCHANGE_NAME}] <<< Position Update >>> : {positions}")

        if balances:
            logger.debug(f"[{BINANCE_EXCHANGE_NAME}] Balance Update: {balances}")

    except Exception as e:
        logger.error(f"[{BINANCE_EXCHANGE_NAME}] Error handling account update: {e}")


async def _test_binance_api_key(api_key: str) -> bool:
    """Test if the Binance API key is valid by making a simple request."""
    endpoint = "/fapi/v2/account"
    url = BINANCE_REST_URL_LIVE + endpoint
    headers = {'X-MBX-APIKEY': api_key}

    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(url, headers=headers) as response:
                if response.status == 200:
                    logger.info(f"[{BINANCE_EXCHANGE_NAME}] API key validation successful")
                    return True
                elif response.status == 401:
                    logger.error(f"[{BINANCE_EXCHANGE_NAME}] API key is invalid or unauthorized")
                    return False
                else:
                    logger.warning(f"[{BINANCE_EXCHANGE_NAME}] API key test returned status: {response.status}")
                    return False
    except Exception as e:
        logger.error(f"[{BINANCE_EXCHANGE_NAME}] Error testing API key: {e}")
        return False


# ==============================================================================
# SECTION 4: MAIN EXECUTION
# ==============================================================================

async def shutdown(tasks: list):
    logger.info("Shutdown signal received, cancelling all tasks...")
    for task in tasks:
        task.cancel()
    await asyncio.gather(*tasks, return_exceptions=True)
    logger.info("All tasks have been cancelled.")

async def main():
    """Orchestrates all exchange listeners."""
    logger.info("Initializing multi-exchange listener...")
    
    # Create a task for each exchange listener
    bybit_task = asyncio.create_task(bybit_listener())
    okx_task = asyncio.create_task(okx_listener())
    bitget_task = asyncio.create_task(bitget_listener())
    binance_task = asyncio.create_task(binance_listener())

    all_tasks = [bybit_task, okx_task, bitget_task, binance_task]
    # all_tasks = [binance_task]
    
    # Set up signal handlers for graceful shutdown
    loop = asyncio.get_running_loop()
    for sig in (signal.SIGINT, signal.SIGTERM):
        loop.add_signal_handler(sig, lambda: asyncio.create_task(shutdown(all_tasks)))

    logger.info("All listeners are running. Press Ctrl+C to exit.")
    await asyncio.gather(*all_tasks)

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("Main program interrupted.")