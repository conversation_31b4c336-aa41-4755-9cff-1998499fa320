"""
API密钥配置文件示例
请复制此文件为 api_keys.py 并填入真实的API密钥
注意：api_keys.py 文件不应提交到git仓库中
"""

# 实盘交易API配置
EXCHANGE_API_KEYS = {
    'binance': {
        'api_key': '',  # 请填入Binance API Key
        'secret': '',   # 请填入Binance Secret
    },
    'bybit': {
        'api_key': '',  # 请填入Bybit API Key
        'secret': '',   # 请填入Bybit Secret
    },
    'okx': {
        'api_key': '',     # 请填入OKX API Key
        'secret': '',      # 请填入OKX Secret
        'passphrase': '',  # 请填入OKX Passphrase（交易密码）
    },
    'bitget': {
        'api_key': '',     # 请填入Bitget API Key
        'secret': '',      # 请填入Bitget Secret
        'passphrase': '',  # 请填入Bitget Passphrase（交易密码）
    }
}

# 可选：使用环境变量（推荐用于生产环境）
import os

# 如果设置了环境变量，优先使用环境变量
if os.getenv('BINANCE_API_KEY'):
    EXCHANGE_API_KEYS['binance']['api_key'] = os.getenv('BINANCE_API_KEY')
    EXCHANGE_API_KEYS['binance']['secret'] = os.getenv('BINANCE_SECRET')

if os.getenv('BYBIT_API_KEY'):
    EXCHANGE_API_KEYS['bybit']['api_key'] = os.getenv('BYBIT_API_KEY')
    EXCHANGE_API_KEYS['bybit']['secret'] = os.getenv('BYBIT_SECRET')

if os.getenv('OKX_API_KEY'):
    EXCHANGE_API_KEYS['okx']['api_key'] = os.getenv('OKX_API_KEY')
    EXCHANGE_API_KEYS['okx']['secret'] = os.getenv('OKX_SECRET')
    EXCHANGE_API_KEYS['okx']['passphrase'] = os.getenv('OKX_PASSPHRASE')

if os.getenv('BITGET_API_KEY'):
    EXCHANGE_API_KEYS['bitget']['api_key'] = os.getenv('BITGET_API_KEY')
    EXCHANGE_API_KEYS['bitget']['secret'] = os.getenv('BITGET_SECRET')
