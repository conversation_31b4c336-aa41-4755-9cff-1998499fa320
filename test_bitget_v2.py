#!/usr/bin/env python3
"""
测试 Bitget v2 API 实现
"""

from logging_setup import setup_loggers
import logging

# 设置日志
setup_loggers()
decision_logger = logging.getLogger('decision_logger')
output_logger = logging.getLogger('output_logger')

def test_bitget_v2_availability():
    """测试 Bitget v2 API 是否可用"""
    print("=== 测试 Bitget v2 API 可用性 ===")
    
    try:
        import bitget.v1.mix.order_api as bitgetOrderApi
        import bitget.v1.mix.account_api as bitgetAccountApi
        import bitget.v1.mix.market_api as bitgetMarketApi
        import bitget.bitget_api as bitgetBaseApi
        print("✅ Bitget v2 API 库导入成功")
        return True
    except ImportError as e:
        print(f"❌ Bitget v2 API 库导入失败: {e}")
        print("请安装: pip install python-bitget")
        return False

def test_bitget_v1_vs_v2():
    """比较 v1 和 v2 API 的差异"""
    print("\n=== 测试 Bitget v1 vs v2 ===")
    
    try:
        from exchange import Bitgetv1HTTP, BitgetV2HTTP
        from api_keys import EXCHANGE_API_KEYS
        
        api_key = EXCHANGE_API_KEYS['bitget']['api_key']
        api_secret = EXCHANGE_API_KEYS['bitget']['secret']
        api_passphrase = EXCHANGE_API_KEYS['bitget']['passphrase']
        
        # 测试 v1 API
        print("\n--- 测试 Bitget v1 API ---")
        try:
            bitget_v1 = Bitgetv1HTTP('Bitget', api_key=api_key, api_secret=api_secret, passphrase=api_passphrase)
            v1_connected = bitget_v1.connect()
            print(f"v1 连接状态: {v1_connected}")
            
            if v1_connected:
                equity_v1, balance_v1 = bitget_v1.get_balance_info()
                print(f"v1 余额: 总资产={equity_v1}, 可用余额={balance_v1}")
                
                min_qty_v1, max_qty_v1, ct_val_v1 = bitget_v1.get_symbol_info('BTCUSDT')
                print(f"v1 交易对信息: min={min_qty_v1}, max={max_qty_v1}, ct_val={ct_val_v1}")
        except Exception as e:
            print(f"v1 API 测试失败: {e}")
        
        # 测试 v2 API
        print("\n--- 测试 Bitget v2 API ---")
        try:
            bitget_v2 = BitgetV2HTTP('Bitget', api_key=api_key, api_secret=api_secret, passphrase=api_passphrase)
            v2_connected = bitget_v2.connect()
            print(f"v2 连接状态: {v2_connected}")
            
            if v2_connected:
                equity_v2, balance_v2 = bitget_v2.get_balance_info()
                print(f"v2 余额: 总资产={equity_v2}, 可用余额={balance_v2}")
                
                min_qty_v2, max_qty_v2, ct_val_v2 = bitget_v2.get_symbol_info('BTCUSDT')
                print(f"v2 交易对信息: min={min_qty_v2}, max={max_qty_v2}, ct_val={ct_val_v2}")
        except Exception as e:
            print(f"v2 API 测试失败: {e}")
            
    except Exception as e:
        print(f"比较测试失败: {e}")

def test_bitget_v2_endpoints():
    """测试 Bitget v2 API 端点"""
    print("\n=== 测试 Bitget v2 API 端点 ===")
    
    if not test_bitget_v2_availability():
        print("跳过端点测试，v2 API 不可用")
        return
    
    try:
        import bitget.bitget_api as bitgetBaseApi
        from api_keys import EXCHANGE_API_KEYS
        
        api_key = EXCHANGE_API_KEYS['bitget']['api_key']
        api_secret = EXCHANGE_API_KEYS['bitget']['secret']
        api_passphrase = EXCHANGE_API_KEYS['bitget']['passphrase']
        
        base_api = bitgetBaseApi.BitgetApi(api_key, api_secret, api_passphrase)
        
        # 测试合约信息端点
        print("测试合约信息端点...")
        contracts_response = base_api.get("/api/v2/mix/market/contracts", {"productType": "USDT-FUTURES"})
        print(f"合约信息响应: {contracts_response.get('code', 'unknown')}")
        
        # 测试账户信息端点
        print("测试账户信息端点...")
        account_response = base_api.get("/api/v2/mix/account/accounts", {"productType": "USDT-FUTURES"})
        print(f"账户信息响应: {account_response.get('code', 'unknown')}")
        
        # 测试订单簿端点
        print("测试订单簿端点...")
        orderbook_response = base_api.get("/api/v2/mix/market/orderbook", {
            "symbol": "BTCUSDT_UMCBL",
            "productType": "USDT-FUTURES",
            "limit": "15"
        })
        print(f"订单簿响应: {orderbook_response.get('code', 'unknown')}")
        
    except Exception as e:
        print(f"端点测试失败: {e}")

def main():
    """主测试函数"""
    print("=== Bitget v2 API 测试 ===\n")
    
    # 测试API可用性
    v2_available = test_bitget_v2_availability()
    
    if v2_available:
        # 比较v1和v2
        test_bitget_v1_vs_v2()
        
        # 测试v2端点
        test_bitget_v2_endpoints()
    else:
        print("\n⚠️ Bitget v2 API 不可用")
        print("当前将使用 v1 API 作为后备方案")
    
    print("\n=== 测试完成 ===")
    print("现在可以运行: python trader.py")

if __name__ == "__main__":
    main()
