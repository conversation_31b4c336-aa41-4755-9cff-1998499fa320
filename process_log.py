{"cells": [{"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import re\n", "import json"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["def parse_trade_log_final(log_file_path, output_csv_path):\n", "    \"\"\"\n", "    解析包含 open 和 close 动作的交易日志。\n", "    脚本从 real.buy_record 和 real.sell_record 中提取精确信息。\n", "    \"\"\"\n", "    try:\n", "        with open(log_file_path, 'r', encoding='utf-8') as f:\n", "            content = f.read()\n", "    except FileNotFoundError:\n", "        print(f\"错误：找不到文件 '{log_file_path}'。\")\n", "        return\n", "\n", "    log_entries = re.split(r'\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2},\\d{3}', content)\n", "\n", "    parsed_records = []\n", "    for entry in log_entries:\n", "        if not entry.strip():\n", "            continue\n", "\n", "        json_start_index = entry.find('{')\n", "        if json_start_index != -1:\n", "            json_str = entry[json_start_index:]\n", "            try:\n", "                data = json.loads(json_str)\n", "                \n", "                real_data = data.get('real', {})\n", "                buy_record = real_data.get('buy_record', {})\n", "                sell_record = real_data.get('sell_record', {})\n", "\n", "                record = {\n", "                    'trade_id': data.get('trade_id'),\n", "                    'action': data.get('action'),\n", "                    # 兼容 time_stamp 和 timestamp 两种字段名\n", "                    'timestamp': data.get('time_stamp', data.get('timestamp')),\n", "                    'symbol': buy_record.get('symbol'),\n", "                    'exec_qty': buy_record.get('exec_qty'),\n", "                    'buy_exchange': buy_record.get('exchange'),\n", "                    'avg_buy_price': buy_record.get('avg_price'),\n", "                    'buy_exec_fee': buy_record.get('exec_fee'),\n", "                    'sell_exchange': sell_record.get('exchange'),\n", "                    'avg_sell_price': sell_record.get('avg_price'),\n", "                    'sell_exec_fee': sell_record.get('exec_fee'),\n", "                }\n", "                parsed_records.append(record)\n", "            except (json.<PERSON><PERSON>, TypeError):\n", "                print(f\"警告：无法解析以下文本块，已跳过：\\n{json_str[:200]}...\")\n", "\n", "    if not parsed_records:\n", "        print(\"未在日志文件中找到任何有效的交易记录。\")\n", "        return\n", "\n", "    df = pd.DataFrame(parsed_records)\n", "    df['datetime'] = pd.to_datetime(df['timestamp'], unit='s', errors='coerce')\n", "    \n", "    cols = ['datetime', 'trade_id', 'action', 'timestamp', 'symbol', 'exec_qty',\n", "            'buy_exchange', 'avg_buy_price', 'buy_exec_fee',\n", "            'sell_exchange', 'avg_sell_price', 'sell_exec_fee']\n", "    \n", "    existing_cols = [col for col in cols if col in df.columns]\n", "    df = df[existing_cols]\n", "\n", "    df.to_csv(output_csv_path, index=False, encoding='utf-8-sig')\n", "    print(f\"解析完成！数据已保存到 '{output_csv_path}'。\")\n", "    print(\"\\n数据预览：\")\n", "    print(df)\n"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["解析完成！数据已保存到 'trade_history_parsed_new2.csv'。\n", "\n", "数据预览：\n", "                          datetime                              trade_id  \\\n", "0    2025-09-02 12:03:04.667346478  ecc6f86b-6c42-4973-900d-2d0727ea072a   \n", "1    2025-09-02 12:06:03.630314350  848a39af-13c1-448a-959d-655a3a3cf985   \n", "2    2025-09-02 12:53:42.016877174  d3314602-c6c1-4cde-8ea2-3d1885746e1b   \n", "3    2025-09-02 12:53:42.231836081  d3314602-c6c1-4cde-8ea2-3d1885746e1b   \n", "4    2025-09-03 06:40:35.429117441  c1b63d75-5d71-4a1f-81b9-6cba11423dff   \n", "...                            ...                                   ...   \n", "1120 2025-09-09 09:05:58.023970127  73bcce9a-7a84-454a-ae7a-2fd92ac6b387   \n", "1121 2025-09-09 09:06:09.323088646  92d68dd7-6910-4fd5-b2ac-daf074afca58   \n", "1122 2025-09-09 09:06:20.052723885  b94f55ab-4f98-46cf-83ff-f5d20c914fdf   \n", "1123 2025-09-09 09:10:31.839989901  b94f55ab-4f98-46cf-83ff-f5d20c914fdf   \n", "1124 2025-09-09 09:10:32.278972626  b46ea503-bbc2-4516-b44f-2e387df89972   \n", "\n", "     action     timestamp     symbol  exec_qty buy_exchange avg_buy_price  \\\n", "0      open  1.756815e+09    IMXUSDT      16.0          Okx        0.5134   \n", "1      open  1.756815e+09  PARTIUSDT      43.0      Binance        0.1856   \n", "2      open  1.756818e+09    LRCUSDT      76.0      Binance       0.10833   \n", "3     close  1.756818e+09    LRCUSDT      76.0      Binance       0.10838   \n", "4      open  1.756882e+09    NILUSDT      32.0      Binance        0.2534   \n", "...     ...           ...        ...       ...          ...           ...   \n", "1120   open  1.757409e+09  KAITOUSDT       4.0      Binance        1.5289   \n", "1121   open  1.757409e+09  KAITOUSDT       4.0      Binance        1.5103   \n", "1122   open  1.757409e+09  KAITOUSDT       0.0       Bitget             0   \n", "1123  close  1.757409e+09  KAITOUSDT       4.0       Bitget         1.481   \n", "1124   open  1.757409e+09  KAITOUSDT       0.0      Binance           0.0   \n", "\n", "      buy_exec_fee sell_exchange avg_sell_price  sell_exec_fee  \n", "0         0.004107       Binance         0.5135       0.000000  \n", "1         0.000000        Bitget         0.1857       0.004791  \n", "2         0.000000         Bybit        0.10833       0.004528  \n", "3         0.000000         Bybit        0.10843       0.004532  \n", "4         0.000000         Bybit         0.2539       0.004469  \n", "...            ...           ...            ...            ...  \n", "1120      0.000000         Bybit              0       0.000000  \n", "1121      0.000000         Bybit              0       0.000000  \n", "1122      0.000000         Bybit              0       0.000000  \n", "1123      0.003554         Bybit         1.4891       0.003276  \n", "1124      0.000000         Bybit              0       0.000000  \n", "\n", "[1125 rows x 12 columns]\n"]}], "source": ["parse_trade_log_final('trade_history.log','trade_history_parsed_new2.csv')"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>datetime</th>\n", "      <th>trade_id</th>\n", "      <th>action</th>\n", "      <th>timestamp</th>\n", "      <th>symbol</th>\n", "      <th>exec_qty</th>\n", "      <th>buy_exchange</th>\n", "      <th>avg_buy_price</th>\n", "      <th>buy_exec_fee</th>\n", "      <th>sell_exchange</th>\n", "      <th>avg_sell_price</th>\n", "      <th>sell_exec_fee</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2025-09-02 12:53:42.016877174</td>\n", "      <td>d3314602-c6c1-4cde-8ea2-3d1885746e1b</td>\n", "      <td>open</td>\n", "      <td>1.756818e+09</td>\n", "      <td>LRCUSDT</td>\n", "      <td>76.0</td>\n", "      <td>Binance</td>\n", "      <td>0.10833</td>\n", "      <td>0.000000</td>\n", "      <td>Bybit</td>\n", "      <td>0.10833</td>\n", "      <td>0.004528</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2025-09-02 12:53:42.231836081</td>\n", "      <td>d3314602-c6c1-4cde-8ea2-3d1885746e1b</td>\n", "      <td>close</td>\n", "      <td>1.756818e+09</td>\n", "      <td>LRCUSDT</td>\n", "      <td>76.0</td>\n", "      <td>Binance</td>\n", "      <td>0.10838</td>\n", "      <td>0.000000</td>\n", "      <td>Bybit</td>\n", "      <td>0.10843</td>\n", "      <td>0.004532</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2025-09-04 05:46:40.901727200</td>\n", "      <td>39736129-04d7-47fd-bb7c-f417b247152d</td>\n", "      <td>open</td>\n", "      <td>1.756965e+09</td>\n", "      <td>SPKUSDT</td>\n", "      <td>126.0</td>\n", "      <td>Binance</td>\n", "      <td>0.06437</td>\n", "      <td>0.000000</td>\n", "      <td>Bitget</td>\n", "      <td>0.06429</td>\n", "      <td>0.004861</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2025-09-04 05:46:41.946900368</td>\n", "      <td>39736129-04d7-47fd-bb7c-f417b247152d</td>\n", "      <td>close</td>\n", "      <td>1.756965e+09</td>\n", "      <td>SPKUSDT</td>\n", "      <td>126.0</td>\n", "      <td>Binance</td>\n", "      <td>0.06425</td>\n", "      <td>0.000000</td>\n", "      <td>Bitget</td>\n", "      <td>0.06430</td>\n", "      <td>0.004861</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2025-09-04 06:02:05.941654444</td>\n", "      <td>454b8c42-d59d-4a22-b06d-7b7c83615eb5</td>\n", "      <td>open</td>\n", "      <td>1.756966e+09</td>\n", "      <td>SPKUSDT</td>\n", "      <td>127.0</td>\n", "      <td>Bitget</td>\n", "      <td>0.06410</td>\n", "      <td>0.004884</td>\n", "      <td>Binance</td>\n", "      <td>0.06405</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                        datetime                              trade_id action  \\\n", "0  2025-09-02 12:53:42.016877174  d3314602-c6c1-4cde-8ea2-3d1885746e1b   open   \n", "1  2025-09-02 12:53:42.231836081  d3314602-c6c1-4cde-8ea2-3d1885746e1b  close   \n", "2  2025-09-04 05:46:40.901727200  39736129-04d7-47fd-bb7c-f417b247152d   open   \n", "3  2025-09-04 05:46:41.946900368  39736129-04d7-47fd-bb7c-f417b247152d  close   \n", "4  2025-09-04 06:02:05.941654444  454b8c42-d59d-4a22-b06d-7b7c83615eb5   open   \n", "\n", "      timestamp   symbol  exec_qty buy_exchange  avg_buy_price  buy_exec_fee  \\\n", "0  1.756818e+09  LRCUSDT      76.0      Binance        0.10833      0.000000   \n", "1  1.756818e+09  LRCUSDT      76.0      Binance        0.10838      0.000000   \n", "2  1.756965e+09  SPKUSDT     126.0      Binance        0.06437      0.000000   \n", "3  1.756965e+09  SPKUSDT     126.0      Binance        0.06425      0.000000   \n", "4  1.756966e+09  SPKUSDT     127.0       Bitget        0.06410      0.004884   \n", "\n", "  sell_exchange  avg_sell_price  sell_exec_fee  \n", "0         Bybit         0.10833       0.004528  \n", "1         Bybit         0.10843       0.004532  \n", "2        Bitget         0.06429       0.004861  \n", "3        Bitget         0.06430       0.004861  \n", "4       Binance         0.06405       0.000000  "]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["trade_log_updated = pd.read_csv('trade_history_parsed_new.csv')\n", "trade_log_updated[:5]"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import io\n", "\n", "def analyze_trade_performance_corrected(df: pd.DataFrame):\n", "    \"\"\"\n", "    分析交易日志DataFrame，并在计算前修正 close 动作中颠倒的买卖数据。\n", "    按天统计并打印PnL和胜率。\n", "    \"\"\"\n", "    print(\"trade log分析开始...\")\n", "    \n", "    # --- 1. 数据预处理 ---\n", "    df_corrected = df.copy() # 创建副本以避免修改原始DataFrame\n", "    df_corrected['datetime'] = pd.to_datetime(df_corrected['datetime'])\n", "    df_corrected['date'] = df_corrected['datetime'].dt.date\n", "\n", "    # --- 2. 核心修正逻辑 ---\n", "    print(\"\\n正在修正 'close' 动作中的颠倒数据...\")\n", "    # 筛选出所有 'close' 动作的行\n", "    close_mask = df_corrected['action'] == 'close'\n", "\n", "    # 定义需要交换的列\n", "    buy_cols = ['buy_exchange', 'avg_buy_price', 'buy_exec_fee']\n", "    sell_cols = ['sell_exchange', 'avg_sell_price', 'sell_exec_fee']\n", "\n", "    # 使用 .loc 和 close_mask 来精确地交换这些列的值\n", "    # 将 close 行的买入数据暂存\n", "    temp_buy_data = df_corrected.loc[close_mask, buy_cols].copy()\n", "    \n", "    # 将 close 行的卖出数据赋值给买入列\n", "    df_corrected.loc[close_mask, buy_cols] = df_corrected.loc[close_mask, sell_cols].values\n", "    \n", "    # 将暂存的买入数据赋值给卖出列\n", "    df_corrected.loc[close_mask, sell_cols] = temp_buy_data.values\n", "    \n", "    print(\"数据修正完成。\")\n", "\n", "\n", "    # --- 3. 计算每笔操作（open/close）的净 PnL (使用修正后的数据) ---\n", "    df_corrected.dropna(subset=['avg_buy_price', 'avg_sell_price', 'exec_qty'], inplace=True)\n", "    df_corrected['buy_exec_fee'].fillna(0, inplace=True)\n", "    df_corrected['sell_exec_fee'].fillna(0, inplace=True)\n", "    \n", "    df_corrected['net_pnl_per_action'] = (df_corrected['avg_sell_price'] - df_corrected['avg_buy_price']) * df_corrected['exec_qty'] - (df_corrected['buy_exec_fee'] + df_corrected['sell_exec_fee'])\n", "\n", "    # --- 4. 计算每笔完整交易（按trade_id）的总 PnL ---\n", "    trade_results = df_corrected.groupby('trade_id')['net_pnl_per_action'].sum().reset_index()\n", "    trade_results.rename(columns={'net_pnl_per_action': 'total_pnl'}, inplace=True)\n", "    trade_results['is_win'] = trade_results['total_pnl'] > 0\n", "\n", "    # --- 5. 关联交易日期 ---\n", "    trade_dates = df_corrected[df_corrected['action'] == 'open'][['trade_id', 'date']].drop_duplicates()\n", "    if not trade_dates.empty:\n", "         trade_results = pd.merge(trade_results, trade_dates, on='trade_id', how='left')\n", "    else: \n", "        trade_dates = df_corrected[df_corrected['action'] == 'close'][['trade_id', 'date']].drop_duplicates()\n", "        trade_results = pd.merge(trade_results, trade_dates, on='trade_id', how='left')\n", "\n", "    # --- 6. 按天统计最终结果 ---\n", "    daily_stats = trade_results.groupby('date').agg(\n", "        total_pnl=('total_pnl', 'sum'),\n", "        win_trades=('is_win', 'sum'),\n", "        total_trades=('trade_id', 'count')\n", "    ).reset_index()\n", "    daily_stats['win_rate_percent'] = (daily_stats['win_trades'] / daily_stats['total_trades'] * 100).round(2)\n", "\n", "    # --- 7. 打印结果 ---\n", "    print(\"\\n--- 每日交易表现 (已修正) ---\")\n", "    if daily_stats.empty:\n", "        print(\"没有找到完整的交易记录进行分析。\")\n", "    else:\n", "        for index, row in daily_stats.iterrows():\n", "            print(f\"\\n日期: {row['date']}\")\n", "            print(f\"  - 总 PnL: {row['total_pnl']:.4f}\")\n", "            print(f\"  - 交易笔数: {row['total_trades']} (胜: {row['win_trades']} / 负: {row['total_trades'] - row['win_trades']})\")\n", "            print(f\"  - 胜率: {row['win_rate_percent']}%\")\n", "\n", "    print(\"\\n--- 总体表现摘要 (已修正) ---\")\n", "    total_pnl_summary = daily_stats['total_pnl'].sum()\n", "    total_wins_summary = daily_stats['win_trades'].sum()\n", "    total_trades_summary = daily_stats['total_trades'].sum()\n", "    \n", "    if total_trades_summary > 0:\n", "        overall_win_rate = (total_wins_summary / total_trades_summary * 100).round(2)\n", "        print(f\"总 PnL: {total_pnl_summary:.4f}\")\n", "        print(f\"总交易笔数: {total_trades_summary}\")\n", "        print(f\"总胜利笔数: {total_wins_summary}\")\n", "        print(f\"总胜率: {overall_win_rate}%\")\n", "    else:\n", "        print(\"无数据可供汇总。\")\n", "    print(\"\\n分析结束。\")\n", "    return trade_results,df_corrected"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["trade log分析开始...\n", "\n", "正在修正 'close' 动作中的颠倒数据...\n", "数据修正完成。\n", "\n", "--- 每日交易表现 (已修正) ---\n", "\n", "日期: 2025-09-02\n", "  - 总 PnL: -0.0129\n", "  - 交易笔数: 1 (胜: 0 / 负: 1)\n", "  - 胜率: 0.0%\n", "\n", "日期: 2025-09-04\n", "  - 总 PnL: -6.9245\n", "  - 交易笔数: 274 (胜: 0 / 负: 274)\n", "  - 胜率: 0.0%\n", "\n", "日期: 2025-09-05\n", "  - 总 PnL: -4.5826\n", "  - 交易笔数: 182 (胜: 0 / 负: 182)\n", "  - 胜率: 0.0%\n", "\n", "日期: 2025-09-08\n", "  - 总 PnL: -0.1693\n", "  - 交易笔数: 12 (胜: 1 / 负: 11)\n", "  - 胜率: 8.33%\n", "\n", "日期: 2025-09-09\n", "  - 总 PnL: -87.2805\n", "  - 交易笔数: 50 (胜: 4 / 负: 46)\n", "  - 胜率: 8.0%\n", "\n", "--- 总体表现摘要 (已修正) ---\n", "总 PnL: -98.9698\n", "总交易笔数: 519\n", "总胜利笔数: 5\n", "总胜率: 0.96%\n", "\n", "分析结束。\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/fh/_bwb2kk14xlcblmmmdrc76480000gn/T/ipykernel_7663/2225588742.py:40: FutureWarning: A value is trying to be set on a copy of a DataFrame or Series through chained assignment using an inplace method.\n", "The behavior will change in pandas 3.0. This inplace method will never work because the intermediate object on which we are setting values always behaves as a copy.\n", "\n", "For example, when doing 'df[col].method(value, inplace=True)', try using 'df.method({col: value}, inplace=True)' or df[col] = df[col].method(value) instead, to perform the operation inplace on the original object.\n", "\n", "\n", "  df_corrected['buy_exec_fee'].fillna(0, inplace=True)\n", "/var/folders/fh/_bwb2kk14xlcblmmmdrc76480000gn/T/ipykernel_7663/2225588742.py:41: FutureWarning: A value is trying to be set on a copy of a DataFrame or Series through chained assignment using an inplace method.\n", "The behavior will change in pandas 3.0. This inplace method will never work because the intermediate object on which we are setting values always behaves as a copy.\n", "\n", "For example, when doing 'df[col].method(value, inplace=True)', try using 'df.method({col: value}, inplace=True)' or df[col] = df[col].method(value) instead, to perform the operation inplace on the original object.\n", "\n", "\n", "  df_corrected['sell_exec_fee'].fillna(0, inplace=True)\n"]}], "source": ["trade_results,df_corrected = analyze_trade_performance_corrected(trade_log_updated)\n"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>trade_id</th>\n", "      <th>total_pnl</th>\n", "      <th>is_win</th>\n", "      <th>date</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>004ee04a-597a-4871-9e04-a69abb22582f</td>\n", "      <td>0.012428</td>\n", "      <td>True</td>\n", "      <td>2025-09-09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>00529634-2a69-448c-9090-aaf23015188e</td>\n", "      <td>-0.021713</td>\n", "      <td>False</td>\n", "      <td>2025-09-04</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>0155b1a3-6f52-471f-8cfe-27b9d3d41287</td>\n", "      <td>-0.030089</td>\n", "      <td>False</td>\n", "      <td>2025-09-04</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>021ec77d-0dbf-4a8a-b894-30a54b7de92b</td>\n", "      <td>-0.013732</td>\n", "      <td>False</td>\n", "      <td>2025-09-04</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>02218b2d-d94e-4c90-b36e-186c5e4a3b42</td>\n", "      <td>-0.024373</td>\n", "      <td>False</td>\n", "      <td>2025-09-05</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                               trade_id  total_pnl  is_win        date\n", "0  004ee04a-597a-4871-9e04-a69abb22582f   0.012428    True  2025-09-09\n", "1  00529634-2a69-448c-9090-aaf23015188e  -0.021713   False  2025-09-04\n", "2  0155b1a3-6f52-471f-8cfe-27b9d3d41287  -0.030089   False  2025-09-04\n", "3  021ec77d-0dbf-4a8a-b894-30a54b7de92b  -0.013732   False  2025-09-04\n", "4  02218b2d-d94e-4c90-b36e-186c5e4a3b42  -0.024373   False  2025-09-05"]}, "execution_count": 35, "metadata": {}, "output_type": "execute_result"}], "source": ["trade_results[:5]"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>trade_id</th>\n", "      <th>total_pnl</th>\n", "      <th>is_win</th>\n", "      <th>date</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>004ee04a-597a-4871-9e04-a69abb22582f</td>\n", "      <td>0.012428</td>\n", "      <td>True</td>\n", "      <td>2025-09-09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>46</th>\n", "      <td>12c4fd14-7623-4061-9836-307467ab62a5</td>\n", "      <td>0.014377</td>\n", "      <td>True</td>\n", "      <td>2025-09-09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>76</th>\n", "      <td>221d2121-e226-4816-9f79-f48de0f46666</td>\n", "      <td>0.008075</td>\n", "      <td>True</td>\n", "      <td>2025-09-09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>125</th>\n", "      <td>3846fc12-fac8-4c67-ac56-d01e195ca8bf</td>\n", "      <td>0.001958</td>\n", "      <td>True</td>\n", "      <td>2025-09-08</td>\n", "    </tr>\n", "    <tr>\n", "      <th>322</th>\n", "      <td>9ae9352a-ed45-4f6e-9e8c-fa459f26fb19</td>\n", "      <td>0.003239</td>\n", "      <td>True</td>\n", "      <td>2025-09-09</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                 trade_id  total_pnl  is_win        date\n", "0    004ee04a-597a-4871-9e04-a69abb22582f   0.012428    True  2025-09-09\n", "46   12c4fd14-7623-4061-9836-307467ab62a5   0.014377    True  2025-09-09\n", "76   221d2121-e226-4816-9f79-f48de0f46666   0.008075    True  2025-09-09\n", "125  3846fc12-fac8-4c67-ac56-d01e195ca8bf   0.001958    True  2025-09-08\n", "322  9ae9352a-ed45-4f6e-9e8c-fa459f26fb19   0.003239    True  2025-09-09"]}, "execution_count": 37, "metadata": {}, "output_type": "execute_result"}], "source": ["winning_trades_summary = trade_results[trade_results['is_win']]\n", "winning_trades_summary"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                          datetime                              trade_id  \\\n", "926  2025-09-08 02:58:15.371385098  3846fc12-fac8-4c67-ac56-d01e195ca8bf   \n", "927  2025-09-08 03:01:17.431622982  3846fc12-fac8-4c67-ac56-d01e195ca8bf   \n", "940  2025-09-09 01:42:05.745919943  004ee04a-597a-4871-9e04-a69abb22582f   \n", "941  2025-09-09 02:00:03.383151770  004ee04a-597a-4871-9e04-a69abb22582f   \n", "966  2025-09-09 02:36:06.717848539  12c4fd14-7623-4061-9836-307467ab62a5   \n", "967  2025-09-09 02:36:34.292508364  12c4fd14-7623-4061-9836-307467ab62a5   \n", "996  2025-09-09 03:15:52.623041630  221d2121-e226-4816-9f79-f48de0f46666   \n", "997  2025-09-09 03:16:36.534939289  221d2121-e226-4816-9f79-f48de0f46666   \n", "1004 2025-09-09 03:33:23.093135357  9ae9352a-ed45-4f6e-9e8c-fa459f26fb19   \n", "1005 2025-09-09 03:38:38.390560865  9ae9352a-ed45-4f6e-9e8c-fa459f26fb19   \n", "\n", "     action     timestamp     symbol  exec_qty buy_exchange  avg_buy_price  \\\n", "926    open  1.757300e+09   CATIUSDT      76.0        Bybit        0.08673   \n", "927   close  1.757300e+09   CATIUSDT      76.0      Binance        0.08689   \n", "940    open  1.757382e+09  KAITOUSDT       5.0      Binance        1.35850   \n", "941   close  1.757383e+09  KAITOUSDT       5.0        Bybit        1.34852   \n", "966    open  1.757385e+09  KAITOUSDT       4.0       Bitget        1.50100   \n", "967   close  1.757385e+09  KAITOUSDT       0.0        Bybit        1.46140   \n", "996    open  1.757388e+09  KAITOUSDT       5.0      Binance        1.41720   \n", "997   close  1.757388e+09  KAITOUSDT       5.0        Bybit        1.41902   \n", "1004   open  1.757389e+09  KAITOUSDT       5.0      Binance        1.41580   \n", "1005  close  1.757389e+09  KAITOUSDT       5.0        Bybit        1.40040   \n", "\n", "      buy_exec_fee sell_exchange  avg_sell_price  sell_exec_fee        date  \\\n", "926       0.003625       Binance        0.086760       0.000000  2025-09-08   \n", "927       0.000000         Bybit        0.086981       0.003636  2025-09-08   \n", "940       0.000000         Bybit        1.368600       0.003764  2025-09-09   \n", "941       0.003708       Binance        1.342400       0.000000  2025-09-09   \n", "966       0.003602         Bybit        1.506600       0.001205  2025-09-09   \n", "967       0.003215        Bitget        0.000000       0.000000  2025-09-09   \n", "996       0.000000         Bybit        1.422200       0.001422  2025-09-09   \n", "997       0.003902       Binance        1.416700       0.000000  2025-09-09   \n", "1004      0.000000         Bybit        1.421600       0.003909  2025-09-09   \n", "1005      0.003851       Binance        1.396800       0.000000  2025-09-09   \n", "\n", "      net_pnl_per_action  \n", "926            -0.001345  \n", "927             0.003304  \n", "940             0.046736  \n", "941            -0.034308  \n", "966             0.017592  \n", "967            -0.003215  \n", "996             0.023578  \n", "997            -0.015502  \n", "1004            0.025091  \n", "1005           -0.021851  \n"]}], "source": ["winning_trade_ids = trade_results[trade_results['is_win']]['trade_id']\n", "print(df_corrected[df_corrected['trade_id'].isin(winning_trade_ids)])"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["解析完成！数据已保存到 'testcsv.csv'。\n", "\n", "数据预览：\n", "                       datetime                              trade_id action  \\\n", "0 2025-09-05 09:39:45.178416014  246f71b9-7456-4736-b3f7-54b40056a766   open   \n", "1 2025-09-05 09:39:58.220924139  246f71b9-7456-4736-b3f7-54b40056a766  close   \n", "\n", "      timestamp     symbol  exec_qty buy_exchange  avg_buy_price  \\\n", "0  1.757065e+09  PARTIUSDT      40.0        Bybit         0.2097   \n", "1  1.757065e+09  PARTIUSDT      40.0        Bybit         0.2116   \n", "\n", "   buy_exec_fee sell_exchange  avg_sell_price  sell_exec_fee  \n", "0      0.004613        Bitget          0.2102       0.005045  \n", "1      0.004655        Bitget          0.2124       0.005098  \n"]}], "source": ["parse_trade_log_final('trade_history_test.log','testcsv.csv')"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/plain": ["-0.031410999999999786"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["(40 * (0.2102-0.2097) - 0.004613-0.005045)+ (40 * (0.2116-0.2124) - 0.004655 - 0.005098)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["trade log分析开始...\n", "\n", "正在修正 'close' 动作中的颠倒数据...\n", "数据修正完成。\n", "\n", "--- 每日交易表现 (已修正) ---\n", "\n", "日期: 2025-09-05\n", "  - 总 PnL: -0.0314\n", "  - 交易笔数: 1 (胜: 0 / 负: 1)\n", "  - 胜率: 0.0%\n", "\n", "--- 总体表现摘要 (已修正) ---\n", "总 PnL: -0.0314\n", "总交易笔数: 1\n", "总胜利笔数: 0\n", "总胜率: 0.0%\n", "\n", "分析结束。\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/fh/_bwb2kk14xlcblmmmdrc76480000gn/T/ipykernel_7663/1647242378.py:40: FutureWarning: A value is trying to be set on a copy of a DataFrame or Series through chained assignment using an inplace method.\n", "The behavior will change in pandas 3.0. This inplace method will never work because the intermediate object on which we are setting values always behaves as a copy.\n", "\n", "For example, when doing 'df[col].method(value, inplace=True)', try using 'df.method({col: value}, inplace=True)' or df[col] = df[col].method(value) instead, to perform the operation inplace on the original object.\n", "\n", "\n", "  df_corrected['buy_exec_fee'].fillna(0, inplace=True)\n", "/var/folders/fh/_bwb2kk14xlcblmmmdrc76480000gn/T/ipykernel_7663/1647242378.py:41: FutureWarning: A value is trying to be set on a copy of a DataFrame or Series through chained assignment using an inplace method.\n", "The behavior will change in pandas 3.0. This inplace method will never work because the intermediate object on which we are setting values always behaves as a copy.\n", "\n", "For example, when doing 'df[col].method(value, inplace=True)', try using 'df.method({col: value}, inplace=True)' or df[col] = df[col].method(value) instead, to perform the operation inplace on the original object.\n", "\n", "\n", "  df_corrected['sell_exec_fee'].fillna(0, inplace=True)\n"]}], "source": ["test_log = pd.read_csv('testcsv.csv')\n", "analyze_trade_performance_corrected(test_log)"]}], "metadata": {"kernelspec": {"display_name": "backtest", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.8"}}, "nbformat": 4, "nbformat_minor": 2}