import time
from trader import TraderState, unified_place_order,OrderSignal
from logging_setup import setup_loggers
# from trader import connect_to_bybit
from exchange import BybitExchangeHTTP
import uuid

trader_state = TraderState()
decision_logger, output_logger = setup_loggers()
decision_logger.info(f'模拟开始')
# 记得改_http_manager 里面的 demo default = true/false
# demo keys
# bybit_exchange = BybitExchangeHTTP('Bybit', api_key="MmZWmAS94JeZF8Ev4G",api_secret="KsrStIdsXeVpUN5sz6pOnVxQgfPNzPounrSk",isTest=False)

# real keys
bybit_exchange = BybitExchangeHTTP('Bybit', api_key="Q43K5Qr31hwaWxtvUV",api_secret="GGJ2P98SqRFQyKaV41JgOaWxEAMU3zc6xvMN",isTest=False) 

bybit_exchange.connect()
trader_state.exchanges['bybit'] = bybit_exchange
total_equity, total_available_balance = bybit_exchange.get_balance_info()
if total_equity:
    decision_logger.info(f'bybit 剩余可用资金: {total_available_balance}')
else:
    decision_logger.info(f'bybit 剩余可用资金查询失败')

#does bybit need get position mode, set position mode? account mode?

bybit_exchange_api = trader_state.exchanges['bybit']
# buy_order_signal = OrderSignal('linear','QTUMUSDT','buy','market','10','bybit')
# bybit_buy_res = unified_place_order(trader_state,buy_order_signal)
# print(bybit_buy_res)
"""(True, {'retCode': 0, 'retMsg': 'OK', 'result': {'orderId': '851fe2c1-d80d-4201-8d6c-6aff98fc1b01', 'orderLinkId': ''}, 'retExtInfo': {}, 'time': *************})"""

# sell_order_signal = OrderSignal('linear','QTUMUSDT','sell','market','10','bybit')
# bybit_sell_res = unified_place_order(trader_state, sell_order_signal)

# print(bybit_sell_res)
"""(True, {'retCode': 0, 'retMsg': 'OK', 'result': {'orderId': 'c57a594b-07d5-4475-b24c-d5d31b8a75ec', 'orderLinkId': ''}, 'retExtInfo': {}, 'time': *************})"""
# bybit 获取余额速率限制 10次/秒
# 历史记录获取
# 需要输入
# 不知道怎么用
# history = bybit_exchange.session.get_order_history()
# print(history)

# instruments = bybit_exchange_api.session.get_instruments_info(category='linear')
# print('hello')
# lotsz= bybit_exchange_api.get_lot_size('BTCUSDT')
# print(lotsz)

orderbook =bybit_exchange_api.get_orderbook('BTCUSDT')
print(orderbook)