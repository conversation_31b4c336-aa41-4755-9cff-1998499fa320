from shared_imports import redis,logging,time,os,Config,asyncio,json,websockets,ConnectionClosedError,ConnectionClosedOK,copy
from trading_state import TradingState
from shared_imports import websockets,asyncio, json,ConnectionClosedError,ConnectionClosedOK,Config,datetime
from trading_state import TradingState
import asyncio
import json
import websockets

from abc import ABC, abstractmethod
import json
from time import sleep
from typing import Dict
import uuid
from config_copy import Config
from api_keys import EXCHANGE_API_KEYS
from logging_setup import logging, setup_loggers
from exchange import Bitgetv1HTTP,BitgetV2HTTP,OkxExchangeHTTP,BybitExchangeHTTP,BaseExchangeHTTP,BinanceExchangeHTTP
from dataclasses import dataclass
import math
import sys
# from data_service_test import bitget_ws

state = TradingState()
state.init_symbols()
symbols = state.symbols

setup_loggers()
decision_logger = logging.getLogger('decision_logger')
history_logger = logging.getLogger('history_logger')

shared_data = {}

MAX_RECONNECT_ATTEMPTS = 5
RECONNECT_DELAY = 5  # seconds
CONNECTION_TIMEOUT = 10  # seconds


# 深度账本参数             排序     bid     ask
#   - binance   20               降序     升序
#   - bybit     50               降序     升序
#   - bitget    15               降序     升序
#   - Okx       15               降序     升序

def calculate_spread(symbol, exchange_name_1, exchange_name_2):
    if shared_data[symbol][exchange_name_1] is None or shared_data[symbol][exchange_name_2] is None or shared_data[symbol][exchange_name_2]["bid"] is None or shared_data[symbol][exchange_name_1]["ask"] is None or shared_data[symbol][exchange_name_2]["ask"] is None or shared_data[symbol][exchange_name_1]["bid"] is None:
        return None

    if shared_data[symbol][exchange_name_2]["bid"] > shared_data[symbol][exchange_name_1]["ask"]:
        return {"spread": 2 * (shared_data[symbol][exchange_name_2]["bid"] - shared_data[symbol][exchange_name_1]["ask"]) / (shared_data[symbol][exchange_name_2]["bid"] + shared_data[symbol][exchange_name_1]["ask"]), "buy_exchange": exchange_name_1, "sell_exchange": exchange_name_2, "buy_price": shared_data[symbol][exchange_name_1]["ask"], "sell_price": shared_data[symbol][exchange_name_2]["bid"]}
    elif shared_data[symbol][exchange_name_1]["bid"] > shared_data[symbol][exchange_name_2]["ask"]:
        return {"spread": 2 * (shared_data[symbol][exchange_name_1]["bid"] - shared_data[symbol][exchange_name_2]["ask"]) / (shared_data[symbol][exchange_name_1]["bid"] + shared_data[symbol][exchange_name_2]["ask"]), "buy_exchange": exchange_name_2, "sell_exchange": exchange_name_1, "buy_price": shared_data[symbol][exchange_name_2]["ask"], "sell_price": shared_data[symbol][exchange_name_1]["bid"]}
    else:
        return None # 上面两种情况已经包含了有正常的情况

    return None

# 通用WebSocket连接器
async def generic_ws_connector(exchange_name, ws_config, state:TradingState):
    """通用WebSocket连接器，减少重复代码"""
    reconnect_count = 0
    keepalive_task = None

    while reconnect_count < Config.MAX_RECONNECT_ATTEMPTS:
        try:
            print(f"[{exchange_name}] Connecting... (attempt {reconnect_count + 1})")

            # 为Bitget禁用内置ping机制，因为它使用自定义ping/pong
            ping_interval = None if exchange_name == 'Bitget' else 20
            ping_timeout = None if exchange_name == 'Bitget' else 10

            async with websockets.connect(
                ws_config['uri'],
                ping_interval=ping_interval,
                ping_timeout=ping_timeout,
                close_timeout=10
            ) as ws:
                # 发送订阅消息
                if 'subscribe_msg' in ws_config:
                    await ws.send(json.dumps(ws_config['subscribe_msg']))

                # 启动自定义keepalive任务（仅对Bitget）
                if 'keepalive' in ws_config:
                    keepalive_task = asyncio.create_task(ws_config['keepalive'](ws))

                print(f"[{exchange_name}] Connected successfully")
                reconnect_count = 0

                while True:
                    try:
                        msg = await asyncio.wait_for(ws.recv(), timeout=30)

                        # 处理Bitget的ping/pong消息
                        if exchange_name == 'Bitget' and msg == "pong":
                            continue

                        data = json.loads(msg)

                        # 使用解析函数处理数据
                        parsed = ws_config['parser'](data)
                        if parsed:
                            symbol, bid, ask = parsed
                            async with state.lock:
                                if symbol in state.shared_data:
                                    state.shared_data[symbol][exchange_name]["bid"] = bid
                                    state.shared_data[symbol][exchange_name]["ask"] = ask
                            
                            # 直接计算spread
                            if symbol not in shared_data:
                                shared_data[symbol] = {}
                            
                            if exchange_name not in shared_data[symbol]:
                                shared_data[symbol][exchange_name] = {"bid": None, "ask": None}

                            if "spreads" not in shared_data[symbol]:
                                shared_data[symbol]["spreads"] = {
                                    "Bybit-Bitget": {"exchanges":["Bybit","Bitget"], "spread_info": None},
                                    "Bybit-Okx": {"exchanges":["Bybit","Okx"], "spread_info": None},
                                    "Bybit-Binance": {"exchanges":["Bybit","Binance"], "spread_info": None},
                                    "Bitget-Okx": {"exchanges":["Bitget","Okx"], "spread_info": None},
                                    "Bitget-Binance": {"exchanges":["Bitget","Binance"], "spread_info": None},
                                    "Okx-Binance": {"exchanges":["Okx","Binance"], "spread_info": None},
                                }

                            if "best_spread" not in shared_data[symbol]:
                                shared_data[symbol]["best_spread"] = {"symbol": None, "spread": None, "buy_exchange": None, "sell_exchange": None, "buy_price": None, "sell_price": None}
                            
                            # # 更新价格
                            shared_data[symbol][exchange_name]["bid"] = bid
                            shared_data[symbol][exchange_name]["ask"] = ask

                            # 计算同币种两两交易所间开平仓价差
                            for spread_name, spread_data in shared_data[symbol]["spreads"].items():
                                if exchange_name in spread_data["exchanges"]:
                                    # pass
                                    shared_data[symbol]["spreads"][spread_name]["spread_info"] = calculate_spread(symbol, spread_data["exchanges"][0], spread_data["exchanges"][1])
                                    # if shared_data[symbol]["spreads"][spread_name]["spread_info"] is None:
                                    #     continue
                                    # if exchange_name == shared_data[symbol]["best_spread"]["buy_exchange"] or exchange_name == shared_data[symbol]["best_spread"]["sell_exchange"]:
                                    #     shared_data[symbol]["best_spread"] = None
                                    # if shared_data[symbol]["best_spread"]["spread"] is None or shared_data[symbol]["spreads"][spread_name]["spread_info"]["spread"] > shared_data[symbol]["best_spread"]["spread"]:
                                    #     shared_data[symbol]["best_spread"] = shared_data[symbol]["spreads"][spread_name]["spread_info"]

                            
                            # if shared_data[symbol]["best_spread"]["spread"] is not None and shared_data[symbol]["best_spread"]["spread"] >= Config.MIN_SPREAD_PCT_THRESHOLD:
                            #     shared_data["best_spread"] = {"symbol": symbol, "spread": shared_data[symbol]["best_spread"]["spread"], "buy_exchange": shared_data[symbol]["best_spread"]["buy_exchange"], "sell_exchange": shared_data[symbol]["best_spread"]["sell_exchange"], "buy_price": shared_data[symbol]["best_spread"]["buy_price"], "sell_price": shared_data[symbol]["best_spread"]["sell_price"]}
                            #     print(f'最优价差: {shared_data["best_spread"]}')
                            

                    except asyncio.TimeoutError:
                        print(f"[{exchange_name}] No data received for 30s, checking connection...")
                        if exchange_name != 'Bitget':  # 其他交易所使用标准ping
                            await ws.ping()
                        continue
                    except json.JSONDecodeError as e:
                        print(f"[{exchange_name}] JSON decode error: {e}")
                        continue

        except (ConnectionClosedError, ConnectionClosedOK) as e:
            print(f"[{exchange_name}] Connection closed: {e}")
        except Exception as e:
            print(f"[{exchange_name}] Error: {e}")
        finally:
            # 清理keepalive任务
            if keepalive_task and not keepalive_task.done():
                keepalive_task.cancel()
                try:
                    await keepalive_task
                except asyncio.CancelledError:
                    pass

        reconnect_count += 1
        if reconnect_count < Config.MAX_RECONNECT_ATTEMPTS:
            print(f"[{exchange_name}] Reconnecting in {Config.RECONNECT_DELAY}s...")
            await asyncio.sleep(Config.RECONNECT_DELAY)
        else:
            print(f"[{exchange_name}] Max reconnection attempts reached")
            break

# 各交易所的解析器
def parse_binance(data):
    if 'data' in data:
        payload = data['data']
        symbol = payload.get('s')
        bid = payload.get('b')
        ask = payload.get('a')
        if symbol and bid and ask:
            return symbol, float(bid), float(ask)
    return None

def parse_bybit(data):
    if 'data' in data and data.get('topic', '').startswith('tickers.'):
        payload = data['data']
        symbol = payload.get('symbol')
        bid = payload.get('bid1Price')
        ask = payload.get('ask1Price')
        if symbol and bid and ask:
            return symbol, float(bid), float(ask)
    return None

def parse_bitget(data):
    """解析Bitget WebSocket数据"""
    if 'data' in data and len(data['data']) > 0:
        item = data['data'][0]
        bid = item.get('bidPr')
        ask = item.get('askPr')
        symbol = item.get('instId')
        if bid and ask and symbol:
            try:
                return symbol, float(bid), float(ask)
            except (ValueError, TypeError):
                return None
    return None

def parse_okx(data):
    if 'data' in data and len(data['data']) > 0:
        item = data['data'][0]
        bid = item.get('bidPx')
        ask = item.get('askPx')
        symbol = item.get('instId', '').replace('-SWAP','').replace('-','')
        if bid and ask and symbol:
            return symbol, float(bid), float(ask)
    return None

class OrderSignal:
    def __init__(self,category:str,
                 symbol: str,
                 side: str,  # buy/sell
                orderType: str,  # market/limit, not supporting limit yet TODO
                quantity: float,
                exchange: str,
                action: str
                ):
        self.category = category
        self.symbol = symbol
        self.orderType = orderType
        self.side = side
        self.quantity = quantity
        self.exchange = exchange
        self.id = uuid.uuid4()
        self.timestamp = time.time()
        self.action = action
   
class TraderState:

    def __init__(self):
        self.active_trades = []
        self.trade_history = {}
        self.exchanges = {'Bybit': None, 'Okx': None, 'Bitget': None, 'Binance': None}
        self.test_exchanges = ['Bybit','Okx','Bitget','Binance']

    def generate_and_store_from_history(self):
        """
        从 self.trade_history 生成 API 所需的格式，并存入 Redis。
        这是一个 O(M) 操作, M是独立交易的数量。
        """
        trade_pairs = []
        summary = {
            'total_trades': len(self.trade_history), # 总交易数就是 self.trade_history 的长度
            'open_trades': 0,
            'profitable_trades': 0,
            'total_pnl': 0.0
        }

        # 遍历 self.trade_history 中的每一笔配对好的交易
        for trade_id, trade_data in self.trade_history.items():
            open_record = trade_data['open']
            close_record = trade_data['close']

            open_buy_detail = open_record['real']['buy_record']
            open_sell_detail = open_record['real']['sell_record']

            trade_pair = {
                'symbol': open_buy_detail['symbol'],
                'buy_exchange': open_buy_detail['exchange'],
                'sell_exchange': open_sell_detail['exchange'],
                'open_buy_price': open_buy_detail['avg_price'],
                'open_sell_price': open_sell_detail['avg_price'],
                'open_time': open_buy_detail['our_time'],
                'status': 'OPEN',
                'pnl': 0.0,
                'close_time': None,
                'close_buy_price': None,
                'close_sell_price': None
            }

            if close_record:
                close_buy_detail = close_record['real']['buy_record']
                close_sell_detail = close_record['real']['sell_record']
                
                # TODO: 替换为真实的 PnL 逻辑
                pnl = close_record.get('virtual', {}).get('pnl', 0.0) 

                trade_pair.update({
                    'status': 'CLOSED',
                    'close_time': close_buy_detail['our_time'],
                    'close_buy_price': close_buy_detail['avg_price'],
                    'close_sell_price': close_sell_detail['avg_price'],
                    'pnl': pnl
                })
                
                summary['total_pnl'] += pnl
                if pnl >= 0:
                    summary['profitable_trades'] += 1
            else:
                summary['open_trades'] += 1
                # TODO: 更新实时 PnL
                # trade_pair['pnl'] = open_record.get('virtual', {}).get('current_pnl', 0.0)

            trade_pairs.append(trade_pair)

        final_data_to_store = {
            'trade_pairs': trade_pairs,
            'summary': summary,
            'last_updated': time.time()
        }

        self.redis_client.set("trading:formatted_history", json.dumps(final_data_to_store))
        print(f"已从 self.trade_history 生成并存储最新交易历史到 Redis。")

    def update_and_save(self, full_trade_record):
        """
        增量更新Redis和内存（更健壮的版本）。
        """
        # Use .get() to safely access keys that might not exist.
        trade_id = full_trade_record.get('trade_id')
        action = full_trade_record.get('action')

        # ✅ **Crucial Check 1: Ensure trade_id and action exist**
        if not trade_id or not action:
            # Using logger instead of print
            decision_logger.error(f"记录缺少 'trade_id' 或 'action'。跳过: {full_trade_record}")
            return

        # 1. 更新内存
        self.update_trade_history(full_trade_record)

        # 2. 增量更新Redis
        pipe = self.redis_client.pipeline()
        
        trade_detail_key = f"trade:detail:{trade_id}"
        summary_key = "trading:summary"

        if action == 'open':
            # Safely get the open_time using nested .get() calls
            open_time = full_trade_record.get('real', {}).get('buy_record', {}).get('our_time')
            
            # ✅ **Crucial Check 2: Ensure the timestamp for ZADD exists**
            if open_time is None:
                decision_logger.error(f"开仓记录 {trade_id} 缺少 'our_time'。无法更新Redis。")
                return

            pipe.hset(trade_detail_key, "open_record", json.dumps(full_trade_record))
            pipe.hset(trade_detail_key, "status", "OPEN")
            pipe.zadd("trades:by_time", {trade_id: open_time})
            pipe.hincrby(summary_key, "total_trades", 1)
            pipe.hincrby(summary_key, "open_trades", 1)

        elif action == 'close':
            close_time = full_trade_record.get('real', {}).get('buy_record', {}).get('our_time')
            pnl = full_trade_record.get('virtual', {}).get('pnl', 0.0) # This part is already safe

            # ✅ **Crucial Check 3: Ensure the timestamp for ZADD exists**
            if close_time is None:
                decision_logger.error(f"平仓记录 {trade_id} 缺少 'our_time'。无法更新Redis。")
                return

            pipe.hset(trade_detail_key, "close_record", json.dumps(full_trade_record))
            pipe.hset(trade_detail_key, "status", "CLOSED")
            pipe.hset(trade_detail_key, "pnl", pnl)
            pipe.zadd("trades:by_time", {trade_id: close_time})
            pipe.hincrby(summary_key, "open_trades", -1)
            pipe.hincrbyfloat(summary_key, "total_pnl", pnl)
            if pnl >= 0:
                pipe.hincrby(summary_key, "profitable_trades", 1)

        # 执行所有命令
        pipe.execute()
        # Using logger instead of print
        decision_logger.info(f"增加Redis中的交易: {trade_id}")

    def update_trade_history(self, full_trade_record):
        """
        高效地更新内存中的 trade_self.trade_history。
        这是一个 O(1) 操作。
        """
        trade_id = full_trade_record['trade_id']
        action = full_trade_record['action']

        if action == 'open':
            # 如果是开仓，创建一个新条目
            if trade_id in self.trade_history_manager.trade_history:
                print(f"警告: 收到重复的开仓ID {trade_id}，将覆盖。")
            self.trade_history_manager.trade_history[trade_id] = {
                "open": full_trade_record,
                "close": None
            }
            print(f"新交易已开仓: {trade_id}")

        elif action == 'close':
            # 如果是平仓，更新现有条目
            if trade_id in self.trade_history_manager.trade_history:
                self.trade_history_manager.trade_history[trade_id]['close'] = full_trade_record
                print(f"交易已平仓: {trade_id}")
            else:
                # 异常情况：收到了一个没有开仓记录的平仓ID
                print(f"错误: 收到未知的平仓ID {trade_id}，无法匹配。")

    def calculate_trade_amount(self, symbol, buy_exchange_api:BaseExchangeHTTP, buy_price, sell_exchange_api:BaseExchangeHTTP,sell_price):
        """计算交易数量 - 带缓存命中检测"""
        decision_logger.info(f"🔍 开始计算交易数量: {symbol}, 买方:{buy_exchange_api.exchange_name}, 卖方:{sell_exchange_api.exchange_name}")

        # 获取余额信息 - 检测缓存命中
        decision_logger.info(f"📊 获取 {buy_exchange_api.exchange_name} 余额信息...")
        buy_has_cache = buy_exchange_api._balance_cache is not None
        decision_logger.info(f"   余额缓存状态: {'✅ 有缓存，将使用缓存' if buy_has_cache else '❌ 无缓存，将调用API'}")

        _, buy_available = buy_exchange_api.get_balance_info(force_update=False)
        if buy_available == '' or buy_available is None:
            decision_logger.error(f'获取 {buy_exchange_api.exchange_name} 余额失败')
            return 0
        decision_logger.info(f"   ✅ {buy_exchange_api.exchange_name} 可用余额: {buy_available}")

        decision_logger.info(f"📊 获取 {sell_exchange_api.exchange_name} 余额信息...")
        sell_has_cache = sell_exchange_api._balance_cache is not None
        decision_logger.info(f"   余额缓存状态: {'✅ 有缓存，将使用缓存' if sell_has_cache else '❌ 无缓存，将调用API'}")

        _, sell_available = sell_exchange_api.get_balance_info(force_update=False)
        if sell_available == '' or sell_available is None:
            decision_logger.error(f'获取 {sell_exchange_api.exchange_name} 余额失败')
            return 0
        decision_logger.info(f"   ✅ {sell_exchange_api.exchange_name} 可用余额: {sell_available}")
        
        buy_available = float(buy_available)
        sell_available = float(sell_available)
        usdt_available = min(buy_available,sell_available)
        buy_available = float(usdt_available) * 0.85
        buy_amount_draft = buy_available / buy_price
        sell_available = float(usdt_available) * 0.85
        sell_amount_draft = sell_available / sell_price
        order_amount = min(buy_amount_draft,sell_amount_draft)

        # 获取符号信息 - 检测缓存命中
        decision_logger.info(f"📋 获取 {buy_exchange_api.exchange_name} {symbol} 符号信息...")
        buy_symbol_loaded = buy_exchange_api._symbol_info_loaded
        buy_symbol_cached = symbol in buy_exchange_api._symbol_info_cache if buy_symbol_loaded else False
        decision_logger.info(f"   符号缓存状态: {'✅ 已加载' if buy_symbol_loaded else '❌ 未加载'}, {'✅ 有缓存' if buy_symbol_cached else '❌ 无缓存'}")

        buy_ct_val = None
        if buy_exchange_api.exchange_name == 'Okx':
            buy_lot,max_buy_q,buy_ct_val = buy_exchange_api.get_symbol_info(symbol)
        else:
            buy_lot,max_buy_q,_ = buy_exchange_api.get_symbol_info(symbol)
        decision_logger.info(f"   ✅ {buy_exchange_api.exchange_name} {symbol}: min_qty={buy_lot}, max_qty={max_buy_q}, ct_val={buy_ct_val}")
        if buy_lot == None:
            buy_lot = 1.0

        decision_logger.info(f"📋 获取 {sell_exchange_api.exchange_name} {symbol} 符号信息...")
        sell_symbol_loaded = sell_exchange_api._symbol_info_loaded
        sell_symbol_cached = symbol in sell_exchange_api._symbol_info_cache if sell_symbol_loaded else False
        decision_logger.info(f"   符号缓存状态: {'✅ 已加载' if sell_symbol_loaded else '❌ 未加载'}, {'✅ 有缓存' if sell_symbol_cached else '❌ 无缓存'}")

        sell_ct_val = None
        if sell_exchange_api.exchange_name == 'Okx':
            sell_lot,max_sell_q,sell_ct_val = sell_exchange_api.get_symbol_info(symbol)
        else:
            sell_lot,max_sell_q,_ = sell_exchange_api.get_symbol_info(symbol)
        decision_logger.info(f"   ✅ {sell_exchange_api.exchange_name} {symbol}: min_qty={sell_lot}, max_qty={max_sell_q}, ct_val={sell_ct_val}")
        if sell_lot == None:
            sell_lot = 1.0


        # our_lot_size = buy_lot, sell_lot 的最小公倍数    
        our_lot_size = buy_exchange_api.lcm(buy_lot,sell_lot)
        if our_lot_size == 0:
            return 0
        order_amount = math.floor(order_amount / our_lot_size) * our_lot_size

        # 检查最大交易量限制，避免None值比较
        if max_buy_q is not None:
            order_amount = min(order_amount, max_buy_q)
        if max_sell_q is not None:
            order_amount = min(order_amount, max_sell_q)

        # 如果任一交易所的最大交易量为None，记录警告
        if max_buy_q is None or max_sell_q is None:
            decision_logger.warning(f"交易量限制查询失败: max_buy_q={max_buy_q}, max_sell_q={max_sell_q}")

        # OKX 合约面值修正：当包含 OKX 时，需要考虑张数取整对实际建仓数量的影响
        if buy_exchange_api.exchange_name == 'Okx' or sell_exchange_api.exchange_name == 'Okx':
            if buy_exchange_api.exchange_name == 'Okx' and buy_ct_val is not None:
                # 买方是 OKX，计算实际可交易的张数
                okx_contracts = int(order_amount / buy_ct_val)  # 向下取整到整张
                actual_order_amount = okx_contracts * buy_ct_val  # 实际建仓数量
                decision_logger.info(f"OKX买方张数修正: {order_amount:.6f} 币 -> {okx_contracts} 张 -> {actual_order_amount:.6f} 币")
                order_amount = actual_order_amount

            elif sell_exchange_api.exchange_name == 'Okx' and sell_ct_val is not None:
                # 卖方是 OKX，计算实际可交易的张数
                okx_contracts = int(order_amount / sell_ct_val)  # 向下取整到整张
                actual_order_amount = okx_contracts * sell_ct_val  # 实际建仓数量
                decision_logger.info(f"OKX卖方张数修正: {order_amount:.6f} 币 -> {okx_contracts} 张 -> {actual_order_amount:.6f} 币")
                order_amount = actual_order_amount

            # 如果修正后的数量为0或过小，返回0
            if order_amount <= 0:
                decision_logger.warning("OKX张数修正后交易量为0，跳过此次套利")
                return 0

        # now put into orderbook and get the number of trades within slippage
        # TODO

        # 缓存命中总结
        decision_logger.info(f"💡 缓存命中总结:")
        decision_logger.info(f"   余额缓存: {buy_exchange_api.exchange_name}={'✅命中' if buy_has_cache else '❌未命中'}, {sell_exchange_api.exchange_name}={'✅命中' if sell_has_cache else '❌未命中'}")
        decision_logger.info(f"   符号缓存: {buy_exchange_api.exchange_name}={'✅命中' if buy_symbol_cached else '❌未命中'}, {sell_exchange_api.exchange_name}={'✅命中' if sell_symbol_cached else '❌未命中'}")
        decision_logger.info(f"🎯 最终交易数量: {order_amount}")

        return order_amount
        # return 100
    
    def get_current_position_size(self):
        return len(self.active_trades)

    def _handle_partial_open_failure(self, successful_exchange, successful_record, failure_reason):
        """处理建仓部分失败的情况"""
        decision_logger.error(f"🚨 建仓部分失败处理: {failure_reason}")
        decision_logger.error(f"成功的交易所: {successful_exchange}")
        decision_logger.error(f"成功的订单记录: {successful_record}")

        # 尝试自动处理
        if successful_record and 'orderid' in successful_record:
            orderid = successful_record['orderid']
            decision_logger.error(f"尝试撤销订单: {orderid}")

            # TODO: 实现自动撤销
            cancel_success = self._attempt_cancel_order(successful_exchange, orderid)

            if cancel_success:
                decision_logger.info(f"✅ 成功撤销订单 {orderid}")
            else:
                decision_logger.error(f"❌ 撤销订单失败 {orderid}")
                decision_logger.error("⚠️ 存在单边仓位风险，需要手动处理！")

                # 记录风险仓位
                self._record_risk_position(successful_exchange, successful_record, failure_reason)
        else:
            decision_logger.error("⚠️ 无法获取订单ID，需要手动检查和处理！")

    def _attempt_cancel_order(self, exchange_name, orderid):
        """尝试撤销订单"""
        try:
            exchange_api = self.exchanges[exchange_name]

            # TODO: 实现各交易所的撤销订单API
            # if hasattr(exchange_api, 'cancel_order'):
            #     return exchange_api.cancel_order(orderid)

            decision_logger.warning(f"撤销订单功能尚未实现: {exchange_name}")
            return False

        except Exception as e:
            decision_logger.error(f"撤销订单异常: {e}")
            return False

    def _record_risk_position(self, exchange_name, order_record, risk_reason):
        """记录风险仓位"""
        risk_position = {
            'timestamp': time.time(),
            'exchange': exchange_name,
            'order_record': order_record,
            'risk_reason': risk_reason,
            'status': 'NEEDS_MANUAL_HANDLING'
        }

        # TODO: 保存到风险仓位数据库
        decision_logger.error(f"🚨 记录风险仓位: {risk_position}")

        # TODO: 发送告警通知
        # self._send_risk_alert(risk_position)

    def refresh_all_balances(self):
        """强制刷新所有交易所的余额缓存"""
        decision_logger.info("强制刷新所有交易所余额...")

        for exchange_name, exchange_api in self.exchanges.items():
            if exchange_api is not None:
                decision_logger.info(f"刷新 {exchange_name} 余额...")
                balance = exchange_api.get_balance_info(force_update=True)
                if balance and balance[0] is not None:
                    decision_logger.info(f"{exchange_name} 余额: 总资产={balance[0]}, 可用={balance[1]}")
                else:
                    decision_logger.error(f"{exchange_name} 余额获取失败")

        decision_logger.info("余额刷新完成")

    def load_all_symbol_info(self):
        """加载所有交易所的符号信息到缓存"""
        decision_logger.info("开始加载所有交易所的符号信息...")

        for exchange_name, exchange_api in self.exchanges.items():
            if exchange_api is not None:
                decision_logger.info(f"加载 {exchange_name} 符号信息...")
                try:
                    exchange_api.load_all_symbol_info()
                    symbol_count = len(exchange_api._symbol_info_cache)
                    decision_logger.info(f"{exchange_name} 成功加载 {symbol_count} 个符号信息")
                except Exception as e:
                    decision_logger.error(f"{exchange_name} 符号信息加载失败: {e}")

        decision_logger.info("符号信息加载完成")

    def estimate_market_impact(self, symbol, buy_exchange, sell_exchange, trade_amount):
        """预估市价单的市场影响（滑点）"""
        # 基础滑点预估（可以根据历史数据优化）
        base_slippage = 0.02  # 0.02% 基础滑点

        # 根据交易量调整滑点
        if trade_amount > 10000:  # 大额交易
            volume_slippage = 0.01
        elif trade_amount > 1000:  # 中等交易
            volume_slippage = 0.005
        else:  # 小额交易
            volume_slippage = 0.002

        # 根据交易所调整滑点（不同交易所流动性不同）
        exchange_slippage = {
            'Bybit': 0.002,
            'Okx': 0.003,
            'Bitget': 0.004,
            'Binance': 0.002
        }

        buy_slippage = exchange_slippage.get(buy_exchange, 0.005)
        sell_slippage = exchange_slippage.get(sell_exchange, 0.005)

        total_slippage = base_slippage + volume_slippage + buy_slippage + sell_slippage

        decision_logger.debug(f"📊 滑点预估 {symbol}: 基础{base_slippage:.3f}% + 交易量{volume_slippage:.3f}% + 交易所{buy_slippage:.3f}%/{sell_slippage:.3f}% = {total_slippage:.3f}%")

        return total_slippage

    def validate_spread_before_trading(self, symbol, calculated_spread_pct, buy_exchange, sell_exchange, trade_amount):
        """建仓前验证价差的可行性"""
        decision_logger.info(f"🔍 建仓前价差验证: {symbol}")

        # 1. 获取最新实时价格
        latest_data = self.get_symbol_realtime_data(symbol, [buy_exchange, sell_exchange])

        if not latest_data:
            decision_logger.error("❌ 无法获取实时数据，取消建仓")
            return False, None, None

        # 2. 检查数据新鲜度
        max_age = max(latest_data["exchanges"][ex]["age"] for ex in [buy_exchange, sell_exchange])
        if max_age > 2.0:
            decision_logger.warning(f"⚠️ 数据较旧({max_age:.3f}s)，价差可能不准确")

        # 3. 使用最新价格重新计算价差
        buy_price = latest_data["exchanges"][buy_exchange]["ask"]
        sell_price = latest_data["exchanges"][sell_exchange]["bid"]

        actual_spread = sell_price - buy_price
        actual_spread_pct = 2 * actual_spread / (buy_price + sell_price) * 100

        # 4. 价差偏差检查
        spread_deviation = abs(actual_spread_pct - calculated_spread_pct)
        decision_logger.info(f"📊 价差对比: 计算{calculated_spread_pct:.5f}% vs 实际{actual_spread_pct:.5f}% (偏差{spread_deviation:.5f}%)")

        if spread_deviation > 0.02:  # 偏差超过0.02%
            decision_logger.warning(f"⚠️ 价差偏差较大: {spread_deviation:.5f}%")

        # 5. 预估滑点影响
        estimated_slippage = self.estimate_market_impact(symbol, buy_exchange, sell_exchange, trade_amount)
        conservative_spread_pct = actual_spread_pct - estimated_slippage

        decision_logger.info(f"📊 滑点分析: 实际价差{actual_spread_pct:.5f}% - 预估滑点{estimated_slippage:.5f}% = 保守价差{conservative_spread_pct:.5f}%")

        # 6. 最终决策
        min_profitable_spread = 0.05  # 最小盈利价差阈值
        if conservative_spread_pct >= min_profitable_spread:
            decision_logger.info(f"✅ 保守价差{conservative_spread_pct:.5f}% >= 阈值{min_profitable_spread:.5f}%，可以建仓")
            return True, buy_price, sell_price
        else:
            decision_logger.warning(f"❌ 保守价差{conservative_spread_pct:.5f}% < 阈值{min_profitable_spread:.5f}%，取消建仓")
            return False, None, None

    def get_realtime_market_data(self, use_redis_fallback=True):
        """获取实时市场数据
        Args:
            use_redis_fallback: 如果实时数据不可用，是否回退到Redis
        Returns:
            市场数据字典或None
        """
        # 优先使用实时数据管理器
        try:
            realtime_data = realtime_data_manager.get_realtime_data()
            if realtime_data:
                decision_logger.debug("✅ 使用实时数据管理器数据")
                return realtime_data
        except Exception as e:
            decision_logger.warning(f"实时数据管理器访问失败: {e}")

        # 回退到Redis数据
        if use_redis_fallback:
            try:
                raw_data = self.redis_client.get("trading:exchange_data")
                if raw_data:
                    data = json.loads(raw_data)
                    decision_logger.debug("⚠️ 使用Redis缓存数据（可能有延迟）")
                    return data
            except Exception as e:
                decision_logger.error(f"Redis数据获取失败: {e}")

        decision_logger.error("❌ 无法获取市场数据")
        return None

    def get_symbol_realtime_data(self, symbol, required_exchanges=None):
        """获取指定symbol的实时数据，带数据质量检查"""
        if required_exchanges is None:
            required_exchanges = ['Bybit', 'Okx', 'Bitget', 'Binance']

        # 使用实时数据管理器获取套利数据
        arbitrage_data = realtime_data_manager.get_arbitrage_data(symbol, required_exchanges)

        if arbitrage_data:
            # 检查数据新鲜度
            max_age = max(ex_data["age"] for ex_data in arbitrage_data["exchanges"].values())
            if max_age < 2.0:  # 数据年龄小于2秒
                decision_logger.debug(f"✅ {symbol} 实时数据质量良好，最大年龄: {max_age:.3f}秒")
                return arbitrage_data
            else:
                decision_logger.warning(f"⚠️ {symbol} 数据较旧，最大年龄: {max_age:.3f}秒")

        # 回退到传统方式
        decision_logger.warning(f"❌ {symbol} 实时数据不可用，回退到Redis")
        return None

    def _handle_partial_close_failure(self, successful_exchange, failed_exchange,
                                    successful_record, failed_record, failure_reason):
        """处理平仓部分失败的情况"""
        decision_logger.error(f"平仓部分失败处理: {failure_reason}")
        decision_logger.error(f"成功的交易所: {successful_exchange}")
        decision_logger.error(f"失败的交易所: {failed_exchange}")

        # TODO: 实现风险处理逻辑
        # 1. 记录部分平仓状态
        # 2. 尝试重新平仓失败的部分
        # 3. 如果重试失败，标记为风险仓位
        # 4. 发送告警通知

        decision_logger.error("⚠️ 部分平仓失败，存在风险敞口，需要手动处理！")
    
    def open_position_test_Aug21(self, symbol, buy_exchange, sell_exchange, data) -> bool:
        """open positions, add to trade history and active trades return True if success, False if fail"""

        # 🔍 获取最新实时价格，避免价差偏差
        decision_logger.info(f"🔍 获取 {symbol} 最新实时价格用于建仓...")

        # 优先使用实时数据管理器获取最新价格
        latest_data = self.get_symbol_realtime_data(symbol, [buy_exchange, sell_exchange])

        if latest_data and latest_data["exchanges"]:
            # 使用最新的实时价格
            buy_price = latest_data["exchanges"][buy_exchange]["ask"]
            sell_price = latest_data["exchanges"][sell_exchange]["bid"]
            data_age = max(latest_data["exchanges"][buy_exchange]["age"],
                          latest_data["exchanges"][sell_exchange]["age"])
            decision_logger.info(f"✅ 使用实时价格: 买价{buy_price}@{buy_exchange}, 卖价{sell_price}@{sell_exchange}, 数据年龄{data_age:.3f}秒")
        else:
            # 回退到传统数据
            buy_price = data[symbol][buy_exchange]['ask']
            sell_price = data[symbol][sell_exchange]['bid']
            decision_logger.warning(f"⚠️ 使用传统数据: 买价{buy_price}@{buy_exchange}, 卖价{sell_price}@{sell_exchange}")

        # 重新计算实际价差
        actual_spread = sell_price - buy_price
        actual_spread_pct = 2 * actual_spread / (buy_price + sell_price) * 100
        decision_logger.info(f"📊 实际建仓价差: {actual_spread:.2f} ({actual_spread_pct:.5f}%)")

        succeed = False
        buy_exchange_api = self.exchanges[buy_exchange]
        sell_exchange_api = self.exchanges[sell_exchange]
        trade_amount = self.calculate_trade_amount(symbol,buy_exchange_api,buy_price,sell_exchange_api,sell_price)
        decision_logger.info(f'货币: {symbol}, {buy_price}@{buy_exchange}, {sell_price}@{sell_exchange})')

        decision_logger.info(f'计算得出总交易量(开/平): {trade_amount}')

        if trade_amount == 0:
            decision_logger.info(f'计算交易量为0，不交易')
            return False, {"trade_amount": 0}
        buy_amount = trade_amount
        sell_amount = trade_amount

        
        buy_record = {}
        sell_record = {}

        # connect to real api
        buy_order_signal = OrderSignal('linear',symbol,'buy','market',trade_amount,buy_exchange,'open')
        buy_succeed, buy_orderid = self.unified_place_order(buy_order_signal)

        sell_order_signal = OrderSignal('linear',symbol,'sell','market',trade_amount,sell_exchange,'open')
        sell_succeed, sell_orderid = self.unified_place_order(sell_order_signal)

        if buy_succeed and buy_orderid:
            buy_record = self.exchanges[buy_order_signal.exchange].get_order_detail(buy_orderid)
        else:
            decision_logger.error(f"{buy_order_signal.exchange} 下单失败或缺少订单ID")

        if sell_succeed and sell_orderid:
            sell_record = self.exchanges[sell_order_signal.exchange].get_order_detail(sell_orderid)
        else:
            decision_logger.error(f"{sell_order_signal.exchange} 下单失败或缺少订单ID")

        # 处理建仓失败情况
        if not buy_succeed:
            decision_logger.error(f'买单@{buy_exchange} 失败. info: {buy_record}')

        if not sell_succeed:
            decision_logger.error(f'卖单@{sell_exchange} 失败. info: {sell_record}')

        # 检查是否需要处理部分成功的情况
        if buy_succeed and not sell_succeed:
            decision_logger.error(f"建仓部分失败：买单成功但卖单失败，需要撤销买单")
            self._handle_partial_open_failure(buy_exchange, buy_record, "sell_failed")
            return False, {"buy_succeed": True, "sell_succeed": False, "action": "partial_failure_handled"}

        elif not buy_succeed and sell_succeed:
            decision_logger.error(f"建仓部分失败：卖单成功但买单失败，需要撤销卖单")
            self._handle_partial_open_failure(sell_exchange, sell_record, "buy_failed")
            return False, {"buy_succeed": False, "sell_succeed": True, "action": "partial_failure_handled"}

        elif not buy_succeed and not sell_succeed:
            decision_logger.error(f"建仓完全失败：买单和卖单都失败")
            return False, {"buy_succeed": False, "sell_succeed": False, "action": "complete_failure"}

        # 只有在两个订单都成功时才继续
        if buy_succeed and sell_succeed:
            actual_buy_amount = trade_amount

            # 安全地获取订单ID
            buy_orderid = None
            sell_orderid = None

            print(f'buy_record: {buy_record}')
            if buy_record and 'orderid' in buy_record:
                buy_orderid = buy_record['orderid']
            else:
                decision_logger.error(f"买单记录中没有orderid字段: {buy_record}")
                return False

            if sell_record and 'orderid' in sell_record:
                sell_orderid = sell_record['orderid']
            else:
                decision_logger.error(f"卖单记录中没有orderid字段: {sell_record}")
                return False

            # 获取订单详情
            buy_detail = buy_exchange_api.get_order_detail(buy_orderid)
            sell_detail = sell_exchange_api.get_order_detail(sell_orderid)

            # 检查订单详情是否获取成功
            if not buy_detail:
                decision_logger.error(f"无法获取买单详情，订单ID: {buy_orderid}")
                return False

            if not sell_detail:
                decision_logger.error(f"无法获取卖单详情，订单ID: {sell_orderid}")
                return False


            # 使用真实成交信息
            actual_buy_price = buy_detail.get('avg_price', buy_price)
            actual_sell_price = sell_detail.get('avg_price', sell_price)
            actual_buy_amount = buy_detail.get('exec_qty', buy_amount)
            actual_sell_amount = sell_detail.get('exec_qty', sell_amount)

            # 记录真实建仓信息
            decision_logger.info(f"真实建仓信息:")
            decision_logger.info(f"  买入: {buy_exchange} {actual_buy_amount:.6f} @ {actual_buy_price:.6f}")
            decision_logger.info(f"  卖出: {sell_exchange} {actual_sell_amount:.6f} @ {actual_sell_price:.6f}")
            decision_logger.info(f"  理论价差: {sell_price - buy_price:.6f}")
            decision_logger.info(f"  实际价差: {actual_sell_price - actual_buy_price:.6f}")

            # freeze capitals
            # update balances
            # TODO

            virtual_trade_record = {
                'symbol':symbol,
                'buy_exchange':buy_exchange,
                'buy_price': actual_buy_price,  # 使用真实成交价
                'buy_amount': actual_buy_amount,  # 使用真实成交量
                'sell_price': actual_sell_price,  # 使用真实成交价
                'sell_exchange':sell_exchange,
                'sell_amount': actual_sell_amount,  # 使用真实成交量
                'time_stamp':time.time()
            }

            full_trade_record = {
                'trade_id':str(uuid.uuid4()),  # 转换为字符串
                'action':'open',
                'real':{
                        'buy_record':buy_record,
                        'sell_record':sell_record},
                'virtual': virtual_trade_record,
                'time_stamp': time.time()
            }
            self.update_and_save(full_trade_record)


            self.active_trades.append(full_trade_record)
            history_log = json.dumps(full_trade_record,indent=4)
            history_logger.info(f'新纪录:\n{history_log}')

            # current_position
            virtual_rec = full_trade_record['virtual']
            actual_spread = virtual_rec['sell_price'] - virtual_rec['buy_price']
            actual_spread_pct = 2 * actual_spread / (virtual_rec['sell_price'] + virtual_rec['buy_price'])

            current_position = {
                                "symbol": virtual_rec['symbol'],
                                "best_buy_exchange": virtual_rec['buy_exchange'],
                                "best_buy_price": virtual_rec['buy_price'],
                                "best_sell_exchange": virtual_rec['sell_exchange'],
                                "best_sell_price": virtual_rec['sell_price'],
                                "open_spread_pct": actual_spread_pct,
                                "trade_time": time.time()
                                }
            # redis
            trader_state.redis_client.set("trading:current_position",json.dumps(current_position))

            # balance info update
            for exchange_name in [buy_exchange,sell_exchange]:
                account_usdt_value = Config.INITIAL_USDT_CAPITALS[exchange_name]
                _, available_usdt_balance = self.exchanges[exchange_name].get_balance_info()
                self.portfolio_manager.update_exchange_balance(
                    exchange_name=exchange_name,
                    account_usdt_value=account_usdt_value,
                    available_usdt_balance=available_usdt_balance
                )
    
            # 3. (可选但推荐) 更新后，立即将最新的整体状态持久化到Redis
            latest_state = self.portfolio_manager.to_dict()
            self.redis_client.set("trading:balance", json.dumps(latest_state))
            return True, full_trade_record
        
        else:
            decision_logger.info('开仓失败')
            hist = {'buy_succeed': buy_succeed,
                        'sell_succeed': sell_succeed}
            history_log = json.dumps(hist)
            history_logger.info(f'新纪录:\n{history_log}')

            # balance info update
            for exchange_name in [buy_exchange,sell_exchange]:
                account_usdt_value = Config.INITIAL_USDT_CAPITALS[exchange_name]
                _, available_usdt_balance = self.exchanges[exchange_name].get_balance_info()
                self.portfolio_manager.update_exchange_balance(
                    exchange_name=exchange_name,
                    account_usdt_value=account_usdt_value,
                    available_usdt_balance=available_usdt_balance
                )
    
            # 3. (可选但推荐) 更新后，立即将最新的整体状态持久化到Redis
            latest_state = self.portfolio_manager.to_dict()
            self.redis_client.set("trading:balance", json.dumps(latest_state))
        
            return False, hist
    
    def close_position_testAug21(self, symbol, prev_buy_exchange, prev_sell_exchange)-> bool:
        """
        prev_buy_exchange: 之前购入symbol (做多) 的交易所
        prev_sell_exchange: 之前出售symbol (做空) 的交易所

        """
        succeed = False

        # 买多少平多少
        trade_amount = self.active_trades[0]['virtual']['buy_amount']
        # ideally
        close_sell_record = {}
        close_buy_record = {}

        close_buy_order_signal = OrderSignal('linear',symbol,'sell','market',trade_amount,prev_buy_exchange,'close')
        close_buy_succeed, close_buy_record = self.unified_place_order(close_buy_order_signal)

        close_sell_order_signal = OrderSignal('linear',symbol,'buy','market',trade_amount,prev_sell_exchange,'close')
        close_sell_succeed, close_sell_record = self.unified_place_order(close_sell_order_signal)

        if not close_buy_succeed:
            decision_logger.info(f'平多失败, info: {close_buy_record}')

        if not close_sell_succeed:
            decision_logger.info(f'平空失败, info: {close_sell_record}')

        # if all succeed
        succeed = True
        if succeed:
            # free previous frozen capitals
            virtual_trade_record = {
                'symbol': symbol,
                'prev_buy_exchange': prev_buy_exchange,
                'prev_sell_exchange': prev_sell_exchange,
                'buy_exchange': prev_sell_exchange,
                'buy_amount': trade_amount,
                'sell_exchange': prev_buy_exchange,
                'sell_amount': trade_amount,
                'time_stamp':time.time()
            }
            full_trade_record = {
                'trade_id': self.active_trades[0]['trade_id'], 
                'action': 'close',
                'real':{
                        'sell_record':close_sell_record,
                        'buy_record':close_buy_record,
                        },
                'virtual': virtual_trade_record,
                'timestamp':time.time() 
            }
            self.update_and_save(full_trade_record)
            self.active_trades.pop()
            history_log = json.dumps(full_trade_record,indent=4)
            history_logger.info(f'新纪录:\n{history_log}')

            # redis
            trader_state.redis_client.delete("trading:current_position")
            # balance info update
            for exchange_name in [prev_buy_exchange,prev_sell_exchange]:
                account_usdt_value = Config.INITIAL_USDT_CAPITALS[exchange_name]
                _, available_usdt_balance = self.exchanges[exchange_name].get_balance_info()
                self.portfolio_manager.update_exchange_balance(
                    exchange_name=exchange_name,
                    account_usdt_value=account_usdt_value,
                    available_usdt_balance=available_usdt_balance
                )
    
            # 3. (可选但推荐) 更新后，立即将最新的整体状态持久化到Redis
            latest_state = self.portfolio_manager.to_dict()
            self.redis_client.set("trading:balance", json.dumps(latest_state))
            return True, full_trade_record
        # balance info update
        for exchange_name in [prev_buy_exchange,prev_sell_exchange]:
            account_usdt_value = Config.INITIAL_USDT_CAPITALS[exchange_name]
            _, available_usdt_balance = self.exchanges[exchange_name].get_balance_info()
            self.portfolio_manager.update_exchange_balance(
                exchange_name=exchange_name,
                account_usdt_value=account_usdt_value,
                available_usdt_balance=available_usdt_balance
            )

        # 3. (可选但推荐) 更新后，立即将最新的整体状态持久化到Redis
        latest_state = self.portfolio_manager.to_dict()
        self.redis_client.set("trading:balance", json.dumps(latest_state))
        return False, {"close_buy_succeed":close_buy_succeed,
                       "close_sell_succeed": close_sell_succeed}

    def can_open_positions(self) -> bool:
        return Config.MAX_POSITION_SIZE > len(self.active_trades)
    
    def check_open_conditions(self,max_spread_pct) -> bool:
        """return True of False, if should open -> True"""
        # TODO maybe also check balance
        return max_spread_pct >= Config.MIN_SPREAD_PCT_THRESHOLD
    
    def monitor_active_trades(self, data):
        for active_trade in self.active_trades:
            # calculate current_spread_pct but fixing the buy_exchange and sell_exchange.
            # have to sell@buy_exchange and buy@sell_exchange to close positions
            symbol = active_trade['virtual']['symbol']
            prev_buy = active_trade['virtual']['buy_exchange']
            bid_from_prev_buy = data[symbol][prev_buy]['bid']
            prev_sell = active_trade['virtual']['sell_exchange']
            ask_from_prev_sell = data[symbol][prev_sell]['ask']
            current_spread, current_spread_pct = calculate_closing_spread(bid_from_prev_buy, ask_from_prev_sell)

        return current_spread, current_spread_pct
    
    def check_close_conditions(self, spread_pct):
        """return True, threshold if meet the threshold, else False, threshold"""
        # TODO
        ask_from_buy = self.active_trades[0]['virtual']['buy_price']
        bid_from_sell = self.active_trades[0]['virtual']['sell_price']
        open_spread, open_spread_pct = calculate_opening_spread(ask_from_buy, bid_from_sell)
        res = float(open_spread_pct) * float(Config.MAGIC_THRESHOLD)
        return spread_pct < res, res
                
    def unified_place_order(self, order_signal:OrderSignal) :
        # select an exchange and place order
        exchange_api = self.exchanges[order_signal.exchange]

        orderid = None
        succeed = False
        res = None

        # 格式化数量，避免浮点精度问题
        formatted_qty = round(order_signal.quantity, 6)

        if order_signal.exchange == 'Okx':
            # OKX需要将币的数量转换为张数
            contracts_qty = exchange_api.coins_to_contracts(order_signal.symbol, order_signal.quantity)
            if contracts_qty is None or contracts_qty <= 0:
                decision_logger.error(f"OKX单位转换失败: {order_signal.quantity} 币 -> {contracts_qty} 张")
                return False, None

            decision_logger.info(f"OKX下单转换: {order_signal.quantity} 币 -> {contracts_qty} 张")
            succeed, res = exchange_api.place_order(category=order_signal.category,
                                            symbol=order_signal.symbol,
                                            side=order_signal.side,
                                            orderType=order_signal.orderType,
                                            qty=contracts_qty,action=order_signal.action)
            if succeed and res:
                orderid = res['data'][0]['clOrdId']
        elif order_signal.exchange == 'Bitget':
            # Bitget使用币的数量，直接下单
            succeed, res = exchange_api.place_order(category=order_signal.category,
                                            symbol=order_signal.symbol,
                                            side=order_signal.side,
                                            orderType=order_signal.orderType,
                                            qty=formatted_qty,action=order_signal.action)
            if succeed and res:
                orderid = res['data']['clientOid'] # 使用统一的 orderid 字段
        elif order_signal.exchange == 'Binance':
            # Binance使用币的数量，直接下单
            succeed, res = exchange_api.place_order(category=order_signal.category,
                                            symbol=order_signal.symbol,
                                            side=order_signal.side,
                                            orderType=order_signal.orderType,
                                            qty=formatted_qty)
            if succeed and res:
                orderid = res['clientOrderId']
        else:
            # Bybit使用币的数量，直接下单
            succeed, res = exchange_api.place_order(category=order_signal.category,
                                symbol=order_signal.symbol,
                                side=order_signal.side,
                                orderType=order_signal.orderType,
                                qty=str(formatted_qty))  # Bybit 需要字符串格式
            if succeed and res:
                orderid = res['result']['orderLinkId']

        return succeed, orderid
        # 只有在下单成功且有订单ID时才查询订单详情
        # if succeed and orderid:
        #     try:
        #         order_detail = exchange_api.get_order_detail(orderid)
        #         return succeed, order_detail
        #     except Exception as e:
        #         decision_logger.error(f"获取订单详情失败: {e}")
        #         return succeed, res  # 返回原始下单响应
        # else:
        #     decision_logger.error(f"{order_signal.exchange} 下单失败或缺少订单ID")
        #     return succeed, res

    def parse_order_result(self, exchange_name, order_result):
        """解析各交易所的订单结果，提取成交价格和数量
        返回: (avg_price, filled_qty, order_id, status)
        """
        if not order_result:
            return None, None, None, 'FAILED'

        try:
            if exchange_name == 'Binance':
                # Binance 返回格式
                avg_price = float(order_result.get('avgPrice', '0'))
                filled_qty = float(order_result.get('executedQty', '0'))
                order_id = order_result.get('orderId')
                status = order_result.get('status', 'UNKNOWN')
                return avg_price, filled_qty, order_id, status

            elif exchange_name == 'Bybit':
                # Bybit 返回格式
                if order_result.get('retCode') == 0 and order_result.get('result'):
                    result = order_result['result']
                    avg_price = float(result.get('avgPrice', '0'))
                    filled_qty = float(result.get('cumExecQty', '0'))
                    order_id = result.get('orderId')
                    status = result.get('orderStatus', 'UNKNOWN')
                    return avg_price, filled_qty, order_id, status

            elif exchange_name == 'Okx':
                # OKX 返回格式
                if order_result.get('code') == '0' and order_result.get('data'):
                    data = order_result['data'][0]
                    # OKX 下单后需要查询订单详情获取成交信息
                    order_id = data.get('ordId')
                    # 这里返回订单ID，实际成交信息需要后续查询
                    return None, None, order_id, 'PENDING'

            elif exchange_name == 'Bitget':
                # Bitget v2 返回格式
                if order_result.get('code') == '00000' and order_result.get('data'):
                    data = order_result['data']
                    order_id = data.get('orderId')
                    # Bitget 下单后也需要查询订单详情
                    return None, None, order_id, 'PENDING'

        except Exception as e:
            decision_logger.error(f"解析{exchange_name}订单结果失败: {e}")

        return None, None, None, 'FAILED'


# 各交易所连接函数
async def binance_ws(state:TradingState):
    params = [f"{s.lower()}@bookTicker" for s in state.symbols]
    uri = f"wss://fstream.binance.com/stream?streams=" + "/".join(params)
    config = {
        'uri': uri,
        'parser': parse_binance
    }
    await generic_ws_connector('Binance', config, state)

async def bybit_ws(state:TradingState):
    config = {
        'uri': "wss://stream.bybit.com/v5/public/linear",
        'subscribe_msg': {
            "op": "subscribe",
            "args": [f"tickers.{symbol}" for symbol in state.symbols]
        },
        'parser': parse_bybit
    }
    await generic_ws_connector('Bybit', config, state)

async def bitget_keepalive(ws):
    """Bitget自定义keepalive函数"""
    while True:
        await asyncio.sleep(25)  # ping every 25 seconds
        try:
            await ws.send("ping")
        except:
            break  # stop if connection closed

async def bitget_ws(state:TradingState):
    """Bitget WebSocket连接，现在使用generic_ws_connector"""
    config = {
        'uri': "wss://ws.bitget.com/v2/ws/public",
        'subscribe_msg': {
            "op": "subscribe",
            "args": [
                {
                    "instType": "USDT-FUTURES",
                    "channel": "ticker",
                    "instId": sym
                } for sym in state.symbols
            ]
        },
        'parser': parse_bitget,
        'keepalive': bitget_keepalive
    }
    await generic_ws_connector('Bitget', config, state)


async def okx_ws(state:TradingState):
    config = {
        'uri': "wss://ws.okx.com:8443/ws/v5/public",
        'subscribe_msg': {
            "op": "subscribe",
            "args": [{
                "channel": "tickers",
                "instId": symbol
            } for symbol in state.okx_symbols]
        },
        'parser': parse_okx
    }
    await generic_ws_connector('Okx', config, state)

async def save_market_data(state:TradingState):
    while True:
        try:
            state.redis_client.set("trading:exchange_data", json.dumps(state.shared_data))

        except Exception as e:
            print(f"Error updating Redis: {e}")

        await asyncio.sleep(0.01)
# 显示函数 (传入state参数)
def display_exchange_data(state: TradingState):
    print("+------------+-----------+-------------+-------------+")
    print("|   Symbol   | Exchange  | Bid Price   |  Ask Price  |")
    print("+------------+-----------+-------------+-------------+")
    for symbols, orderbooks in state.shared_data.items():
        for exchange, book in orderbooks.items():
            
            bid = book['bid'] if book['bid'] else "..."
            ask = book['ask'] if book['ask'] else "..."

            print(f"| {symbols:<10} | {exchange:<9} | {str(bid):<11} | {str(ask):<11} |")
    print("+------------+-----------+-------------+-------------+")        

async def display_terminal(state: TradingState):
    while True:
        display_exchange_data(state)
        print(f"Last update: {time.strftime('%Y-%m-%d %H:%M:%S')}")

        await asyncio.sleep(1)        

async def main():
    # 初始化

    print(f"Monitoring {len(state.symbols)} currencies")
    
    # 启动所有任务
    tasks = [
        binance_ws(state),
        bitget_ws(state),
        okx_ws(state),
        bybit_ws(state),
        # display_terminal(state),
        # save_market_data(state),
    ]
    
    await asyncio.gather(*tasks)

if __name__ == '__main__':  
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n程序已手动终止")