import os
import logging
import json
from datetime import datetime

def setup_loggers(log_dir='./cross_exchange_re/log'):
    """
    设置并返回所有日志记录器。
    此函数会检查，确保每个日志处理器只被添加一次，以防止重复日志。
    日志文件名包含当前日期。
    """
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)

    # 获取当前日期作为文件名后缀
    current_date = datetime.now().strftime('%Y-%m-%d')

    log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    formatter = logging.Formatter(log_format)

    # 1. 设置决策日志记录器
    decision_logger = logging.getLogger('decision_logger')
    decision_logger.setLevel(logging.INFO)
    if not decision_logger.handlers: # 检查是否已添加过handler
        decision_filename = f'decision_log_{current_date}.txt'
        decision_handler = logging.FileHandler(os.path.join(log_dir, decision_filename))
        decision_handler.setFormatter(formatter)
        decision_logger.addHandler(decision_handler)

    # 2. 设置输出日志记录器
    output_logger = logging.getLogger('output_logger')
    output_logger.setLevel(logging.INFO)
    if not output_logger.handlers: # 检查是否已添加过handler
        output_filename = f'output_log_{current_date}.txt'
        output_handler = logging.FileHandler(os.path.join(log_dir, output_filename))
        output_handler.setFormatter(formatter)
        output_logger.addHandler(output_handler)

    # 3. 新增交易历史日志记录器
    trade_history_logger = logging.getLogger('history_logger')
    trade_history_logger.setLevel(logging.INFO)
    if not trade_history_logger.handlers: # 检查是否已添加过handler
        history_filename = f'trade_history_{current_date}.log'
        trade_history_handler = logging.FileHandler(os.path.join(log_dir, history_filename))
        trade_history_handler.setFormatter(formatter)
        trade_history_logger.addHandler(trade_history_handler)

    return decision_logger, output_logger, trade_history_logger