from dataclasses import dataclass
import time
from decimal import Decimal
import math
from typing import Dict, <PERSON><PERSON>
from pybit.unified_trading import HTTP
from pybit.exceptions import FailedRequestError, InvalidRequestError
from pybit.unified_trading import WebSocketTrading
from time import sleep
import re
# Bitget v1 API (保留兼容性)
try:
    from pybitget import Client
    from pybitget import logger
    from pybitget.enums import NEW_BUY, NEW_SELL
    from pybitget.utils import random_string
    from pybitget.exceptions import BitgetAPIException,BitgetParamsException,BitgetRequestException
    PYBITGET_AVAILABLE = True
    print("✅ pybitget (v1) 导入成功")
except ImportError as e:
    PYBITGET_AVAILABLE = False
    print(f"⚠️ pybitget (v1) 导入失败: {e}")
    # 定义占位符类以避免NameError
    class BitgetAPIException(Exception): pass
    class BitgetParamsException(Exception): pass
    class BitgetRequestException(Exception): pass

# Bitget v2 API (新版本)
try:
    import bitget.v2.mix.order_api as bitgetOrderApi
    import bitget.v2.mix.account_api as bitgetAccountApi
    import bitget.v2.mix.market_api as bitgetMarketApi
    import bitget.bitget_api as bitgetBaseApi
    BITGET_V2_AVAILABLE = True
    print("Bitget v2 API 导入成功")
except ImportError as e:
    BITGET_V2_AVAILABLE = False
    print(f"Bitget v2 API 导入失败: {e}")
except Exception as e:
    BITGET_V2_AVAILABLE = False
    print(f"Bitget v2 API 导入异常: {e}")
import uuid

# OKX API
try:
    from okx import OkxRestClient
    from okx.exceptions import OkxApiException,OkxParamsException,OkxRequestException
    OKX_AVAILABLE = True
    print("✅ OKX API 导入成功")
except ImportError as e:
    OKX_AVAILABLE = False
    print(f"⚠️ OKX API 导入失败: {e}")
    # 定义占位符类
    class OkxApiException(Exception): pass
    class OkxParamsException(Exception): pass
    class OkxRequestException(Exception): pass

# from bitget import BitgetSync
from abc import ABC,abstractmethod

# Binance API
try:
    from binance import Client as BinanceClient
    from binance.exceptions import BinanceAPIException, BinanceRequestException, BinanceOrderException
    BINANCE_AVAILABLE = True
    print("✅ Binance API 导入成功")
except ImportError as e:
    BINANCE_AVAILABLE = False
    print(f"⚠️ Binance API 导入失败: {e}")
    # 定义占位符类
    class BinanceAPIException(Exception): pass
    class BinanceRequestException(Exception): pass
    class BinanceOrderException(Exception): pass
import logging

output_logger = logging.getLogger('output_logger')


class BaseExchangeHTTP(ABC):
    def __init__(self, exchange_name,api_key,api_secret,passphrase,isTest=False):
        self.exchange_name = exchange_name
        self.session = None
        self.api_key = api_key
        self.passphrase = passphrase
        self.api_secret = api_secret
        self.isTest = isTest

        # 余额缓存系统
        self._balance_cache = None
        self._balance_cache_timestamp = 0

        # 符号信息缓存系统
        self._symbol_info_cache = {}  # {symbol: (min_qty, max_qty, ct_val)}
        self._symbol_info_loaded = False
        
    @abstractmethod
    def connect(self) -> bool:
        pass

    @abstractmethod
    def place_order(self):
        """return True/False, response"""
        pass
    
    @abstractmethod
    def place_limit_order(self):
        """return True/False,response"""
        pass

    @abstractmethod
    def cancel_order(self):
        """return bool,Dict (t/f, response)"""
        pass

    @abstractmethod
    def log_error(self):
        pass

    @abstractmethod
    def get_balance_info(self, force_update=False):
        """return total equity, total balance(in usdt) float
        Args:
            force_update: 是否强制更新，默认False使用缓存
        """
        pass

    @abstractmethod
    def get_order_detail(self):
        """返还字典，键 有
        symbol 交易对名称（统一）
        avg_price, 成交平均价格 float
        exec_qty,  成交数量 float
        exec_fee,  成交手续费   float
        created_time, 交易所系统创建订单时间 int
        our_time， 我们本地记录的创建时间   int
        """
        pass

    @abstractmethod
    def error_handler(self):
        pass

    @abstractmethod
    def load_all_symbol_info(self):
        """加载所有合约信息到缓存"""
        pass

    def get_symbol_info(self, symbol):
        """获取指定symbol的信息，从缓存中获取
        Args:
            symbol: 交易对符号
        Returns:
            (min_qty, max_qty, ct_val) 或 (None, None, None)
        """
        # 如果缓存未加载，先加载
        if not self._symbol_info_loaded:
            output_logger.info(f"{self.exchange_name}: 符号缓存未加载，开始批量加载...")
            self.load_all_symbol_info()

        # 从缓存中获取
        result = self._symbol_info_cache.get(symbol, (None, None, None))
        if result[0] is not None:
            output_logger.debug(f"{self.exchange_name}: ✅ 从缓存获取 {symbol} 信息")
        else:
            output_logger.warning(f"{self.exchange_name}: ❌ 缓存中未找到 {symbol} 信息")

        return result

    @abstractmethod
    def get_orderbook(self):
        """return orderbook asks, bids"""
        pass
    def lcm(self,a, b):
        """
        计算两个浮点数 a 和 b 的最小公倍数。
        
        参数:
            a (float): 第一个浮点数
            b (float): 第二个浮点数
            
        返回:
            float: a 和 b 的最小公倍数
        """
        # 1. 将浮点数转换为字符串，以精确确定小数位数
        dec_a = Decimal(str(a))
        dec_b = Decimal(str(b))
        
        # 2. 获取各自的小数位数
        scale_a = -dec_a.as_tuple().exponent
        scale_b = -dec_b.as_tuple().exponent
        
        # 3. 找出最大的小数位数
        max_scale = max(scale_a, scale_b)
        
        # 4. 计算乘数 (10 ^ max_scale)
        multiplier = 10 ** max_scale
        
        # 5. 将原数乘以 multiplier 得到整数
        int_a = int(dec_a * multiplier)
        int_b = int(dec_b * multiplier)
        
        # 6. 计算两个整数的最大公约数
        gcd_value = math.gcd(int_a, int_b)
        
        # 7. 计算两个整数的最小公倍数
        # LCM(a, b) = |a * b| / GCD(a, b)
        lcm_value = abs(int_a * int_b) // gcd_value
        
        # 8. 将整数的最小公倍数转换回浮点数
        result = lcm_value / multiplier
        
        return result
    
    def generate_orderid(self,symbol:str):
        """通过交易对生成orderid"""
        return symbol + "G666G" + str(int(time.time()*1000))
    
    def strip_orderid(self,orderid:str):
        """从orderid中提取交易对和时间戳"""
        return orderid.split('G666G')
    
class BybitExchangeHTTP(BaseExchangeHTTP):
    def __init__(self,exchange_name,api_key,api_secret,isTest=False):
        super().__init__(exchange_name, api_key, api_secret, None,isTest)
        self.exchange_name = 'Bybit'

    def log_error(self,e):
        output_logger.info(f'错误代码: {e.status_code}, 错误信息: {e.message}')

    def connect(self) -> bool:
        try:
            bybit_session = HTTP(
            testnet=False,
            demo=self.isTest,
            api_key=self.api_key,
            api_secret=self.api_secret,
        )
            self.session = bybit_session
            return True
        
        except Exception as e:
            self.error_handler(e)
    
    def load_all_symbol_info(self):
        """加载所有线性合约信息到缓存"""
        try:
            output_logger.info("Bybit: 开始加载所有合约信息...")

            # 获取所有线性合约信息
            inst_info = self.session.get_instruments_info(category='linear',limit=1000)
            if inst_info['retCode'] == 0:
                contracts = inst_info['result']['list']
                loaded_count = 0

                for contract in contracts:
                    symbol = contract['symbol']
                    lot_size_filter = contract.get('lotSizeFilter', {})
                    min_order_qty = lot_size_filter.get('minOrderQty')
                    max_order_qty = lot_size_filter.get('maxOrderQty')

                    if min_order_qty and max_order_qty:
                        # Bybit直接使用币的数量，合约面值为1
                        self._symbol_info_cache[symbol] = (
                            float(min_order_qty),
                            float(max_order_qty),
                            1.0
                        )
                        loaded_count += 1

                self._symbol_info_loaded = True
                output_logger.info(f"Bybit: 成功加载 {loaded_count} 个合约信息")
            else:
                output_logger.error(f"Bybit: 获取合约信息失败，retCode: {inst_info['retCode']}")

        except Exception as e:
            self.error_handler(e)
            output_logger.error("Bybit: 加载合约信息异常")
        
    def get_orderbook(self,symbol):
        try:
            ret = self.session.get_orderbook(category='linear',symbol=symbol)
            if ret['retCode'] == 0:
                asks = ret['result']['a']
                bids = ret['result']['b']
                return asks, bids
            else:
                output_logger.info(f'Bybit get_orderbook retCode: {ret["retCode"]}, retMsg: {ret["retMsg"]}')
        except Exception as e:
            self.error_handler(e)

    def place_order(self,category,symbol,side,orderType,qty,reduce_only='false'):
        """note, qty is a string
        now only limited to orderType = Market because price is not in args
        orderType Limit / Market
        side Buy / Sell
        https://bybit-exchange.github.io/docs/v5/order/create-order#response-parameters
        
        res["orderId"] = symbol_timestamp"
        """
        try:
            orderid = self.generate_orderid(symbol)
            f_orderType = orderType[0].upper() + orderType[1:]
            f_side = side[0].upper() + side[1:]
            response = self.session.place_order(
            category=category,
            symbol=symbol,
            side=f_side,
            orderType=f_orderType,
            qty=qty,
            orderLinkId=orderid,
            reduceOnly = reduce_only)

            response['orderid'] = orderid

            print(f'bybit place_order response: {response}')
            return True,response

        except Exception as e:
            self.error_handler(e)
            return False, None
        


    def place_limit_order(self,category,symbol,side,orderType,qty,price,reduce_only='false'):
        """note, qty is a string
        orderType Limit
        side Buy / Sell
        https://bybit-exchange.github.io/docs/v5/order/create-order#response-parameters
        限价单，不写timeinforce默认使用GTC（订单有效直到被完全执行/手动取消)
        """
        try:
            orderid = self.generate_orderid(symbol)
            f_orderType = orderType[0].upper() + orderType[1:]
            f_side = side[0].upper() + side[1:]
            response = self.session.place_order(
            category=category,
            symbol=symbol,
            side=f_side,
            orderType=f_orderType,
            qty=qty,
            price = price,
            timeInForce = 'GTC',
            orderLinkId=orderid,
            reduceOnly = reduce_only
            )

            response['orderid'] = orderid

            print(f'bybit place_limit_order response: {response}')
            return True,response

        except Exception as e:
            self.error_handler(e)
            return False, None

        
    def get_order_detail(self, orderid:str):
        """order id is self generated cutomized order id
            created time 是交易所系统中生成订单的时间
            our_time 是我们使用交易所api下单前记录的时间
                qty是下单量，cumExecQty是累积已经成交量"""
        try:
            result = self.session.get_open_orders(category='linear', orderLinkId=orderid)
            print(f'bybit get_order_detail result: {result}')
            order_detail = result['result']['list'][0]
            _, timestamp = self.strip_orderid(orderid)
            avg_price = 0
            if order_detail['avgPrice'] != '':
                avg_price = order_detail['avgPrice']
            # 如果返回为空说明没有成交，暂时设为0
            
            detail = {"symbol": order_detail['symbol'],
                    "avg_price": avg_price,
                    "exec_qty": float(order_detail['cumExecQty']),
                    "exec_fee": abs(float(order_detail['cumExecFee'])),
                    "created_time": int(order_detail['createdTime']),
                    "our_time":int(timestamp),
                    "exchange":self.exchange_name,
                    "orderid":orderid,
                    "status":order_detail['orderStatus'],
                    }
            return detail
            
        except Exception as e:
            self.error_handler(e)

    def cancel_order(self,orderid)-> Tuple[bool,Dict]:
        symbol, timestamp = self.strip_orderid(orderid)
        try:
            result = self.session.cancel_order(category = 'linear',
                                      symbol = symbol,
                                      orderLinkId = orderid)
            return True, result
        except Exception as e:
            self.error_handler(e)
            return False, None


    def get_balance_info(self, force_update=False):
        """获取余额信息 - 带缓存机制
        Args:
            force_update: 是否强制更新，默认False使用缓存
        Returns:
            (total_equity, available_balance) 或 (None, None)
        """
        # 检查是否使用缓存
        if not force_update and self._balance_cache is not None:
            return self._balance_cache

        try:
            res = self.session.get_wallet_balance(accountType="UNIFIED")
            if res['retCode'] == 0:
                balance_info = res['result']['list'][0]
                total_equity = balance_info['totalEquity']

                # 调试：打印实际的数据结构
                output_logger.info(f"Bybit balance_info structure: {balance_info}")

                # 查找USDT币种的余额
                usdt_balance = None
                if 'coin' in balance_info and balance_info['coin']:
                    for coin in balance_info['coin']:
                        if coin['coin'] == 'USDT':
                            usdt_balance = coin.get('availableBalance')
                            break

                    # 如果没找到USDT，使用第一个币种的余额
                    if usdt_balance is None and len(balance_info['coin']) > 0:
                        first_coin = balance_info['coin'][0]
                        usdt_balance = first_coin.get('availableBalance')

                # 如果还是没找到，尝试直接从账户信息获取
                if usdt_balance is None:
                    usdt_balance = balance_info.get('totalAvailableBalance') or balance_info.get('totalWalletBalance')

                # 如果账户余额为0或空，这是正常情况
                if usdt_balance is not None or total_equity == '0':
                    usdt_balance = usdt_balance or '0'  # 如果为None，设为'0'
                    result = (float(total_equity), float(usdt_balance))

                    # 更新缓存
                    self._balance_cache = result
                    self._balance_cache_timestamp = time.time()

                    return result
                else:
                    output_logger.info(f"Bybit: 未找到可用余额信息。数据结构: {balance_info}")
                    return None, None
            else:
                output_logger.info(f"Bybit get_balance_info retCode: {res['retCode']}, retMsg: {res.get('retMsg', 'Unknown error')}")
                return None, None

        except Exception as e:
            self.error_handler(e)
            return None,None

    def load_all_symbol_info(self):
        """加载所有线性合约信息到缓存"""
        try:
            output_logger.info("Bybit: 开始加载所有合约信息...")

            # 获取所有线性合约信息
            inst_info = self.session.get_instruments_info(category='linear')
            if inst_info['retCode'] == 0:
                contracts = inst_info['result']['list']
                loaded_count = 0

                for contract in contracts:
                    symbol = contract['symbol']
                    lot_size_filter = contract.get('lotSizeFilter', {})
                    min_order_qty = lot_size_filter.get('minOrderQty')
                    max_order_qty = lot_size_filter.get('maxOrderQty')

                    if min_order_qty and max_order_qty:
                        # Bybit直接使用币的数量，合约面值为1
                        self._symbol_info_cache[symbol] = (
                            float(min_order_qty),
                            float(max_order_qty),
                            1.0
                        )
                        loaded_count += 1

                self._symbol_info_loaded = True
                output_logger.info(f"Bybit: 成功加载 {loaded_count} 个合约信息")
            else:
                output_logger.error(f"Bybit: 获取合约信息失败，retCode: {inst_info['retCode']}")

        except Exception as e:
            self.error_handler(e)
            output_logger.error("Bybit: 加载合约信息异常")

    def get_order_details(self, order_id, symbol):
        """查询订单详情"""
        try:
            response = self.session.get_open_orders(category="linear", symbol=symbol, orderId=order_id)
            if response.get('retCode') == 0 and response.get('result', {}).get('list'):
                return response['result']['list'][0]
            return None
        except Exception as e:
            self.error_handler(e)
            return None

    def error_handler(self, e):
        if isinstance(e, (InvalidRequestError,FailedRequestError)):
            self.log_error(e)
        else:
            output_logger.info(f"Bybit error: {e}")

    def get_positions(self):
        try:
            response = self.session.get_positions(category='linear',settleCoin='USDT',limit=200)
            if response['retCode'] == 0:
                return response['result']['list']
        except Exception as e:
            logger.error(f'获取bybit仓位信息时出错: {e}')
            return None
        
    def close_all_positions(self) -> bool:
        """
        遍历所有持仓并下市价单平仓。
        """
        logger.info(f"{self.name}: 正在尝试平掉所有仓位。")
        try:
            positions = self.get_positions()
            if not positions:
                logger.info(f"{self.name}: 没有需要平仓的仓位。")
                return True

            all_closed = True
            for position in positions:
                size = float(position.get('size', '0'))
                if size > 0:
                    symbol = position['symbol']
                    # 确定平仓方向：如果持有多头(Buy)，则卖出(SELL)平仓，反之亦然。
                    side = 'sell' if position['side'] == 'buy' else 'buy'
                    
                    logger.info(f"正在平仓 {symbol}: {side.value} {size}")
                    succ,response = self.place_order(category='linear',symbol=symbol,side=side,orderType='market',qty=size,reduce_only='true')

                    if not succ:
                        logger.error(f"为 {symbol} 下平仓单失败。")
                        all_closed = False
                    else:
                        logger.info(f"已成功为 {symbol} 提交平仓单。")
            
            return all_closed

        except Exception as e:
            logger.exception(f"{self.name}: 平仓所有仓位时发生错误: {e}")
            return False
        
    def cancel_all_orders(self) -> bool:
        """取消所有线性合约的挂单。"""
        logger.info(f"{self.name}: 正在尝试取消所有挂单。")
        try:
            response = self.session.cancel_all_orders(category='linear',settleCoin='USDT')
            if response.get('retCode') == 0:
                logger.info(f"{self.name}: 已成功发送取消所有挂单的请求。")
                return True
            else:
                logger.error(f"{self.name}: 取消所有挂单失败。 交易所返回: {response.get('retMsg')}")
                return False
        except Exception as e:
            logger.exception(f"{self.name}: 取消所有挂单时发生错误: {e}")
            return False
        
class Bitgetv1HTTP(BaseExchangeHTTP):
    def __init__(self, exchange_name,api_key,api_secret,passphrase,isTest=False):
        self.exchange_name = 'Bitget'
        self.session = None
        self.api_key = api_key
        self.passphrase = passphrase
        self.api_secret = api_secret
        self.isTest = isTest
        
    def connect(self):
        """return True if successfully connected"""
        try:
            if not PYBITGET_AVAILABLE:
                output_logger.error("pybitget 库不可用，无法使用 Bitget v1 API")
                return False

            client = Client(self.api_key, self.api_secret, passphrase=self.passphrase)
            self.session = client
            return True
        except Exception as e:
            # logger.debug(e)
            self.error_handler(e)
            return False

    def _convert_symbol(self,symbol:str) -> str:
        return symbol + '_UMCBL'

    def place_order(self,category,symbol,side,orderType,qty):
        """category linear/ .../ should not matter because in v1 it is included in symbol name
        orderType = market/ limit, orderType == limit -> price is mandatory
        side = NEW_BUY NEW_SELL BUY_CLOSE SELL_CLOSE, check enum
        we fix marginCoin = USDT for our own purpose
        raise exceptions.BitgetAPIException(response)
        pybitget.exceptions.BitgetAPIException: API Request Error(code=40762):
        The order amount exceeds the balance

        真实下单
        """

        # symbol = "BTCUSDT_UMCBL"
        try:
            category = "linear"
            marginCoin = "USDT"
            symbol = self._convert_symbol(symbol)
            size = qty

            # 生成自定义订单ID
            orderid = self.generate_orderid(symbol.replace('_UMCBL', ''))

            if side == 'buy':
                f_side = NEW_BUY
            elif side == 'sell':
                f_side = NEW_SELL
            else:
                #TODO
                # not supported yet
                # BUY_CLOSE and SELL_CLOSE, is it necessary
                raise ValueError
            s = self.session.mix_place_order(symbol, marginCoin, size, f_side, orderType, clientOrderId=orderid)
            # logger.debug(s)

            # 添加自定义订单ID到响应中
            if s:
                s['orderid'] = orderid

            return True, s
            #TODO
            # 失败后的处理，失败原因

            # s = client.mix_place_order(symbol, marginCoin, size, side, orderType, price=price, clientOrderId=random_string("cntest"))
            # if orderType = limit, put in price
        except Exception as e:
            # logger.debug(e)
            self.error_handler(e)
            return False, None
        
    def log_error(self,e):
        output_logger.info(f'Bitget status code: {e.status_code}. Error Message: {e.message}')

    def get_balance_info(self, force_update=False):
        """获取余额信息 - 带缓存机制
        Args:
            force_update: 是否强制更新，默认False使用缓存
        Returns:
            (total_equity, available_balance) 或 (None, None)
        """
        # 检查是否使用缓存
        if not force_update and self._balance_cache is not None:
            return self._balance_cache

        try:
            result = self.session.mix_get_accounts(productType='UMCBL')
            equity = result['data'][0]['equity']
            available = result['data'][0]['available']

            # 更新缓存
            balance_result = (equity, available)
            self._balance_cache = balance_result
            self._balance_cache_timestamp = time.time()

            return balance_result
        except Exception as e:
            # logger.debug(e)
            self.error_handler(e)
            return None,None

    def load_all_symbol_info(self):
        """加载所有合约信息到缓存"""
        try:
            output_logger.info("Bitget v1: 开始加载所有合约信息...")

            # 获取所有UMCBL合约信息
            result = self.session.mix_get_contracts(productType='UMCBL')
            print(f'Bitget v1 contracts: {result}')

            if result['code'] == '00000' and result['data']:
                loaded_count = 0

                for contract in result['data']:
                    symbol = contract['symbol']
                    min_trade_num = contract.get('minTradeNum')
                    max_trade_num = contract.get('maxTradeNum')

                    if min_trade_num and max_trade_num:
                        # 转换为标准symbol格式 (去掉_UMCBL后缀)
                        standard_symbol = symbol.replace('_UMCBL', '')
                        self._symbol_info_cache[standard_symbol] = (
                            float(min_trade_num),
                            float(max_trade_num),
                            1.0
                        )
                        loaded_count += 1

                self._symbol_info_loaded = True
                output_logger.info(f"Bitget v1: 成功加载 {loaded_count} 个合约信息")
            else:
                output_logger.error(f"Bitget v1: 获取合约信息失败，code: {result.get('code')}")

        except Exception as e:
            self.error_handler(e)
            output_logger.error("Bitget v1: 加载合约信息异常")

    def get_orderbook(self, symbol):
        """获取订单簿数据"""
        try:
            symbol = self._convert_symbol(symbol)
            result = self.session.mix_get_depth(symbol=symbol, limit=15)
            if result['code'] == '00000' and result['data']:
                asks = result['data']['asks']  # 卖单 [[price, size], ...]
                bids = result['data']['bids']  # 买单 [[price, size], ...]
                return asks, bids
            return None, None
        except Exception as e:
            self.error_handler(e)
            return None, None

    def get_order_detail(self, orderid: str):
        """获取订单详情 - Bitget v1"""
        try:
            symbol, timestamp = self.strip_orderid(orderid)
            symbol_converted = self._convert_symbol(symbol)

            # 使用 Bitget v1 API 查询订单详情
            response = self.session.mix_get_order_detail(symbol_converted, orderid)

            if response and response.get('code') == '00000' and response.get('data'):
                order_data = response['data']
                detail = {
                    "symbol": symbol,
                    "avg_price": float(order_data.get('priceAvg', '0')),
                    "exec_qty": float(order_data.get('filledQty', '0')),
                    "exec_fee": abs(float(order_data.get('fee', '0'))),
                    "created_time": int(order_data.get('cTime', '0')),
                    "our_time": int(timestamp),
                    "exchange": self.exchange_name,
                    "orderid": orderid,
                }
                return detail
            else:
                output_logger.info(f'Bitget v1 get_order_detail failed, orderid: {orderid}, response: {response}')
                return None

        except Exception as e:
            self.error_handler(e)
            return None

    def error_handler(self, e):
        if isinstance(e,(BitgetAPIException,BitgetParamsException,BitgetRequestException)):
            self.log_error(e)
        else:
            output_logger.info(e)

class BitgetV2HTTP(BaseExchangeHTTP):
    """Bitget v2 API 实现"""

    def __init__(self, exchange_name, api_key, api_secret, passphrase, isTest=False):
        super().__init__(exchange_name, api_key, api_secret, passphrase, isTest)
        self.exchange_name = 'Bitget'
        self.base_api = None

    def connect(self):
        try:
            if not BITGET_V2_AVAILABLE:
                output_logger.error("Bitget v2 API 不可用，请安装正确的 bitget 库")
                return False

            # 初始化基础API客户端
            self.base_api = bitgetBaseApi.BitgetApi(self.api_key, self.api_secret, self.passphrase)

            # 测试连接
            test_response = self.base_api.get("/api/v2/mix/market/contracts", {"productType": "USDT-FUTURES"})
            if test_response.get('code') == '00000':
                output_logger.info("Bitget v2 连接成功")
                return True
            else:
                output_logger.error(f"Bitget v2 连接测试失败: {test_response}")
                return False

        except Exception as e:
            self.error_handler(e)
            return False

    def place_order(self, category, symbol, side, orderType, qty, action=None):
        try:
            symbol = self._convert_symbol(symbol)

            # 生成自定义订单ID
            orderid = self.generate_orderid(symbol)

            # 构建v2 API参数
            params = {
                "symbol": symbol,
                "productType": "USDT-FUTURES",
                "marginMode": "crossed",
                "marginCoin": "USDT",
                "size": str(qty),
                "side": side,
                # "tradeSide": action,
                "orderType": orderType,
                "force": "gtc",  # Good Till Cancel
                "clientOid": orderid  # 添加自定义订单ID
            }
            print(f'Bitget v2 下单参数: {params}')
            # 使用v2 API下单
            response = self.base_api.post("/api/v2/mix/order/place-order", params)

            if response.get('code') == '00000':
                # 添加自定义订单ID到响应中
                if response:
                    response['orderid'] = orderid
                return True, response
            else:
                output_logger.error(f"Bitget v2 下单失败: {response}")
                return False, response

        except Exception as e:
            self.error_handler(e)
            return False, None
        
    def place_limit_order(self, category, symbol, side, orderType, qty, price,action=None):
        try:
            symbol = self._convert_symbol(symbol)

            # 生成自定义订单ID
            orderid = self.generate_orderid(symbol)
            if orderType != 'limit':
                output_logger.error(f"bitget place_limit_order 在尝试下市价单")
            # 构建v2 API参数
            params = {
                "symbol": symbol,
                "productType": "USDT-FUTURES",
                "marginMode": "crossed",
                "marginCoin": "USDT",
                "size": str(qty),
                "side": side,
                # "tradeSide": action,
                "orderType": orderType,
                "force": "gtc",  # Good Till Cancel
                "clientOid": orderid,  # 添加自定义订单ID
                "price": price,
            }
            print(f'Bitget v2 下单参数: {params}')
            # 使用v2 API下单
            response = self.base_api.post("/api/v2/mix/order/place-order", params)

            if response.get('code') == '00000':
                # 添加自定义订单ID到响应中
                if response:
                    response['orderid'] = orderid
                return True, response
            else:
                output_logger.error(f"Bitget v2 下单失败: {response}")
                return False, response

        except Exception as e:
            self.error_handler(e)
            return False, None

    def get_balance_info(self, force_update=False):
        """获取余额信息 - 带缓存机制
        Args:
            force_update: 是否强制更新，默认False使用缓存
        Returns:
            (total_equity, available_balance) 或 (None, None)
        """
        # 检查是否使用缓存
        if not force_update and self._balance_cache is not None:
            return self._balance_cache

        try:
            # 使用v2 API获取账户信息
            response = self.base_api.get("/api/v2/mix/account/accounts", {"productType": "USDT-FUTURES"})

            if response.get('code') == '00000' and response.get('data'):
                account_data = response['data'][0]
                if account_data['marginCoin'] == 'USDT':
                    total_equity = float(account_data.get('accountEquity', '0'))
                    available_balance = float(account_data.get('available', '0'))

                    # 更新缓存
                    result = (total_equity, available_balance)
                    self._balance_cache = result
                    self._balance_cache_timestamp = time.time()

                    return result
                else:
                    output_logger.error(f"Bitget v2 余额查询失败: {response}")
                    return None, None
            else:
                output_logger.error(f"Bitget v2 余额查询失败: {response}")
                return None, None

        except Exception as e:
            self.error_handler(e)
            return None, None

    def load_all_symbol_info(self):
        """加载所有合约信息到缓存"""
        try:
            output_logger.info("Bitget v2: 开始加载所有合约信息...")

            # 使用v2 API获取所有合约信息
            response = self.base_api.get("/api/v2/mix/market/contracts", {"productType": "USDT-FUTURES"})

            if response.get('code') == '00000' and response.get('data'):
                loaded_count = 0

                for contract in response['data']:
                    symbol = contract['symbol']
                    min_trade_num = contract.get('minTradeNum', '1')
                    max_trade_num = contract.get('maxOrderQty', '999999')

                    if min_trade_num and max_trade_num:
                        # 转换为标准symbol格式
                        standard_symbol = self._convert_symbol_to_standard(symbol)
                        self._symbol_info_cache[standard_symbol] = (
                            float(min_trade_num),
                            float(max_trade_num),
                            1.0
                        )
                        loaded_count += 1

                self._symbol_info_loaded = True
                output_logger.info(f"Bitget v2: 成功加载 {loaded_count} 个合约信息")
            else:
                output_logger.error(f"Bitget v2: 获取合约信息失败，code: {response.get('code')}")

        except Exception as e:
            self.error_handler(e)
            output_logger.error("Bitget v2: 加载合约信息异常")

    def _convert_symbol_to_standard(self, exchange_symbol):
        """将交易所symbol转换为标准格式"""
        # Bitget v2 的symbol通常已经是标准格式
        return exchange_symbol

    def get_orderbook(self, symbol):
        """获取订单簿数据"""
        try:
            symbol = self._convert_symbol(symbol)
            response = self.base_api.get("/api/v2/mix/market/orderbook", {
                "symbol": symbol,
                "productType": "USDT-FUTURES",
                "limit": "15"
            })

            if response.get('code') == '00000' and response.get('data'):
                data = response['data']
                asks = data.get('asks', [])  # 卖单 [[price, size], ...]
                bids = data.get('bids', [])  # 买单 [[price, size], ...]
                return asks, bids
            return None, None
        except Exception as e:
            self.error_handler(e)
            return None, None

    def _convert_symbol(self, symbol):
        """转换symbol格式"""
        if symbol.endswith('USDT'):
            return symbol
        elif symbol.endswith('_UMCBL'):
            return symbol.replace('_UMCBL', '')
        return symbol + 'USDT'

    def error_handler(self, e):
        if isinstance(e, (BitgetAPIException, BitgetParamsException, BitgetRequestException)):
            self.log_error(e)
        else:
            output_logger.info(f'Bitget v2 error: {e}')

    def log_error(self, e):
        output_logger.info(f'Bitget v2 Error Code: {e.code}. Error Message: {e.message}.')

    def get_order_detail(self, orderid: str):
        """获取订单详情 - Bitget v2"""
        try:
            symbol, timestamp = self.strip_orderid(orderid)
            symbol_converted = self._convert_symbol(symbol)

            response = self.base_api.get("/api/v2/mix/order/detail", {
                "symbol": symbol_converted,
                "productType": "USDT-FUTURES",
                "clientOid": orderid
            })

            if response.get('code') == '00000' and response.get('data'):
                order_data = response['data']
                avg_price = 0
                if order_data.get('priceAvg','0') != '':
                    avg_price = order_data.get('priceAvg','0')
                detail = {
                    "symbol": symbol,
                    "avg_price": avg_price,
                    "exec_qty": float(order_data.get('baseVolume', '0')),
                    "exec_fee": abs(float(order_data.get('fee', '0'))),
                    "created_time": int(order_data.get('cTime', '0')),
                    "our_time": int(timestamp),
                    "exchange": self.exchange_name,
                    "orderid": orderid,
                    "status": order_data.get('state', 'UNKNOWN'),
                }
                return detail
            else:
                output_logger.info(f'Bitget v2 get_order_detail failed, orderid: {orderid}, response: {response}')
                return None

        except Exception as e:
            self.error_handler(e)
            return None
        
    def cancel_order(self, orderid: str):
        """return bool, response"""
        try:
            symbol, timestamp = self.strip_orderid(orderid)
            symbol_converted = self._convert_symbol(symbol)

            response = self.base_api.post("/api/v2/mix/order/cancel-order", {
                "symbol": symbol_converted,
                "productType": "usdt-futures",
                "clientOid": orderid
            })

            if response.get('code') == '00000':
                return True, response
            else:
                output_logger.info(f'Bitget v2 cancel_order failed, orderid: {orderid}, response: {response}')
                return False,response
        except Exception as e:
            self.error_handler(e)
            return False,None
        
    def get_order_details(self, order_id, symbol):
        """查询订单详情"""
        try:
            symbol_converted = self._convert_symbol(symbol)
            response = self.base_api.get("/api/v2/mix/order/detail", {
                "symbol": symbol_converted,
                "orderId": order_id
            })
            if response.get('code') == '00000' and response.get('data'):
                return response['data']
            return None
        except Exception as e:
            self.error_handler(e)
            return None
        
        
    def close_all_positions(self) -> bool:
        """(V2实现) 使用闪电平仓接口平掉所有仓位。"""
        logger.info(f"{self.name}: 正在尝试平掉所有仓位。")
        try:
            positions = self.get_positions()
            if not positions:
                logger.info(f"{self.name}: 没有需要平仓的仓位。")
                return True

            all_closed = True
            for pos in positions:
                symbol = pos['symbol']
                hold_side = pos.get('holdSide')
                params = {"symbol": symbol, "productType": "USDT-FUTURES", "holdSide": hold_side}
                
                logger.info(f"正在为 {symbol} ({hold_side}) 提交闪电平仓请求...")
                response = self.session.post("/api/v2/mix/order/close-positions", params)
                
                if response.get('code') != '00000':
                    logger.error(f"闪电平仓 {symbol} 失败: {response.get('msg')}")
                    all_closed = False
                else:
                    failures = response.get('data', {}).get('failureList', [])
                    if failures:
                        logger.error(f"闪电平仓 {symbol} 时，部分订单失败: {failures}")
                        all_closed = False
                    else:
                        logger.info(f"已成功为 {symbol} 提交闪电平仓请求。")
            return all_closed
        except Exception as e:
            logger.exception(f"{self.name}: 平仓所有仓位时发生错误: {e}")
            return False

    def cancel_all_orders(self) -> bool:
        """(V2实现) 取消所有U本位合约挂单"""
        logger.info(f"{self.name}: 正在尝试取消所有挂单。")
        try:
            params = {"productType": "USDT-FUTURES", "marginCoin": "USDT"}
            response = self.session.post("/api/v2/mix/order/cancel-all-orders", params)
            
            if response.get('code') == '00000':
                failures = response.get('data', {}).get('failureList', [])
                if failures:
                    logger.warning(f"{self.name}: 部分订单取消失败: {failures}")
                logger.info(f"{self.name}: 已成功提交取消所有挂单的请求。")
                return True # 即使部分失败，也认为请求本身成功了
            else:
                logger.error(f"{self.name}: 取消所有挂单失败: {response.get('msg')}")
                return False
        except Exception as e:
            logger.exception(f"{self.name}: 取消所有挂单时发生错误: {e}")
            return False

    def get_positions(self) -> list:
        """(V2实现) 获取所有U本位合约的持仓信息"""
        try:
            params = {"productType": "USDT-FUTURES"}
            response = self.session.get("/api/v2/mix/position/all-position", params)
            data = response.get('data',[])
            if response.get('code') == '00000' and response.get('data'):
                return data
            else:
                logger.error(f"{self.name}: 获取持仓失败: {response.get('msg')}")
                return []
        except Exception as e:
            logger.exception(f"{self.name}: 获取持仓时发生错误: {e}")
            return []
        
    def get_open_orders(self) -> list:
        """(V2实现) 获取所有U本位合约的当前挂单。"""
        logger.info(f"{self.name}: 正在获取所有挂单。")
        try:
            params = {"productType": "USDT-FUTURES"}
            response = self.session.get("/api/v2/mix/order/orders-pending", params)
            
            if response.get('code') == '00000' and response.get('data'):
                return response.get('data', {}).get('entrustedList', [])
            else:
                logger.error(f"{self.name}: 获取挂单失败: {response.get('msg')}")
                return []
        except Exception as e:
            logger.exception(f"{self.name}: 获取挂单时发生错误: {e}")
            return []


    
#     def error_handler(self):
#         return super().error_handler()
    
#     def log_error(self):
#         return super().log_error()
    
#     def get_balance_info(self):
#         return super().get_balance_info()
    

class OkxExchangeHTTP(BaseExchangeHTTP):
    def __init__(self, exchange_name, api_key, api_secret, passphrase, isTest=False):
        super().__init__(exchange_name, api_key, api_secret, passphrase, isTest)
        self.exchange_name = 'Okx'

    def connect(self):
        try:
            # 调试：打印连接参数
            output_logger.info(f"OKX connecting with isTest={self.isTest}, passphrase length={len(self.passphrase)}")

            api = OkxRestClient(self.api_key, self.api_secret, self.passphrase,simulation=self.isTest)
            self.session = api

            # 测试连接
            try:
                test_response = api.publicdata.get_instruments(instType="SWAP", instId="BTC-USDT-SWAP")
                output_logger.info(f"OKX connection test successful: {test_response.get('code', 'unknown')}")
                return True
            except Exception as test_e:
                output_logger.info(f"OKX connection test failed: {test_e}")
                return False

        except Exception as e:
            self.error_handler(e)
            return False

    def _symbol_convert(self,symbol:str) -> str:
        return symbol.replace('USDT','-USDT-SWAP')
    
    # Get instrument information to check lot size requirements
    def _get_instrument_info(self, instId):
        """Get instrument information including minSz and lotSz"""
        return self.session.publicdata.get_instruments(instType="SWAP", instId=instId)
    
    def _get_position_mode(self):
        """Get current position mode from account config"""
        account_config = self.session.account.get_account_config()
        return account_config['data'][0]['posMode']

    def place_order(self,category,symbol,side,orderType,qty,action,reduce_only='false'):
        """
        tdMode: https://www.okx.com/docs-v5/trick_zh/#order-management
        side: buy/ sell
        ordType: market, limit

        return success:bool, response(most likely a dict)
        """
        #fix lotSz problem, posSide problem. only consider long_short_mode
        posSide = ''
        if action == 'open' and side == 'buy':
            posSide = 'long'
        elif action == 'open' and side == 'sell':
            posSide = 'short'
        elif action == 'close' and side == 'sell':
            posSide = 'long'
        elif action =='close' and side == 'buy':
            posSide = 'short'
        tdMode = 'cross'
        orderid = self.generate_orderid(symbol)
        symbol = self._symbol_convert(symbol)

        instrument_info = self._get_instrument_info(symbol)
        min_size = float(instrument_info['data'][0]['minSz'])
        lot_size = float(instrument_info['data'][0]['lotSz'])
        print(f'min_size: {min_size}, lot_size: {lot_size}')
        # BTC-USDT-SWAP lotSz = 0.01
        succeed = True
        try:
            # api.trade.place_order(instId, tdMode, side, ordType, sz, ccy='', clOrdId='', posSide='', px='', pxUsd='', pxVol='', reduceOnly='', tgtCcy='', banAmend=False, tradeQuoteCcy='', stpMode='', attachAlgoOrds=None)
            print(f'placing order: {symbol}, {tdMode}, {side}, {orderType}, {qty}, {posSide}')
            response = self.session.trade.place_order(symbol,tdMode,side,orderType,qty,ccy='', clOrdId=orderid, posSide=posSide, px='', pxUsd='', pxVol='', reduceOnly=reduce_only, tgtCcy='', banAmend=False, tradeQuoteCcy='', stpMode='', attachAlgoOrds=None)
            if response['code'] == '1':
                succeed = False
                details = response['data'][0]
                # failed
                if isinstance(details['sCode'],str):
                    output_logger.info(f'Okx Message: {response["msg"]}')
                    output_logger.info(f'Okx Error Code: {details["sCode"]}, Error Message: {details["sMsg"]}')
            elif response['code'] == '0':
                succeed = True

            else:
                raise ValueError
            
            response['orderid'] = orderid
            print(f'Okx place_order response: {response}')
            return succeed, response
        except Exception as e:
            self.error_handler(e)
            return False, None
        
    def place_limit_order(self,category,symbol,side,orderType,qty,action,price,reduce_only='false'):
        """
        tdMode: https://www.okx.com/docs-v5/trick_zh/#order-management
        side: buy/ sell
        ordType: market, limit

        return success:bool, response(most likely a dict)
        """
        if orderType != 'limit':
            output_logger.error(f"在place_limit_order里尝试开市价单")
        posSide = ''
        if action == 'open' and side == 'buy':
            posSide = 'long'
        elif action == 'open' and side == 'sell':
            posSide = 'short'
        elif action == 'close' and side == 'sell':
            posSide = 'long'
        elif action =='close' and side == 'buy':
            posSide = 'short'
        tdMode = 'cross'
        orderid = self.generate_orderid(symbol)
        symbol = self._symbol_convert(symbol)

        instrument_info = self._get_instrument_info(symbol)
        min_size = float(instrument_info['data'][0]['minSz'])
        lot_size = float(instrument_info['data'][0]['lotSz'])
        print(f'min_size: {min_size}, lot_size: {lot_size}')
        # BTC-USDT-SWAP lotSz = 0.01
        succeed = True
        try:
            # api.trade.place_order(instId, tdMode, side, ordType, sz, ccy='', clOrdId='', posSide='', px='', pxUsd='', pxVol='', reduceOnly='', tgtCcy='', banAmend=False, tradeQuoteCcy='', stpMode='', attachAlgoOrds=None)
            print(f'placing order: {symbol}, {tdMode}, {side}, {orderType}, {qty}, {posSide}')
            response = self.session.trade.place_order(symbol,tdMode,side,orderType,qty,ccy='', clOrdId=orderid, posSide=posSide, px=str(price), pxUsd='', pxVol='', reduceOnly=reduce_only, tgtCcy='', banAmend=False, tradeQuoteCcy='', stpMode='', attachAlgoOrds=None)
            if response['code'] == '1':
                succeed = False
                details = response['data'][0]
                # failed
                if isinstance(details['sCode'],str):
                    output_logger.info(f'Okx Message: {response["msg"]}')
                    output_logger.info(f'Okx Error Code: {details["sCode"]}, Error Message: {details["sMsg"]}')
            elif response['code'] == '0':
                succeed = True

            else:
                raise ValueError
            
            response['orderid'] = orderid
            print(f'Okx place_order response: {response}')
            return succeed, response
        except Exception as e:
            self.error_handler(e)
            return False, None
        
    def get_balance_info(self, force_update=False):
        """获取余额信息 - 带缓存机制
        Args:
            force_update: 是否强制更新，默认False使用缓存
        Returns:
            (total_equity, available_balance) 或 (None, None)
        """
        # 检查是否使用缓存
        if not force_update and self._balance_cache is not None:
            return self._balance_cache

        try:
            res = self.session.account.get_balance()
            # 调试：打印完整响应
            output_logger.info(f"OKX get_balance response: {res}")

            if res['code'] == '0' and res['data']:
                account_data = res['data'][0]
                total_equity = account_data['totalEq']

                # 查找USDT的可用余额
                usdt_balance = None
                for detail in account_data['details']:
                    if detail['ccy'] == 'USDT':
                        usdt_balance = detail['availBal']
                        break

                # 如果没找到USDT，使用第一个币种的余额
                if usdt_balance is None and len(account_data['details']) > 0:
                    usdt_balance = account_data['details'][0]['availBal']

                if usdt_balance is not None or total_equity == '0':
                    usdt_balance = usdt_balance or '0'  # 如果为None，设为'0'
                    result = (float(total_equity), float(usdt_balance))

                    # 更新缓存
                    self._balance_cache = result
                    self._balance_cache_timestamp = time.time()

                    return result
                else:
                    output_logger.info(f"OKX: 未找到可用余额信息。账户数据: {account_data}")
                    return None, None
            else:
                output_logger.info(f"OKX get_balance_info code: {res['code']}, msg: {res.get('msg', 'Unknown error')}")
                return None, None
        except Exception as e:
            self.error_handler(e)
            return None,None
        
    def get_orderbook(self,symbol):
        try:
            ret = self.session.get_orderbook(category='linear',symbol=symbol)
            if ret['retCode'] == '0':
                asks = ret['result']['a']
                bids = ret['result']['b']
                return asks, bids
            else:
                output_logger.info(f'Bybit get_orderbook retCode: {ret["retCode"]}, retMsg: {ret["retMsg"]}')
        except Exception as e:
            self.error_handler(e)

    def get_order_detail(self, orderid:str):
        """https://www.okx.com/docs-v5/en/#order-book-trading-trade-get-order-details

        """
        try:
            print(f'okx get_order_detail orderid: {orderid}')
            symbol,timestamp = self.strip_orderid(orderid)
            print(f'okx get_order_detail symbol: {symbol}, timestamp: {timestamp}')
            instId = self._convert_to_okx_symbol(symbol)
            result = self.session.trade.get_order(instId, clOrdId=orderid)
            print(f'okx get_order_detail result: {result}') 
            if result["code"] == "0" and result.get("data") and len(result["data"]) > 0:
                order_detail = result["data"][0]
                filltime, avg_price = 0,0
                if order_detail.get('avgPx','0') != '':
                    avg_price = order_detail['avgPx']
                if order_detail['fillTime'] != '':
                    fill_time = order_detail['fillTime']

                detail = {"symbol": symbol,
                    "avg_price": avg_price,
                    "exec_qty": float(order_detail['accFillSz']),
                    "exec_fee": abs(float(order_detail['fee'])),
                    "created_time": int(filltime),
                    "our_time":int(timestamp),
                    "exchange":self.exchange_name,
                    "orderid":orderid,
                    "status":order_detail['state'],
                }
                print(f'okx get_order_detail detail: {detail}')
                return detail
            else:
                output_logger.info(f'okx get_order_detail failed, orderid: {orderid}, result: {result}')
                return None

        except Exception as e:
            print(f'okx get_order_detail exception: {e}')
            self.error_handler(e)
            return None
        
    def cancel_order(self,orderid):
        try:
            symbol,timestamp = self.strip_orderid(orderid)
            instId = self._convert_to_okx_symbol(symbol)
            result = self.session.trade.cancel_order(instId, clOrdId=orderid)
            if result["code"] == "0" and result.get("data") and len(result["data"]) > 0:

                return True, result
            else:
                output_logger.info(f'okx cancel order failed, orderid: {orderid}, result: {result}')
                return False,result

        except Exception as e:
            print(f'okx cancel order exception: {e}')
            self.error_handler(e)
            return None

    def error_handler(self,e):
        if isinstance(e,(OkxApiException,OkxParamsException,OkxApiException)):
            self.log_error(e)
        else:
            output_logger.info(f'Okx error: {e}')
    
    def log_error(self, e):
        output_logger.info(f'Okx status code: {e.status_code}. Error message: {e.message}')

    def load_all_symbol_info(self):
        """加载所有合约信息到缓存"""
        try:
            output_logger.info("OKX: 开始加载所有合约信息...")

            # 获取所有SWAP合约信息
            inst_info = self.session.publicdata.get_instruments(instType='SWAP')
            if inst_info['code'] == '0':
                loaded_count = 0

                for contract in inst_info['data']:
                    inst_id = contract['instId']  # OKX的合约ID
                    lot_size = contract.get('lotSz')
                    max_market_sz = contract.get('maxMktSz')
                    ct_val = contract.get('ctVal')

                    if lot_size and max_market_sz and ct_val:
                        # 转换为标准symbol格式
                        standard_symbol = self._convert_okx_to_standard(inst_id)
                        self._symbol_info_cache[standard_symbol] = (
                            float(lot_size),
                            float(max_market_sz),
                            float(ct_val)
                        )
                        loaded_count += 1

                self._symbol_info_loaded = True
                output_logger.info(f"OKX: 成功加载 {loaded_count} 个合约信息")
            else:
                output_logger.error(f"OKX: 获取合约信息失败，code: {inst_info['code']}")

        except Exception as e:
            self.error_handler(e)
            output_logger.error("OKX: 加载合约信息异常")

    def _convert_okx_to_standard(self, okx_inst_id):
        """将OKX的instId转换为标准symbol格式"""
        # OKX: BTC-USDT-SWAP -> BTCUSDT
        if '-USDT-SWAP' in okx_inst_id:
            return okx_inst_id.replace('-USDT-SWAP', 'USDT').replace('-', '')
        return okx_inst_id
        
    def _convert_to_okx_symbol(self, symbol):
        """将标准symbol转换为OKX格式"""
        # BTCUSDT -> BTC-USDT-SWAP
        if 'USDT' in symbol:
            base = symbol.replace('USDT', '')
            return f"{base}-USDT-SWAP"
        return symbol

    def coins_to_contracts(self, symbol, coin_amount):
        """将币的数量转换为OKX合约张数"""
        try:
            _, _, ct_val = self.get_symbol_info(symbol)
            if ct_val:
                return int(float(coin_amount) / ct_val)  # 向下取整到整张
            return None
        except Exception as e:
            self.error_handler(e)
            return None

    def contracts_to_coins(self, symbol, contract_amount):
        """将OKX合约张数转换为币的数量"""
        try:
            _, _, ct_val = self.get_symbol_info(symbol)
            if ct_val:
                return contract_amount * ct_val
            return None
        except Exception as e:
            self.error_handler(e)
            return None

    def get_order_details(self, order_id, symbol):
        """查询订单详情"""
        try:
            symbol_converted = self._symbol_convert(symbol)
            response = self.session.trade.get_order(instId=symbol_converted, ordId=order_id)
            if response.get('code') == '0' and response.get('data'):
                return response['data'][0]
            return None
        except Exception as e:
            self.error_handler(e)
            return None
        
    def close_all_positions(self) -> bool:
        logger.info(f"{self.name}: 正在尝试平掉所有仓位。")
        try:
            positions = self.get_positions()
            if not positions:
                logger.info(f"{self.name}: 没有需要平仓的仓位。")
                return True

            all_closed = True
            for pos in positions:
                inst_id = pos['instId']
                pos_side = pos['posSide']
                pos_qty_contracts = abs(float(pos.get('pos', '0')))
                symbol = self._symbol_from_okx(inst_id)
                
                symbol_info = self.get_symbol_info(symbol)
                max_mkt_qty_coins = symbol_info.get('max_qty', float('inf'))
                pos_qty_coins = self._contracts_to_coins(symbol, pos_qty_contracts)

                if pos_qty_coins is None:
                    logger.error(f"无法转换 {inst_id} 的持仓数量，跳过。")
                    all_closed = False
                    continue
                
                logger.info(f"正在处理平仓: {symbol} ({pos_side}), 数量: {pos_qty_coins} (币)")

                if pos_qty_coins <= max_mkt_qty_coins:
                    logger.info(f"仓位小于最大市价单限制，使用一键平仓接口。")
                    response = self.session.trade.close_positions(instId=inst_id, mgnMode='cross', posSide=pos_side)
                    if response.get('code') != '0':
                        error_msg = response.get('msg', '')
                        logger.error(f"平仓 {inst_id} 失败。交易所返回: {error_msg}")
                        all_closed = False
                    else:
                        logger.info(f"已成功为 {inst_id} 提交一键平仓请求。")
                else:
                    logger.warning(f"仓位 {pos_qty_coins} 超出市价单最大量 {max_mkt_qty_coins}，将进行拆单平仓。")
                    
                    remaining_qty = pos_qty_coins
                    close_side = 'sell' if pos_side == 'long' else 'buy'
                    
                    while remaining_qty > 0:
                        qty_to_close = min(remaining_qty, max_mkt_qty_coins)
                        logger.info(f"正在提交拆分平仓单: {close_side.value} {qty_to_close} {symbol}")
                        
                        success,response = self.place_order(category='linear',symbol=symbol,side=close_side,orderType='market',qty=qty_to_close,reduce_only='true')

                        if not success:
                            logger.error(f"拆分平仓单失败: {response}")
                            all_closed = False
                            break # 如果一笔失败，则停止后续拆单
                        
                        remaining_qty -= qty_to_close
                        time.sleep(0.2) # 避免过于频繁的请求
                    
                    if remaining_qty == 0:
                        logger.info(f"已成功拆单平掉 {symbol} 的全部仓位。")

            return all_closed
        except Exception as e:
            logger.exception(f"{self.name}: 平仓所有仓位时发生错误: {e}")
            return False

    def get_positions(self) -> list:
            """获取所有U本位永续合约的持仓信息"""
            try:
                response = self.session.account.get_positions(instType='SWAP')
                if response['code'] == '0' and response.get('data'):
                    # 过滤掉没有实际持仓的记录
                    active_positions = [pos for pos in response['data'] if float(pos.get('pos', '0')) != 0]
                    return active_positions
                else:
                    logger.error(f"{self.name}: 获取持仓失败: {response.get('msg')}")
                    return []
            except Exception as e:
                logger.exception(f"{self.name}: 获取持仓时发生错误: {e}")
                return []
            
    def cancel_all_orders(self) -> bool:
        """获取所有未成交订单，并分批次全部取消。"""
        logger.info(f"{self.name}: 正在尝试取消所有挂单。")
        try:
            response = self.session.trade.get_order_list(instType='SWAP')
            if response.get('code') != '0':
                logger.error(f"{self.name}: 获取未成交订单列表失败: {response.get('msg')}")
                return False

            orders_to_cancel = response.get('data', [])
            if not orders_to_cancel:
                logger.info(f"{self.name}: 没有需要取消的挂单。")
                return True

            cancel_requests = [{"instId": order['instId'], "ordId": order['ordId']} for order in orders_to_cancel]

            all_cancelled = True
            for i in range(0, len(cancel_requests), 20):
                batch = cancel_requests[i:i+20]
                logger.info(f"正在取消一批 {len(batch)} 个订单...")
                cancel_response = self.session.trade.cancel_multiple_orders(batch)
                
                if cancel_response.get('code') != '0':
                    logger.error(f"批量取消订单失败。交易所返回: {cancel_response.get('msg')}")
                    all_cancelled = False
                else:
                    logger.info("该批次订单已成功提交取消请求。")
            return all_cancelled
        except Exception as e:
            logger.exception(f"{self.name}: 取消所有挂单时发生错误: {e}")
            return False
        


class BinanceExchangeHTTP(BaseExchangeHTTP):
    def __init__(self,exchange_name, api_key, api_secret, isTest=False):
        super().__init__(exchange_name, api_key, api_secret, None, isTest)
        self.exchange_name = 'Binance'


    def connect(self):
        try:
            client = BinanceClient(self.api_key, self.api_secret, testnet=self.isTest)
            self.session = client
            return True
        except Exception as e:
            self.error_handler(e)
            return False
    
    def get_balance_info(self, force_update=False):
        """获取余额信息 - 带缓存机制
        Args:
            force_update: 是否强制更新，默认False使用缓存
        Returns:
            (total_equity, available_balance) 或 (None, None)
        """
        # 检查是否使用缓存
        if not force_update and self._balance_cache is not None:
            return self._balance_cache

        try:
            # 获取期货账户余额
            res = self.session.futures_account_balance()

            # 调试：打印响应结构
            # output_logger.info(f"Binance futures_account_balance response: {res}")

            if res and len(res) > 0:
                # 查找USDT余额
                usdt_balance = None
                for balance_info in res:
                    if balance_info['asset'] == 'USDT':
                        usdt_balance = balance_info
                        break

                if usdt_balance:
                    result = (float(usdt_balance['balance']), float(usdt_balance['availableBalance']))
                else:
                    # 如果没找到USDT，使用第一个资产
                    first_balance = res[0]
                    result = (float(first_balance['balance']), float(first_balance['availableBalance']))

                # 更新缓存
                self._balance_cache = result
                self._balance_cache_timestamp = time.time()

                return result
            else:
                output_logger.info("Binance: 未找到账户余额信息")
                return None, None

        except Exception as e:
            self.error_handler(e)
            return None, None

    def place_order(self, category,symbol,side:str,orderType,qty,reduceOnly='false'):
        """sample output for ret {
        "...
        "cumQty": "0",
        "cumQuote": "0", // 成交金额
        "executedQty": "0", // 成交量
        "orderId": 22542179, // 系统订单号
        "avgPrice": "0.00000",	// 平均成交价
        "origQty": "10", // 原始委托数量
        "price": "0", // 委托价格
        "side": "SELL", // 买卖方向
        "positionSide": "SHORT", // 持仓方向
        "status": "NEW", // 订单状态
        "stopPrice": "0", // 触发价，对`TRAILING_STOP_MARKET`无效
        ...
        "symbol": "BTCUSDT", // 交易对
        "timeInForce": "GTD", // 有效方法
        "origType": "TRAILING_STOP_MARKET",  // 触发前订单类型
        "activatePrice": "9020", // 跟踪止损激活价格, 仅`TRAILING_STOP_MARKET` 订单返回此字段
        "priceRate": "0.3",	// 跟踪止损回调比例, 仅`TRAILING_STOP_MARKET` 订单返回此字段
        "updateTime": 1566818724722, // 更新时间
        "workingType": "CONTRACT_PRICE", // 条件价格触发类型
        "priceProtect": false,            // 是否开启条件单触发保护
        "priceMatch": "NONE",              //盘口价格下单模式
        "selfTradePreventionMode": "NONE", //订单自成交保护模式
        "goodTillDate": 1693207680000      //订单TIF为GTD时的自动取消时间
        ...
    }"""

        try:
            # 生成自定义订单ID
            orderid = self.generate_orderid(symbol)

            ret = self.session.futures_create_order(
            symbol=symbol,
            side=side.upper(),
            type=orderType.upper(),
            quantity=qty,
            positionSide="BOTH",  #TODO check mode BOTH for One-way Mode ; LONG or SHORT for Hedge Mode
            newClientOrderId=orderid,  # 添加自定义订单ID
            reduceOnly=reduceOnly,
        )

            # 添加自定义订单ID到响应中
            if ret:
                ret['orderid'] = orderid

            return True, ret
        except Exception as e:
            self.error_handler(e)
            return False, None
    
    def place_limit_order(self, category,symbol,side:str,orderType,qty,price,reduceOnly='false'):
        if orderType != "limit":
            output_logger.error("尝试在binance place_limit_order 中下市价单")
        try:
            # 生成自定义订单ID
            orderid = self.generate_orderid(symbol)
            ret = self.session.futures_create_order(
            symbol=symbol,
            side=side.upper(),
            type=orderType.upper(),
            quantity=qty,
            price=price,
            positionSide="BOTH",  # check mode BOTH for One-way Mode ; LONG or SHORT for Hedge Mode
            newClientOrderId=orderid,  # 添加自定义订单ID
            timeInForce="GTC",
            reduceOnly=reduceOnly,
        )

            # 添加自定义订单ID到响应中
            if ret:
                ret['orderid'] = orderid

            return True, ret
        except Exception as e:
            self.error_handler(e)
            return False, None
        
    def load_all_symbol_info(self):
        """加载所有合约信息到缓存"""
        try:
            output_logger.info("Binance: 开始加载所有合约信息...")

            # 获取所有期货交易规则信息
            exchange_info = self.session.futures_exchange_info()
            if exchange_info and 'symbols' in exchange_info:
                loaded_count = 0

                for symbol_info in exchange_info['symbols']:
                    symbol = symbol_info['symbol']

                    # 查找LOT_SIZE过滤器
                    for filter_info in symbol_info['filters']:
                        if filter_info['filterType'] == 'LOT_SIZE':
                            min_qty = filter_info.get('minQty')
                            max_qty = filter_info.get('maxQty')

                            if min_qty and max_qty:
                                self._symbol_info_cache[symbol] = (
                                    float(min_qty),
                                    float(max_qty),
                                    1.0  # Binance直接使用币的数量，合约面值为1
                                )
                                loaded_count += 1
                            break

                self._symbol_info_loaded = True
                output_logger.info(f"Binance: 成功加载 {loaded_count} 个合约信息")
            else:
                output_logger.error("Binance: 获取交易规则信息失败")

        except Exception as e:
            self.error_handler(e)
            output_logger.error("Binance: 加载合约信息异常")

    def error_handler(self,e):
        if isinstance(e, BinanceAPIException):
            self.log_error(e)
        elif isinstance(e, BinanceRequestException):
            output_logger.info(f'Binance Request Error: {e}')
        else:
            output_logger.info(f'Binance Unknown Error: {e}')
            
    def get_orderbook(self, symbol):
        """获取订单簿数据"""
        try:
            result = self.session.futures_order_book(symbol=symbol, limit=20)
            asks = result['asks']  # 卖单 [[price, quantity], ...]
            bids = result['bids']  # 买单 [[price, quantity], ...]
            return asks, bids
        except Exception as e:
            self.error_handler(e)
            return None, None

    def log_error(self,e):
        output_logger.info(f'Binance Error Code: {e.code}. Error Message: {e.message}.')

    def get_order_detail(self, orderid: str):
        """获取订单详情 - Binance"""
        try:
            print(f'binance get_order_detail orderid: {orderid}')
            symbol, timestamp = self.strip_orderid(orderid)

            # 使用 Binance API 查询订单详情
            response = self.session.futures_get_order(symbol=symbol, origClientOrderId=orderid)
            print(f'binance get_order_detail response: {response}')

            if response:
                detail = {
                    "symbol": symbol,
                    "avg_price": float(response.get('avgPrice', '0')),
                    "exec_qty": float(response.get('executedQty', '0')),
                    "exec_fee": abs(float(response.get('commission', '0'))),
                    "created_time": int(response.get('time', '0')),
                    "our_time": int(timestamp),
                    "exchange": self.exchange_name,
                    "orderid": orderid,
                    "status": response.get('status', 'UNKNOWN'),
                }
                print(f'binance get_order_detail detail: {detail}')
                return detail
            else:
                output_logger.info(f'Binance get_order_detail failed, orderid: {orderid}')
                return None
        
        except Exception as e:
            self.error_handler(e)
            return None
        
    def cancel_order(self,orderid):
        try:
            print(f'binance get_order_detail orderid: {orderid}')
            symbol, timestamp = self.strip_orderid(orderid)

            #timestamp需要是unix time
            response = self.session.futures_cancel_order(symbol=symbol, origClientOrderId=orderid,timestamp=time.time()*1000)
            print(f'binance cancel_order response: {response}')

            if response:

                return True,response
            else:
                output_logger.info(f'Binance cancel_order failed, orderid: {orderid}')
                return False,response
        
        except Exception as e:
            self.error_handler(e)
            return False, None
        
    def get_order_details(self, order_id, symbol):
        """查询订单详情"""
        try:
            response = self.session.futures_get_order(symbol=symbol, orderId=order_id)
            return response
        except Exception as e:
            self.error_handler(e)
            return None
        
    def get_positions(self, symbol=None):
        """获取当前U本位合约持仓。"""
        try:
            # 返回 有仓位的和 未完全成交订单
            positions = self.session.futures_position_information(symbol=symbol)
            # 过滤掉没有实际持仓的记录
            active_positions = [p for p in positions if float(p.get('positionAmt', '0')) != 0]
            # 实际持仓数量 > 0 就是我们要的 持仓的
            return active_positions
        except Exception as e:
            self.error_handler(e)
            return []

    def get_open_orders(self, symbol=None):
        """获取当前U本位合约挂单。"""
        try:
            open_orders = self.session.futures_get_open_orders(symbol=symbol)
            return open_orders
        except Exception as e:
            self.error_handler(e)
            return []
        
    def cancel_all_orders(self):
        """取消所有U本位合约的挂单。"""
        output_logger.info("Binance: 正在尝试取消所有挂单...")
        try:
            open_orders = self.get_open_orders()
            if not open_orders:
                output_logger.info("Binance: 没有需要取消的挂单。")
                return True
            
            symbols_with_orders = list(set(order['symbol'] for order in open_orders))
            
            all_cancelled = True
            for symbol in symbols_with_orders:
                output_logger.info(f"Binance: 正在取消 {symbol} 的所有挂单...")
                try:
                    # 该接口会取消一个symbol下的所有挂单
                    self.session.futures_cancel_all_open_orders(symbol=symbol)
                except Exception as e:
                    output_logger.error(f"Binance: 取消 {symbol} 挂单失败。")
                    self.error_handler(e)
                    all_cancelled = False
            
            return all_cancelled
        except Exception as e:
            self.error_handler(e)
            return False
        
    def close_all_positions(self):
        """(优化) 使用批量下单市价平掉所有U本位合约的持仓。"""
        output_logger.info("Binance: 正在尝试平掉所有仓位...")
        try:
            positions = self.get_positions()
            if not positions:
                output_logger.info("Binance: 没有需要平仓的仓位。")
                return True

            orders_to_place = []
            for pos in positions:
                quantity = abs(float(pos['positionAmt']))
                if quantity > 0:
                    order = {
                        "symbol": pos['symbol'],
                        "side": 'SELL' if float(pos['positionAmt']) > 0 else 'BUY',
                        "type": 'MARKET',
                        "quantity": str(quantity),
                        "reduceOnly": "true"
                    }
                    orders_to_place.append(order)
            
            if not orders_to_place:
                output_logger.info("Binance: 没有可平仓的仓位。")
                return True
                
            output_logger.info(f"Binance: 准备批量平掉 {len(orders_to_place)} 个仓位...")
            
            all_closed_successfully = True
            # 批量下单接口每次最多接受5个订单
            for i in range(0, len(orders_to_place), 5):
                batch = orders_to_place[i:i+5]
                try:
                    responses = self.session.futures_place_batch_order(batchOrders=batch)
                    # 检查返回结果中是否有失败的订单
                    for resp in responses:
                        if "code" in resp:
                            output_logger.error(f"Binance: 批量平仓失败, 错误: {resp.get('msg')}")
                            all_closed_successfully = False
                except Exception as e:
                    output_logger.error("Binance: 提交批量平仓请求时发生异常。")
                    self.error_handler(e)
                    all_closed_successfully = False
                    break 

            return all_closed_successfully
        except Exception as e:
            self.error_handler(e)
            return False


class BaseExchangeWs(ABC):
    def __init__(self, exchange_name,api_key,api_secret,passphrase,isTest):
        self.exchange_name = exchange_name
        self.session = None
        self.api_key = api_key
        self.passphrase = passphrase
        api_secret = api_secret
        self.isTest = isTest

    @abstractmethod
    def connect(self):
        pass


    @abstractmethod
    def place_order(self):
        pass
    
    @abstractmethod
    def log_error(self):
        pass

    @abstractmethod
    def get_balance_info(self):
        pass
class BybitExchangeWs(BaseExchangeWs):
    def __init__(self,exchange_name,api_key,api_secret,isTest):
        super().__init__(exchange_name, api_key, api_secret, None, isTest)  #ignore passphrase
        self.ws_trading = None
        self.api_key = api_key
        self.api_secret = api_secret
        self.isTest = isTest

    def connect(self):
        try:
            ws_trading = WebSocketTrading(
        testnet=True,
        api_key=self.api_key,
        api_secret=self.api_secret,
    )
            self.ws_trading = ws_trading
            return True
        except InvalidRequestError as e:
            self.log_error(e)

    def handle_place_order_message(self,message):
        # Receive the orderId
        output_logger.info(message)
        print(message)

    def place_order(self,category,symbol,side,orderType,price,qty):
        """orderType Limit / Market
            side Buy / Sell """
        try:
            self.ws_trading.place_order(
            self.handle_place_order_message,
            category=category,
            symbol=symbol,
            side=side,
            orderType=orderType,
            price=price,
            qty=qty)

        except InvalidRequestError as e:
            self.log_error(e)
        
        except FailedRequestError as e:
            self.log_error(e)

        except Exception as e:
            print(e)
            
    def log_error(self,e):
        output_logger.info(f'错误代码: {e.status_code}, 错误信息: {e.message}')


    def get_balance_info(self):
        pass
    

