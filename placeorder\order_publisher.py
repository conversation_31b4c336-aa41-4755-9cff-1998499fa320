# filename: order_publisher.py

import json
import time
import logging
from realtime_data_manager import realtime_data_manager

logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO)

def generate_orderid(symbol:str):
    """通过交易对生成orderid"""
    return f"{symbol}G666G{int(time.time() * 1000)}"

class WsSignal:
    def __init__(self, instId:str, sz:float, side:str, tdMode:str, ordType:str, 
                  clorid:str = None, px:float = 0.0, 
                 reduceOnly:str = 'false', action:str = 'open', timeInForce:str = 'gtc'):
        # tdmode 指 保证金模式，填 isolated 或者 cross
        self.instId = instId
        self.quantity = sz
        self.price = px
        self.trade_mode = tdMode
        self.order_type = ordType
        self.reduce_only = reduceOnly
        self.side = side
        self.action = action
        self.timeInforce = timeInForce
        
        # 使用您指定的 clorid 变量，并确保在未提供时生成
        if clorid is None:
            self.clorid = generate_orderid(self.instId)
        else:
            self.clorid = clorid
        
        self.safe_check()

    def safe_check(self):
        if self.order_type == 'market' and self.price != 0.0:
            logger.error('错误： 创建了一个带价格的ws交易信号')
        if self.action == 'close' and self.reduce_only != 'true':
            logger.error('错误 平仓没有使用 参数 reduceOnly = \'true\'')

    def to_okx_dict(self) -> dict:
        """将信号转换为OKX WebSocket API要求的格式
            okx 双向, okx 没有gtc 等选项
        """
        inst_id = self.instId
        if 'USDT' in inst_id and '-SWAP' not in inst_id:
            inst_id = inst_id.replace('USDT', '-USDT-SWAP')
        
        args = {
            "instId": inst_id,
            "tdMode": self.trade_mode,
            "clOrdId": self.clorid, # API要求key为clOrdId，值为您指定的clorid
            "side": self.side,
            "ordType": self.order_type,
            "sz": str(self.quantity),
            "reduceOnly": self.reduce_only,
        }
        action = self.action
        side = self.side
        if action == 'open' and side == 'buy':
            posSide = 'long'
        elif action == 'open' and side == 'sell':
            posSide = 'short'
        elif action == 'close' and side == 'sell':
            posSide = 'long'
        elif action =='close' and side == 'buy':
            posSide = 'short'

        args['posSide'] = posSide

        if self.order_type == 'limit':
            args['px'] = str(self.price)
        
        return args
    
    def to_bybit_dict(self) -> dict:
        """将内部信号转换为 Bybit API (v5) 所需的格式"""
        # 字段名和值的转换
        order = {
            "symbol": self.instId.replace("-", ""), # Bybit使用 "BTCUSDT" 而非 "BTC-USDT"
            "side": self.side.capitalize(), # "buy" -> "Buy"
            "orderType": self.order_type.capitalize(), # "market" -> "Market"
            "qty": str(self.quantity),
            "orderLinkId": self.clorid, # Bybit使用 orderLinkId 作为客户端ID
            "reduceOnly": self.reduce_only,
            "category": 'linear',
        }

        if self.order_type == 'limit':
            order['price'] = str(self.price)

        timeInforce = self.timeInforce.upper()
        order['timeInForce'] = timeInforce

        return order

    def to_bitget_dict(self) -> dict:
        """将内部信号转换为 Bitget API v2 所需的格式。"""

        timeInforce = self.timeInforce
        params = {
            "orderType": self.order_type,
            "side": self.side,
            "size": str(self.quantity),
            "force": timeInforce,
            "marginCoin": "USDT",
            "clientOid": self.clorid,
        }
        if self.trade_mode == 'cross':
            params['marginMode'] = 'crossed'
        elif self.trade_mode == 'isolated':
            params['marginMode'] = 'isolated'

        # # 处理双向持仓与单向持仓的逻辑
        # if self.position_side in ['long', 'short']: # 表示是双向持仓模式
        #     params["tradeSide"] = "open" if self.action == 'open' else "close"
        # else: # 假设是单向持仓模式
        #     # Bitget的reduceOnly仅在单向持仓模式下适用

        # bitget 应该是单向模式
        if self.reduce_only == 'true' and self.action == 'close':
            params["reduceOnly"] = "YES"

        if self.order_type == 'limit':
            params['price'] = str(self.price)

        # 'args' 列表所需的最终负载结构
        return {
            "instType": "USDT-FUTURES",
            "channel": "place-order",
            "instId": self.instId.replace("-", ""),
            "id": self.clorid, # 使用 clorid 作为唯一请求ID
            "params": params
        }
    
    def to_binance_dict(self) -> dict:
        """将内部信号转换为 Binance API (U本位合约) 所需的格式。"""
        params = {
            "symbol": self.instId.replace("-", ""),
            "side": self.side.upper(), # BUY 或 SELL
            "type": self.order_type.upper(), # LIMIT 或 MARKET
            "quantity": self.quantity,
            "newClientOrderId": self.clorid,
        }
        
        params['timeInForce'] = self.timeInforce.upper()

        # 根据持仓模式设置 positionSide
        if self.position_side in ['long', 'short']:
            params['positionSide'] = self.position_side.upper()
        else:
            params['positionSide'] = 'BOTH' # 单向持仓

        if self.order_type.upper() == 'LIMIT':
            params['price'] = self.price
        
        # reduceOnly 仅在单向持仓下有效
        if self.position_side not in ['long', 'short'] and self.reduce_only == 'true':
            params['reduceOnly'] = 'true'
       
        return params

    
def okx_place_order(signal:WsSignal):

    try:
        # 步骤 1: 将信号对象转换为 OKX 专用的字典
        okx_order_dict = signal.to_okx_dict()

        # 步骤 2: 序列化为 JSON 字符串
        message_json = json.dumps(okx_order_dict)
        channel_name = "trading_orders:okx"
        # 步骤 3: 发布到指定的 Redis 频道
        realtime_data_manager.redis_client.publish(channel_name, message_json)

        print(f"✅ 交易指令已发送至频道 '{channel_name}': {message_json}")
        return True

    except Exception as e:
        print(f"❌ 发单失败: 在处理或发送到Redis时发生错误: {e}")
        return False
    
def bybit_place_order(signal:WsSignal):

    try:
        # 步骤 1: 将信号对象转换为 bybit 专用的字典
        bybit_order_dict = signal.to_bybit_dict()

        # 步骤 2: 序列化为 JSON 字符串
        message_json = json.dumps(bybit_order_dict)
        channel_name = "trading_orders:bybit"
        # 步骤 3: 发布到指定的 Redis 频道
        realtime_data_manager.redis_client.publish(channel_name, message_json)

        print(f"✅ 交易指令已发送至频道 '{channel_name}': {message_json}")
        return True

    except Exception as e:
        print(f"❌ 发单失败: 在处理或发送到Redis时发生错误: {e}")
        return False
    
def bitget_place_order(signal:WsSignal):

    try:
        # 步骤 1: 将信号对象转换为 OKX 专用的字典
        bitget_order_dict = signal.to_bitget_dict()

        # 步骤 2: 序列化为 JSON 字符串
        message_json = json.dumps(bitget_order_dict)
        channel_name = "trading_orders:bitget"
        # 步骤 3: 发布到指定的 Redis 频道
        realtime_data_manager.redis_client.publish(channel_name, message_json)

        print(f"✅ 交易指令已发送至频道 '{channel_name}': {message_json}")
        return True

    except Exception as e:
        print(f"❌ 发单失败: 在处理或发送到Redis时发生错误: {e}")
        return False
    
def binance_place_order(signal:WsSignal):

    try:
        # 步骤 1: 将信号对象转换为 OKX 专用的字典
        binance_order_dict = signal.to_binance_dict()

        # 步骤 2: 序列化为 JSON 字符串
        message_json = json.dumps(binance_order_dict)
        channel_name = "trading_orders:okx"
        # 步骤 3: 发布到指定的 Redis 频道
        realtime_data_manager.redis_client.publish(channel_name, message_json)

        print(f"✅ 交易指令已发送至频道 '{channel_name}': {message_json}")
        return True

    except Exception as e:
        print(f"❌ 发单失败: 在处理或发送到Redis时发生错误: {e}")
        return False
