#!/usr/bin/env python3
# filename: bybit_websocket_trading.py

import asyncio
import json
import logging
import time
import hmac
import hashlib
from typing import Dict, Any

import websockets
from websockets.legacy.client import WebSocketClientProtocol
import redis.asyncio as redis

try:
    from api_keys import EXCHANGE_API_KEYS
except ImportError:
    print("错误：找不到 api_keys.py 文件。")
    exit()

# --- 全局配置 ---
LOG_FORMAT = "%(asctime)s | %(levelname)-7s | %(module)s:%(lineno)-4d | %(message)s"
logging.basicConfig(level=logging.INFO, format=LOG_FORMAT, datefmt="%H:%M:%S")
logger = logging.getLogger("BybitWsOrderService")

# 使用 Bybit V5 的交易专用 WebSocket 地址 (Testnet)
WS_URL = "wss://stream.bybit.com/v5/trade"
REDIS_URL = "redis://localhost:6379/0"
REDIS_ORDER_CHANNEL = "trading_orders:bybit" # 监听Bybit专用频道

# --- WebSocket 管理器 ---
class BybitWebSocketManager:
    def __init__(self, api_key: str, secret_key: str, url: str):
        self._api_key, self._secret_key, self._url = api_key, secret_key, url
        self._ws: WebSocketClientProtocol = None
        self._is_connected = False
        self._listener_task = None

    async def connect(self):
        while True:
            try:
                logger.info(f"正在尝试连接到 Bybit: {self._url}...")
                self._ws = await websockets.connect(self._url, ping_interval=18, ping_timeout=10)
                await self._auth()
                self._is_connected = True
                logger.info("Bybit WebSocket 已连接并认证成功。")
                if self._listener_task: self._listener_task.cancel()
                self._listener_task = asyncio.create_task(self._listen())
                await self._listener_task
            except (websockets.exceptions.ConnectionClosedError, ConnectionError) as e:
                logger.warning(f"Bybit WebSocket 连接断开: {e}. 5秒后将尝试重连...")
            except Exception as e:
                logger.error(f"Bybit WebSocket 连接时发生未知错误: {e}", exc_info=True)
            finally:
                self._is_connected = False
                await asyncio.sleep(5)

    async def _auth(self):
        expires = int((time.time() + 5) * 1000)
        signature_payload = f"GET/realtime{expires}".encode('utf-8')
        secret_key_bytes = self._secret_key.encode('utf-8')
        signature = hmac.new(secret_key_bytes, signature_payload, hashlib.sha256).hexdigest()
        
        auth_request = {"op": "auth", "args": [self._api_key, expires, signature]}
        await self._ws.send(json.dumps(auth_request))
        logger.info("Bybit 认证请求已发送。")

        # --- 这里是修正点 ---
        # 使用 self._ws 而不是 ws
        auth_response = json.loads(await asyncio.wait_for(self._ws.recv(), timeout=10))
        if not auth_response.get("success"):
            raise ConnectionError(f"Bybit认证失败: {auth_response.get('retMsg', '未知错误')}")
        logger.debug(f"Bybit 认证成功回执: {auth_response}")

    async def _listen(self):
        logger.info("Bybit WebSocket 消息监听任务已启动。")
        try:
            async for message in self._ws:
                data = json.loads(message)
                if data.get("op") == "pong":
                    logger.debug(f"收到 Bybit pong 响应: {data}")
                    continue
                
                if data.get("op") in ["order.create", "order.amend", "order.cancel"]:
                    self._handle_order_response(data)
                else:
                    logger.debug(f"收到未处理的 Bybit 消息: {data}")
        except websockets.exceptions.ConnectionClosed as e:
            logger.warning(f"Bybit 监听任务因连接关闭而终止: {e}")
            raise

    def _handle_order_response(self, response: Dict[str, Any]):
        req_id = response.get("reqId", "N/A")
        if response.get("retCode") == 0:
            order_id = response.get("data", {}).get("orderId", "N/A")
            order_link_id = response.get("data", {}).get("orderLinkId", "N/A")
            logger.info(f"✅ [Bybit 下单成功] ReqID: {req_id}, OrderID: {order_id}, ClientID: {order_link_id}")
        else:
            code = response.get("retCode")
            msg = response.get("retMsg", "未知错误")
            logger.error(f"❌ [Bybit 下单失败] ReqID: {req_id}, 错误码: {code}, 原因: {msg}")

    async def place_order(self, order_args: Dict[str, Any]):
        req_id = order_args.get("orderLinkId", f"fallback_{int(time.time()*1000)}")
        payload = {
            "reqId": req_id,
            "header": {
                "X-BAPI-TIMESTAMP": str(int(time.time() * 1000)),
                "X-BAPI-RECV-WINDOW": "5000",
            },
            "op": "order.create",
            "args": [order_args]
        }
        if not self._is_connected or not self._ws:
            logger.error(f"无法发送Bybit订单 (ReqID: {req_id})：WebSocket 未连接。")
            return
        try:
            logger.info(f"-> 准备发送Bybit订单 (ReqID: {req_id}): {json.dumps(payload)}")
            await self._ws.send(json.dumps(payload))
        except Exception as e:
            logger.error(f"发送Bybit订单消息时出错 (ReqID: {req_id}): {e}", exc_info=True)

# --- Redis 监听器 ---
async def listen_for_redis_orders(ws_manager: BybitWebSocketManager):
    while True:
        try:
            logger.info(f"正在连接到 Redis ({REDIS_URL})...")
            r = await redis.from_url(REDIS_URL, decode_responses=True)
            pubsub = r.pubsub()
            await pubsub.subscribe(REDIS_ORDER_CHANNEL)
            logger.info(f"✅ 已成功订阅 Redis 频道: '{REDIS_ORDER_CHANNEL}'")
            async for message in pubsub.listen():
                if message['type'] != 'message': continue
                logger.info(f"收到来自 Redis 的原始消息: {message['data']}")
                try:
                    order_details_dict = json.loads(message['data'])
                    await ws_manager.place_order(order_details_dict)
                except json.JSONDecodeError:
                    logger.error(f"无法解析来自 Redis 的 JSON 消息: {message['data']}")
                except Exception as e:
                    logger.error(f"处理 Redis 消息时发生错误: {e}", exc_info=True)
        except redis.exceptions.ConnectionError as e:
            logger.error(f"Redis 连接失败: {e}. 10秒后重试...")
            await asyncio.sleep(10)

# --- 主程序入口 ---
async def main():
    logger.info("启动 Bybit WebSocket 下单服务...")
    credentials = EXCHANGE_API_KEYS.get("bybit")
    if not credentials or "api_key" not in credentials:
        logger.error("错误：请在 api_keys.py 文件中正确配置您的 Bybit API 信息。")
        return
    ws_manager = BybitWebSocketManager(
        api_key=credentials["api_key"], secret_key=credentials["secret"], url=WS_URL
    )
    try:
        await asyncio.gather(ws_manager.connect(), listen_for_redis_orders(ws_manager))
    finally:
        logger.info("服务正在关闭...")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("收到用户中断信号 (Ctrl+C)，程序退出。")