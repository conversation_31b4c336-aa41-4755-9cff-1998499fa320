"""
Bitget 合约 (USDT 永续 UMCBL) 最优买卖价订阅示例
------------------------------------------------
WebSocket Endpoint:
    wss://ws.bitget.com/v2/ws/public     (主站 USDT 永续/交割合约等 'mix' 分类)

订阅请求格式 (每个 arg 一个对象):
  {
    "op": "subscribe",
    "args": [
      {"instType":"USDT-FUTURES","channel":"ticker","instId":"BTCUSDT"},
      {"instType":"USDT-FUTURES","channel":"ticker","instId":"ETHUSDT"}
    ]
  }

返回数据示例 (精简):
  {
    "arg":{"instType":"USDT-FUTURES","channel":"ticker","instId":"BTCUSDT"},
    "data":[
      {
        "instId":"BTCUSDT",
        "last":"27123.5",
        "bidPr":"27123.4",
        "askPr":"27123.6",
        "bidSz":"5.123",
        "askSz":"4.011",
        "high24h":"28000",
        "low24h":"26000",
        "priceChangePercent":"-2.1",
        "ts":"1696400000123"
      }
    ]
  }

心跳:
  - Bitget 会不定期发送 {"op":"ping"} 或服务端 ping (部分实现为 {"ping":<ts>} 旧格式)
  - 客户端需在间隔内保持活跃，可主动发送 {"op":"ping"}；收到 ping 时需回复 {"op":"pong"}

关注字段:
  bestBid / bestAsk / bestBidSize / bestAskSize / ts (毫秒)

本示例:
  - 多 symbol 订阅
  - 心跳协程：每 HEARTBEAT_INTERVAL 秒发送一次 {"op":"ping"}
  - 接收循环中若发现服务端 ping，立即回复 pong
  - watchdog 若超时无消息 => 主动断开触发重连
  - 指数退避重连: 2,4,8,... 秒，最大 60 秒，并加轻微抖动

可扩展:
  - 支持其它 instType: DMCBL (交割合约-USD?), CMFUTURES 等，参见官方文档
  - 写入数据库 / 消息队列
  - 增加延迟统计直方图 (Prometheus)
  - 鉴权订阅私有频道 (订单/持仓) —— 需签名 (API Key/Secret + timestamp + sign)
"""

import asyncio
import json
import logging
import signal
import time
from dataclasses import dataclass
from typing import List, Optional, Callable, Awaitable, Dict, Any

import websockets
from websockets import WebSocketClientProtocol

# ---------------- 配置常量 ---------------- #

BITGET_MIX_WS = "wss://ws.bitget.com/v2/ws/public"
# 测试网 (若需要):
# BITGET_MIX_WS = "wss://ws.bitgetapi.com/mix/v1/stream"
#
# UMCBL = USDT 永续 (UMC = USDT-M, BL = Perp)
DEFAULT_INST_TYPE = "USDT-FUTURES"

HEARTBEAT_INTERVAL = 15      # 主动 ping 周期 (秒)
MESSAGE_TIMEOUT = 45         # 超过该秒数未收到任何消息 => 触发重连
RECONNECT_BASE_DELAY = 2
RECONNECT_MAX_DELAY = 60


# ---------------- 数据结构 ---------------- #

@dataclass
class BestQuote:
    inst_type: str
    inst_id: str
    bid_px: float
    bid_sz: float
    ask_px: float
    ask_sz: float
    ts: int           # 服务端毫秒时间戳
    latency_ms: float # 简单延迟估算


# ---------------- 客户端实现 ---------------- #

class BitgetTickerClient:
    """
    Bitget 'ticker' 频道客户端，聚焦最优买卖价。
    """
    def __init__(
        self,
        symbols: List[str],
        inst_type: str = DEFAULT_INST_TYPE,
        ws_url: str = BITGET_MIX_WS,
        on_quote: Optional[Callable[[BestQuote], Awaitable[None]]] = None
    ):
        """
        :param symbols: 例如 ["BTCUSDT","ETHUSDT"]
        :param inst_type: UMCBL (USDT 永续)，如需交割合约可调整
        :param ws_url: WebSocket endpoint
        :param on_quote: 异步回调
        """
        self.symbols = [self._normalize_symbol(s) for s in symbols]
        self.inst_type = inst_type.upper()
        self.ws_url = ws_url
        self.on_quote = on_quote

        self._ws: Optional[WebSocketClientProtocol] = None
        self._stop_evt = asyncio.Event()
        self._last_msg_time: float = 0.0
        self._reconnect_attempt: int = 0
        self._tasks: list[asyncio.Task] = []

    @staticmethod
    def _normalize_symbol(sym: str) -> str:
        return sym.replace("-", "").replace("_", "").upper().strip()

    # -------- Public methods -------- #
    async def start(self):
        logging.info("启动 BitgetTickerClient 订阅: instType=%s symbols=%s",
                     self.inst_type, self.symbols)
        while not self._stop_evt.is_set():
            try:
                await self._connect_and_run()
            except asyncio.CancelledError:
                raise
            except Exception as e:
                logging.exception("主循环异常: %s", e)

            if self._stop_evt.is_set():
                break

            # 指数退避
            self._reconnect_attempt += 1
            base = RECONNECT_BASE_DELAY * (2 ** (self._reconnect_attempt - 1))
            delay = min(base, RECONNECT_MAX_DELAY)
            jitter = delay * 0.2
            wait_time = delay + (jitter * (0.5 - (time.time() % 1)))
            logging.warning("将在 %.2f 秒后重连 (尝试 #%d)", wait_time, self._reconnect_attempt)
            try:
                await asyncio.wait_for(self._stop_evt.wait(), timeout=wait_time)
            except asyncio.TimeoutError:
                pass

        logging.info("客户端停止。")

    async def stop(self):
        logging.info("停止中 ...")
        self._stop_evt.set()
        await self._close_ws()
        for t in self._tasks:
            if not t.done():
                t.cancel()
        await asyncio.gather(*self._tasks, return_exceptions=True)

    # -------- Internal workflow -------- #
    async def _connect_and_run(self):
        logging.info("连接 %s", self.ws_url)
        async with websockets.connect(
            self.ws_url,
            ping_interval=None,  # 手动心跳
            ping_timeout=None,
            max_queue=1024,
            close_timeout=5,
        ) as ws:
            self._ws = ws
            self._reconnect_attempt = 0
            self._last_msg_time = time.time()

            await self._subscribe()

            recv_task = asyncio.create_task(self._receiver_loop(), name="receiver")
            heartbeat_task = asyncio.create_task(self._heartbeat_loop(), name="heartbeat")
            watchdog_task = asyncio.create_task(self._watchdog_loop(), name="watchdog")
            self._tasks = [recv_task, heartbeat_task, watchdog_task]

            done, pending = await asyncio.wait(self._tasks, return_when=asyncio.FIRST_EXCEPTION)
            for d in done:
                if exc := d.exception():
                    raise exc

        self._ws = None

    async def _close_ws(self):
        if self._ws:
            try:
                await self._ws.close()
            except Exception:
                pass

    async def _send_json(self, obj: Dict[str, Any]):
        if not self._ws:
            return
        txt = json.dumps(obj, separators=(",", ":"))
        await self._ws.send(txt)
        logging.debug("发送: %s", txt)

    async def _subscribe(self):
        args = [
            {
                "instType": self.inst_type,
                "channel": "ticker",
                "instId": sym
            }
            for sym in self.symbols
        ]
        req = {"op": "subscribe", "args": args}
        await self._send_json(req)
        logging.info("已发送订阅: %s", args)

    # -------- Heartbeat / Watchdog -------- #
    async def _heartbeat_loop(self):
        while not self._stop_evt.is_set():
            await asyncio.sleep(HEARTBEAT_INTERVAL)
            if not self._ws:
                continue
            try:
                await self._send_json({"op": "ping"})
                logging.debug("发送 ping")
            except Exception as e:
                logging.warning("心跳异常: %s", e)
                break

    async def _watchdog_loop(self):
        while not self._stop_evt.is_set():
            await asyncio.sleep(5)
            idle = time.time() - self._last_msg_time
            if idle > MESSAGE_TIMEOUT:
                logging.warning("超过 %.1fs 未收到消息(阈值=%s)，断开重连", idle, MESSAGE_TIMEOUT)
                await self._close_ws()
                break

    # -------- Receiver -------- #
    async def _receiver_loop(self):
        assert self._ws is not None
        ws = self._ws
        async for raw in ws:
            self._last_msg_time = time.time()

            # 原始 ping 处理（某些实现可能直接是字符串或简单 JSON）
            if isinstance(raw, str):
                # 尝试 JSON
                try:
                    msg = json.loads(raw)
                except json.JSONDecodeError:
                    logging.debug("非 JSON 文本: %s", raw)
                    continue
            else:
                # bytes 情况 (Bitget 一般是文本)
                try:
                    msg = json.loads(raw.decode())
                except Exception:
                    logging.debug("二进制未知消息")
                    continue

            # 统一处理消息
            if self._handle_control_message(msg):
                continue

            # 数据消息 (含 arg / data)
            if "arg" in msg and "data" in msg:
                arg = msg["arg"]
                channel = arg.get("channel")
                if channel == "ticker":
                    for d in msg.get("data", []):
                        try:
                            ts = int(d.get("ts") or 0)
                            now_ms = int(time.time() * 1000)
                            latency = (now_ms - ts) if ts > 0 else -1
                            quote = BestQuote(
                                inst_type=arg.get("instType", self.inst_type),
                                inst_id=d.get("instId", arg.get("instId")),
                                bid_px=float(d.get("bidPr") or 0.0),
                                bid_sz=float(d.get("bidSz") or 0.0),
                                ask_px=float(d.get("askPr") or 0.0),
                                ask_sz=float(d.get("askSz") or 0.0),
                                ts=ts,
                                latency_ms=latency
                            )
                            if self.on_quote:
                                await self.on_quote(quote)
                        except Exception as e:
                            logging.exception("处理 ticker 数据异常: %s | raw=%s", e, d)
                else:
                    logging.debug("忽略频道: %s", channel)
            else:
                logging.debug("未知结构: %s", msg)

    def _handle_control_message(self, msg: Dict[str, Any]) -> bool:
        """
        返回 True 表示该消息是控制(不再继续作为数据处理)。
        """
        # 订阅回执
        if msg.get("op") in ("subscribe", "unsubscribe"):
            if msg.get("code") in (0, "0", None) and msg.get("msg") in (None, "success"):
                logging.info("%s 成功: %s", msg.get("op"), msg.get("args"))
            else:
                logging.error("%s 失败: %s", msg.get("op"), msg)
            return True

        # 心跳: 服务端可能发 {"op":"ping"} 或 {"ping": <ts>}
        if msg.get("op") == "ping":
            # 回复 pong
            asyncio.create_task(self._send_json({"op": "pong"}))
            logging.debug("收到 ping -> 已回 pong")
            return True
        if "ping" in msg:
            asyncio.create_task(self._send_json({"pong": msg["ping"]}))
            logging.debug("收到 ping(ts) -> 已回 pong(ts)")
            return True

        # 服务端对我们 ping 的响应
          # 可能 {"op":"pong"} 或 {"pong":<ts>}
        if msg.get("op") == "pong" or "pong" in msg:
            logging.debug("收到 pong 响应: %s", msg)
            return True

        # 错误码
        if msg.get("code") not in (None, "0", 0):
            logging.error("错误消息: %s", msg)
            return True

        return False


# ---------------- 示例回调与运行 ---------------- #

async def example_on_quote(q: BestQuote):
    print(
        f"[{time.strftime('%X')}] {q.inst_id} "
        f"Bid {q.bid_px:.2f} ({q.bid_sz:.4f}) | "
        f"Ask {q.ask_px:.2f} ({q.ask_sz:.4f}) | "
        f"ts={q.ts} latency={q.latency_ms}ms"
    )

async def main():
    symbols = ["OPUSDT","LDOUSDT","NEARUSDT","CRVUSDT","ZETAUSDT","LRCUSDT","ARBUSDT","LINKUSDT","BCHUSDT","MASKUSDT","ALGOUSDT","YFIUSDT","XLMUSDT","CROUSDT","ETHWUSDT","ETHUSDT","ETCUSDT","USDCUSDT","THETAUSDT","CHZUSDT","AXSUSDT","SANDUSDT","ADAUSDT","FLOWUSDT","AI16ZUSDT","INJUSDT","SUSHIUSDT","1INCHUSDT","MAJORUSDT","MYRIAUSDT","CFXUSDT","ZILUSDT","GMTUSDT","HMSTRUSDT","ORBSUSDT","ATOMUSDT","SOLUSDT","DOGSUSDT","ETHFIUSDT","SAHARAUSDT","KSMUSDT","AVAXUSDT","IDUSDT","BOMEUSDT","MINAUSDT","AAVEUSDT","LPTUSDT","IOTAUSDT","NEOUSDT","ACTUSDT","TONUSDT","TRXUSDT","CELOUSDT","XTZUSDT","YGGUSDT","COMPUSDT","BTCUSDT","BNBUSDT","NOTUSDT","HBARUSDT","SHELLUSDT","1000000MOGUSDT","DOGEUSDT","CETUSUSDT","XRPUSDT","QTUMUSDT","BANDUSDT","ICPUSDT","KAITOUSDT","CATIUSDT","IMXUSDT","SUIUSDT","LTCUSDT","BRETTUSDT","BNTUSDT","NILUSDT","SPKUSDT","FILUSDT","HUMAUSDT","LQTYUSDT","JUPUSDT","PARTIUSDT","AUCTIONUSDT","MAGICUSDT","DOTUSDT"]
    client = BitgetTickerClient(symbols, on_quote=example_on_quote)

    loop = asyncio.get_running_loop()
    stop_evt = asyncio.Event()

    def _stop():
        if not stop_evt.is_set():
            stop_evt.set()
            asyncio.create_task(client.stop())

    for sig in (signal.SIGINT, signal.SIGTERM):
        try:
            loop.add_signal_handler(sig, _stop)
        except NotImplementedError:
            pass

    runner = asyncio.create_task(client.start())
    await stop_evt.wait()
    await runner

if __name__ == "__main__":
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s | %(levelname)-8s | %(message)s",
        datefmt="%H:%M:%S",
    )
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("用户中断")