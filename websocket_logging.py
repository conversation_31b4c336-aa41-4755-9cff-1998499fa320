#!/usr/bin/env python3
"""
WebSocket日志配置模块
提供带日期的日志文件配置
"""

import os
import logging
from datetime import datetime
from typing import Optional

def setup_websocket_logging(
    log_dir: str = './logs',
    log_level: int = logging.INFO,
    include_console: bool = True,
    logger_name: str = "WebSocketTrader"
) -> logging.Logger:
    """
    设置WebSocket交易日志，文件名包含当前日期
    
    Args:
        log_dir: 日志目录
        log_level: 日志级别
        include_console: 是否同时输出到控制台
        logger_name: 日志记录器名称
    
    Returns:
        配置好的日志记录器
    """
    # 创建日志目录
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    # 获取当前日期
    current_date = datetime.now().strftime('%Y-%m-%d')
    current_time = datetime.now().strftime('%H-%M-%S')
    
    # 生成日志文件名
    log_filename = f'websocket_trades_{current_date}_{current_time}.log'
    log_filepath = os.path.join(log_dir, log_filename)
    
    # 设置日志格式
    log_format = "%(asctime)s | %(levelname)-7s | %(name)s | %(message)s"
    formatter = logging.Formatter(log_format, datefmt="%Y-%m-%d %H:%M:%S")
    
    # 创建日志记录器
    logger = logging.getLogger(logger_name)
    logger.setLevel(log_level)
    
    # 清除现有的处理器（避免重复）
    logger.handlers.clear()
    
    # 添加文件处理器
    file_handler = logging.FileHandler(log_filepath, encoding='utf-8')
    file_handler.setFormatter(formatter)
    file_handler.setLevel(log_level)
    logger.addHandler(file_handler)
    
    # 添加控制台处理器（可选）
    if include_console:
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        console_handler.setLevel(log_level)
        logger.addHandler(console_handler)
    
    # 设置第三方库的日志级别
    logging.getLogger("websockets").setLevel(logging.WARNING)
    logging.getLogger("aiohttp").setLevel(logging.WARNING)
    logging.getLogger("httpx").setLevel(logging.WARNING)
    
    logger.info(f"日志系统初始化完成，日志文件: {log_filepath}")
    
    return logger


def setup_daily_rotating_logger(
    log_dir: str = './logs',
    base_filename: str = 'websocket_trades',
    log_level: int = logging.INFO,
    logger_name: str = "WebSocketTrader"
) -> logging.Logger:
    """
    设置每日轮转的日志记录器
    
    Args:
        log_dir: 日志目录
        base_filename: 基础文件名
        log_level: 日志级别
        logger_name: 日志记录器名称
    
    Returns:
        配置好的日志记录器
    """
    from logging.handlers import TimedRotatingFileHandler
    
    # 创建日志目录
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    # 日志文件路径
    log_filepath = os.path.join(log_dir, f'{base_filename}.log')
    
    # 设置日志格式
    log_format = "%(asctime)s | %(levelname)-7s | %(name)s | %(message)s"
    formatter = logging.Formatter(log_format, datefmt="%Y-%m-%d %H:%M:%S")
    
    # 创建日志记录器
    logger = logging.getLogger(logger_name)
    logger.setLevel(log_level)
    
    # 清除现有的处理器
    logger.handlers.clear()
    
    # 添加时间轮转文件处理器（每天午夜轮转）
    rotating_handler = TimedRotatingFileHandler(
        log_filepath,
        when='midnight',
        interval=1,
        backupCount=30,  # 保留30天的日志
        encoding='utf-8'
    )
    rotating_handler.setFormatter(formatter)
    rotating_handler.setLevel(log_level)
    
    # 设置轮转文件的命名格式
    rotating_handler.suffix = "%Y-%m-%d"
    
    logger.addHandler(rotating_handler)
    
    # 添加控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    console_handler.setLevel(log_level)
    logger.addHandler(console_handler)
    
    # 设置第三方库的日志级别
    logging.getLogger("websockets").setLevel(logging.WARNING)
    logging.getLogger("aiohttp").setLevel(logging.WARNING)
    logging.getLogger("httpx").setLevel(logging.WARNING)
    
    logger.info(f"轮转日志系统初始化完成，日志文件: {log_filepath}")
    
    return logger


def get_dated_log_filename(base_name: str, extension: str = 'log') -> str:
    """
    生成包含日期和时间的日志文件名
    
    Args:
        base_name: 基础文件名
        extension: 文件扩展名
    
    Returns:
        包含日期时间的完整文件名
    """
    current_datetime = datetime.now().strftime('%Y-%m-%d_%H-%M-%S')
    return f"{base_name}_{current_datetime}.{extension}"


# 便捷函数
def quick_setup_logger(name: str = "WebSocketTrader") -> logging.Logger:
    """快速设置日志记录器"""
    return setup_websocket_logging(logger_name=name)


if __name__ == "__main__":
    # 测试日志配置
    test_logger = setup_websocket_logging(logger_name="TestLogger")
    test_logger.info("这是一条测试日志消息")
    test_logger.warning("这是一条警告消息")
    test_logger.error("这是一条错误消息")
    
    print("日志配置测试完成，请检查 ./logs 目录下的日志文件")
