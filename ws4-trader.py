"""
扩展版 ws4：在统一多交易所最优价采集基础上，加入简单跨交易所价差对冲策略逻辑。

新增功能概览：
  - StrategyConfig：开仓 / 平仓阈值、下单数量、最小有效数据要求
  - PriceBook：缓存每个 symbol 各交易所最新 bid/ask
  - SpreadAnalyzer：计算可行开仓组合、跟踪已建仓位的平仓条件
  - PositionManager：记录与更新持仓（长/短对冲对）
  - 模拟下单执行（占位 stub，可替换成真实 REST 下单）

策略逻辑（简化版本）：
  开仓：
      For symbol:
        对任意两个不同交易所 (ex_buy, ex_sell)
          开仓方向假设：在 ex_buy 以 ask_buy 买入，同时在 ex_sell 以 bid_sell 卖出
          价差 = (bid_sell - ask_buy) / ask_buy
          若 价差 >= OPEN_SPREAD_PCT 且 两侧数据新鲜 且 未持仓：
              同步执行两腿订单，建立对冲仓位
  平仓：
      对已持仓 (long_ex, short_ex):
          当前可平仓价差 = (bid_short_ex - ask_long_ex) / ask_long_ex
          我们当初是 (long_ex=买低 ask_long_open, short_ex=卖高 bid_short_open)。
          价差下降代表收敛，将在 diff <= CLOSE_SPREAD_PCT 时平仓（示例为收敛套利）
          也可添加止损：若 diff > STOP_LOSS_SPREAD_PCT 则强制平仓
          注：若你希望即时锁定开仓利润（执行即锁定），可改为在开仓后用资金费率/期限判断，不等待收敛。

注意：
  - 价差方向固定为：开仓时 (sell leg bid - buy leg ask) / buy leg ask
  - 平仓时仍使用同一方向 diff（希望它下降）。可以根据策略改为另一种定义。
  - 真实环境必须处理部分成交、滑点、重复下单保护、账户余额检查、异常重试、手续费 / 资金费率评估等。

Author: Adapted by gpt-5
"""

from __future__ import annotations

import asyncio
import json
import logging
import signal
import time
import random
from dataclasses import dataclass, field
from typing import Any, Dict, List, Optional, Callable, Awaitable, Tuple

import websockets
from websockets import WebSocketClientProtocol

# --------------------------------------------------
# Strategy Configuration
# --------------------------------------------------

@dataclass
class StrategyConfig:
    OPEN_SPREAD_PCT: float = 0.002        # 开仓最小价差 (0.2%)
    CLOSE_SPREAD_PCT: float = 0.0005      # 平仓目标价差 (0.05%) 收敛后关闭
    STOP_LOSS_SPREAD_PCT: float = 0.004   # 止损：若价差进一步拉大到 0.4%（可选）
    ORDER_SIZE: float = 0.001             # 下单数量 (示例：BTC 0.001)
    MIN_PRICE_AGE_MS: int = 3000          # 允许的数据最大延迟
    MIN_BOOK_EXCHANGES: int = 2           # 至少 2 个交易所才计算
    SYMBOL_WHITELIST: Optional[List[str]] = None  # 若限定 symbol 集合
    COOLDOWN_AFTER_OPEN_SEC: int = 2      # 开仓后短暂冷却，避免重复判定
    PRINT_DECISIONS: bool = True

# --------------------------------------------------
# Unified Quote Model (unchanged)
# --------------------------------------------------

@dataclass
class BestQuote:
    exchange: str
    symbol: str          # normalized symbol, e.g. BTCUSDT
    raw_symbol: str
    bid: float
    bid_size: float
    ask: float
    ask_size: float
    ts_exchange: int
    ts_local: int
    latency_ms: float
    extra: Dict[str, Any] = field(default_factory=dict)

# --------------------------------------------------
# Position & Trade Data Structures
# --------------------------------------------------

@dataclass
class HedgePosition:
    symbol: str
    long_exchange: str
    short_exchange: str
    long_qty: float
    short_qty: float
    long_open_price: float
    short_open_price: float
    open_spread_pct: float
    open_time: float
    last_eval_time: float
    # 可扩展字段：手续费、资金费率预计、目标收益、风控标签等

class PositionManager:
    def __init__(self):
        self._positions: Dict[str, HedgePosition] = {}
        self._lock = asyncio.Lock()
        self._last_open_ts: Dict[str, float] = {}

    async def has_position(self, symbol: str) -> bool:
        return symbol in self._positions

    async def get_position(self, symbol: str) -> Optional[HedgePosition]:
        return self._positions.get(symbol)

    async def can_open(self, symbol: str, cfg: StrategyConfig) -> bool:
        if await self.has_position(symbol):
            return False
        last_ts = self._last_open_ts.get(symbol, 0)
        if time.time() - last_ts < cfg.COOLDOWN_AFTER_OPEN_SEC:
            return False
        return True

    async def open_position(
        self,
        symbol: str,
        long_ex: str,
        short_ex: str,
        long_price: float,
        short_price: float,
        spread_pct: float,
        cfg: StrategyConfig
    ):
        async with self._lock:
            pos = HedgePosition(
                symbol=symbol,
                long_exchange=long_ex,
                short_exchange=short_ex,
                long_qty=cfg.ORDER_SIZE,
                short_qty=cfg.ORDER_SIZE,
                long_open_price=long_price,
                short_open_price=short_price,
                open_spread_pct=spread_pct,
                open_time=time.time(),
                last_eval_time=time.time()
            )
            self._positions[symbol] = pos
            self._last_open_ts[symbol] = time.time()
            return pos

    async def close_position(self, symbol: str) -> Optional[HedgePosition]:
        async with self._lock:
            return self._positions.pop(symbol, None)

    async def list_positions(self) -> List[HedgePosition]:
        return list(self._positions.values())

# --------------------------------------------------
# Price Book
# --------------------------------------------------

class PriceBook:
    def __init__(self):
        # prices[symbol][exchange] = {"bid":..., "ask":..., "ts": epoch_ms}
        self.prices: Dict[str, Dict[str, Dict[str, float]]] = {}

    def update(self, q: BestQuote):
        sym = q.symbol
        if sym not in self.prices:
            self.prices[sym] = {}
        self.prices[sym][q.exchange] = {
            "bid": q.bid,
            "ask": q.ask,
            "ts": q.ts_local
        }

    def get_symbol_exchanges(self, symbol: str) -> Dict[str, Dict[str, float]]:
        return self.prices.get(symbol, {})

    def is_fresh(self, symbol: str, exchange: str, max_age_ms: int) -> bool:
        now_ms = int(time.time() * 1000)
        ex_map = self.prices.get(symbol, {})
        node = ex_map.get(exchange)
        if not node:
            return False
        return (now_ms - node["ts"]) <= max_age_ms

# --------------------------------------------------
# Simple Order Execution Stub
# --------------------------------------------------

class ExecutionClient:
    """
    订单执行占位：
      - 实盘：请替换为具体交易所 REST / 私有 WS 下单逻辑
      - 需要：账户余额检查、滑点保护、重试、异步并发、错误分类
    """
    async def place_order(
        self,
        exchange: str,
        symbol: str,
        side: str,
        qty: float,
        ref_price: float
    ) -> Tuple[bool, float]:
        # 模拟立即成交 + 0 滑点
        await asyncio.sleep(0)  # 让出事件循环
        fill_price = ref_price
        logging.info(f"[EXEC] {exchange} {symbol} {side} {qty} @ {fill_price}")
        return True, fill_price

# --------------------------------------------------
# Spread Analyzer / Strategy Core
# --------------------------------------------------

class CrossExchangeArbStrategy:
    def __init__(self, cfg: StrategyConfig, position_mgr: PositionManager, book: PriceBook, executor: ExecutionClient):
        self.cfg = cfg
        self.positions = position_mgr
        self.book = book
        self.executor = executor
        self._lock = asyncio.Lock()  # 防止同一时刻并发决策

    async def on_quote(self, q: BestQuote):
        """
        在采集层回调中调用。
        逻辑：
          1. 更新价格
          2. 若 symbol 在白名单（或未设置白名单）→ 判定
        """
        self.book.update(q)

        if self.cfg.SYMBOL_WHITELIST and q.symbol not in self.cfg.SYMBOL_WHITELIST:
            return

        # 避免所有 symbol 的 quote 都触发大量并发：加一个轻量节流锁
        async with self._lock:
            # 已有仓位 → 尝试平仓
            if await self.positions.has_position(q.symbol):
                await self._try_close(q.symbol)
            else:
                await self._try_open(q.symbol)

    async def _try_open(self, symbol: str):
        if not await self.positions.can_open(symbol, self.cfg):
            return

        ex_map = self.book.get_symbol_exchanges(symbol)
        if len(ex_map) < self.cfg.MIN_BOOK_EXCHANGES:
            return

        # 寻找最大开仓价差
        best_candidate = None  # (spread_pct, buy_ex, sell_ex, ask_buy, bid_sell)
        for ex_buy, pb_buy in ex_map.items():
            if not self.book.is_fresh(symbol, ex_buy, self.cfg.MIN_PRICE_AGE_MS):
                continue
            ask_buy = pb_buy["ask"]
            if ask_buy <= 0:
                continue
            for ex_sell, pb_sell in ex_map.items():
                if ex_sell == ex_buy:
                    continue
                if not self.book.is_fresh(symbol, ex_sell, self.cfg.MIN_PRICE_AGE_MS):
                    continue
                bid_sell = pb_sell["bid"]
                if bid_sell <= 0:
                    continue
                spread_pct = (bid_sell - ask_buy) / ask_buy
                if spread_pct >= self.cfg.OPEN_SPREAD_PCT:
                    if (best_candidate is None) or (spread_pct > best_candidate[0]):
                        best_candidate = (spread_pct, ex_buy, ex_sell, ask_buy, bid_sell)

        if not best_candidate:
            return

        spread_pct, buy_ex, sell_ex, ask_buy, bid_sell = best_candidate

        # 下单（模拟）—— 先买再卖或并发都可，这里并发
        if self.cfg.PRINT_DECISIONS:
            logging.info(f"[ARB-OPEN] {symbol} buy {buy_ex}@{ask_buy:.4f} / sell {sell_ex}@{bid_sell:.4f} spread={spread_pct:.4%}")

        size = self.cfg.ORDER_SIZE

        # 并发执行两腿
        buy_task = asyncio.create_task(self.executor.place_order(buy_ex, symbol, "BUY", size, ask_buy))
          # 卖出对冲腿
        sell_task = asyncio.create_task(self.executor.place_order(sell_ex, symbol, "SELL", size, bid_sell))
        done, _ = await asyncio.wait({buy_task, sell_task}, return_when=asyncio.ALL_COMPLETED)

        buy_ok, buy_fill = buy_task.result()
        sell_ok, sell_fill = sell_task.result()

        if buy_ok and sell_ok:
            pos = await self.positions.open_position(
                symbol,
                long_ex=buy_ex,
                short_ex=sell_ex,
                long_price=buy_fill,
                short_price=sell_fill,
                spread_pct=spread_pct,
                cfg=self.cfg
            )
            if self.cfg.PRINT_DECISIONS:
                logging.info(f"[ARB-OPENED] {symbol} spread_open={spread_pct:.4%} long={pos.long_exchange}@{buy_fill:.4f} short={pos.short_exchange}@{sell_fill:.4f}")
        else:
            if self.cfg.PRINT_DECISIONS:
                logging.warning(f"[ARB-OPEN-FAIL] {symbol} buy_ok={buy_ok} sell_ok={sell_ok}")

    async def _try_close(self, symbol: str):
        pos = await self.positions.get_position(symbol)
        if not pos:
            return

        ex_map = self.book.get_symbol_exchanges(symbol)
        need_ex = {pos.long_exchange, pos.short_exchange}
        if not need_ex.issubset(set(ex_map.keys())):
            return

        long_leg = ex_map[pos.long_exchange]
        short_leg = ex_map[pos.short_exchange]

        if not (self.book.is_fresh(symbol, pos.long_exchange, self.cfg.MIN_PRICE_AGE_MS) and
                self.book.is_fresh(symbol, pos.short_exchange, self.cfg.MIN_PRICE_AGE_MS)):
            return

        # 当前可平仓差：仍使用 (bid_short - ask_long) / ask_long
        bid_short = short_leg["bid"]
        ask_long = long_leg["ask"]
        if ask_long <= 0:
            return
        current_spread = (bid_short - ask_long) / ask_long

        # 判断平仓：
        should_close = False
        reason = ""

        # 收敛条件：spread <= 目标平仓阈值
        if current_spread <= self.cfg.CLOSE_SPREAD_PCT:
            should_close = True
            reason = f"converged spread={current_spread:.4%} <= {self.cfg.CLOSE_SPREAD_PCT:.4%}"
        # 止损：价差继续扩大
        elif current_spread >= self.cfg.STOP_LOSS_SPREAD_PCT:
            should_close = True
            reason = f"stop_loss spread={current_spread:.4%} >= {self.cfg.STOP_LOSS_SPREAD_PCT:.4%}"

        if not should_close:
            return

        if self.cfg.PRINT_DECISIONS:
            logging.info(f"[ARB-CLOSE] {symbol} reason={reason}")

        # 平仓腿：
        #   long leg: 卖出 (bid_long 预估)
        #   short leg: 买回 (ask_short 预估)
        bid_long_close = long_leg["bid"]
        ask_short_close = short_leg["ask"]
        if bid_long_close <= 0 or ask_short_close <= 0:
            return

        sell_long_task = asyncio.create_task(
            self.executor.place_order(pos.long_exchange, symbol, "SELL", pos.long_qty, bid_long_close)
        )
        buy_short_task = asyncio.create_task(
            self.executor.place_order(pos.short_exchange, symbol, "BUY", pos.short_qty, ask_short_close)
        )
        await asyncio.wait({sell_long_task, buy_short_task}, return_when=asyncio.ALL_COMPLETED)
        sell_ok, sell_fill = sell_long_task.result()
        buy_ok, buy_fill = buy_short_task.result()

        if sell_ok and buy_ok:
            closed = await self.positions.close_position(symbol)
            if closed and self.cfg.PRINT_DECISIONS:
                pnl_long = (sell_fill - closed.long_open_price) * closed.long_qty
                pnl_short = (closed.short_open_price - buy_fill) * closed.short_qty
                gross_pnl = pnl_long + pnl_short
                logging.info(
                    f"[ARB-CLOSED] {symbol} open_spread={closed.open_spread_pct:.4%} "
                    f"close_spread={current_spread:.4%} PnL={gross_pnl:.6f} "
                    f"(long {closed.long_exchange} +{pnl_long:.6f}, short {closed.short_exchange} +{pnl_short:.6f})"
                )
        else:
            logging.warning(f"[ARB-CLOSE-FAIL] {symbol} sell_long_ok={sell_ok} buy_short_ok={buy_ok}")

# --------------------------------------------------
# (Below) Original WS Framework (lightly integrated)
# --------------------------------------------------

class BaseWSClient:
    name: str = "base"

    def __init__(
        self,
        symbols: List[str],
        on_quote: Callable[[BestQuote], Awaitable[None]],
        heartbeat_interval: int = 20,
        message_timeout: int = 60,
        reconnect_base: int = 2,
        reconnect_max: int = 60,
    ):
        self.input_symbols = symbols[:]
        self.on_quote = on_quote
        self.heartbeat_interval = heartbeat_interval
        self.message_timeout = message_timeout
        self.reconnect_base = reconnect_base
        self.reconnect_max = reconnect_max

        self._ws: Optional[WebSocketClientProtocol] = None
        self._stop_evt = asyncio.Event()
        self._last_msg_time = 0.0
        self._tasks: List[asyncio.Task] = []
        self._reconnect_attempt = 0

    async def _connect(self) -> WebSocketClientProtocol:
        raise NotImplementedError

    async def _build_subscribe(self):
        raise NotImplementedError

    async def _handle_message(self, msg: Any):
        raise NotImplementedError

    async def _send_heartbeat(self):
        if self._ws:
            try:
                await self._ws.ping()
            except Exception as e:
                logging.debug("[%s] heartbeat ping error: %s", self.name, e)

    async def start(self):
        logging.info("[%s] starting symbols=%s", self.name, self.input_symbols)
        while not self._stop_evt.is_set():
            try:
                await self._run_once()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logging.exception("[%s] run loop exception: %s", self.name, e)

            if self._stop_evt.is_set():
                break

            self._reconnect_attempt += 1
            delay = min(self.reconnect_base * (2 ** (self._reconnect_attempt - 1)), self.reconnect_max)
            jitter = random.uniform(0, delay * 0.25)
            wait_time = delay + jitter
            logging.warning("[%s] reconnecting in %.2f s (attempt %d)", self.name, wait_time, self._reconnect_attempt)
            try:
                await asyncio.wait_for(self._stop_evt.wait(), timeout=wait_time)
            except asyncio.TimeoutError:
                pass

        logging.info("[%s] stopped", self.name)

    async def stop(self):
        self._stop_evt.set()
        await self._close_ws()
        for t in self._tasks:
            if not t.done():
                t.cancel()
        await asyncio.gather(*self._tasks, return_exceptions=True)

    async def _close_ws(self):
        if self._ws:
            try:
                await self._ws.close()
            except Exception:
                pass
        self._ws = None

    async def _run_once(self):
        self._ws = await self._connect()
        self._reconnect_attempt = 0
        self._last_msg_time = time.time()
        await self._build_subscribe()

        recv_task = asyncio.create_task(self._recv_loop(), name=f"{self.name}-recv")
        hb_task = asyncio.create_task(self._heartbeat_loop(), name=f"{self.name}-hb")
        wd_task = asyncio.create_task(self._watchdog_loop(), name=f"{self.name}-wd")
        self._tasks = [recv_task, hb_task, wd_task]

        done, pending = await asyncio.wait(self._tasks, return_when=asyncio.FIRST_EXCEPTION)
        for d in done:
            if d.exception():
                raise d.exception()

    async def _recv_loop(self):
        assert self._ws
        ws = self._ws
        async for raw in ws:
            self._last_msg_time = time.time()
            try:
                if isinstance(raw, bytes):
                    raw = raw.decode()
                msg = json.loads(raw)
            except Exception:
                logging.debug("[%s] non-json frame: %s", self.name, raw)
                continue
            await self._handle_message(msg)

    async def _heartbeat_loop(self):
        while not self._stop_evt.is_set() and self._ws:
            await asyncio.sleep(self.heartbeat_interval)
            if self._ws:
                try:
                    await self._send_heartbeat()
                except Exception as e:
                    logging.debug("[%s] heartbeat error: %s", self.name, e)
                    break

    async def _watchdog_loop(self):
        while not self._stop_evt.is_set() and self._ws:
            await asyncio.sleep(5)
            idle = time.time() - self._last_msg_time
            if idle > self.message_timeout:
                logging.warning("[%s] inactivity %.1fs > %ss, reconnecting",
                                self.name, idle, self.message_timeout)
                await self._close_ws()
                break

    async def _emit_quote(
        self,
        symbol_norm: str,
        raw_symbol: str,
        bid: float,
        bid_size: float,
        ask: float,
        ask_size: float,
        ts_exchange: int,
        extra: Dict[str, Any] | None = None,
    ):
        ts_local = int(time.time() * 1000)
        latency = ts_local - ts_exchange if ts_exchange > 0 else -1
        quote = BestQuote(
            exchange=self.name,
            symbol=symbol_norm,
            raw_symbol=raw_symbol,
            bid=bid,
            bid_size=bid_size,
            ask=ask,
            ask_size=ask_size,
            ts_exchange=ts_exchange,
            ts_local=ts_local,
            latency_ms=latency,
            extra=extra or {},
        )
        await self.on_quote(quote)

# ---------------- Bitget ---------------- #

class BitgetClient(BaseWSClient):
    name = "bitget"
    WS_URL = "wss://ws.bitget.com/v2/ws/public"
    INST_TYPE = "USDT-FUTURES"

    def __init__(self, symbols: List[str], on_quote: Callable[[BestQuote], Awaitable[None]]):
        super().__init__(symbols, on_quote, heartbeat_interval=15, message_timeout=45)

    async def _connect(self):
        logging.info("[bitget] connecting %s", self.WS_URL)
        return await websockets.connect(self.WS_URL, ping_interval=None, ping_timeout=None, max_queue=1024)

    async def _build_subscribe(self):
        args = [
            {
                "instType": self.INST_TYPE,
                "channel": "ticker",
                "instId": s.upper().replace("-", "").replace("_", "")
            } for s in self.input_symbols
        ]
        req = {"op": "subscribe", "args": args}
        await self._ws.send(json.dumps(req))
        logging.info("[bitget] subscribed %s", args)

    async def _send_heartbeat(self):
        if self._ws:
            await self._ws.send(json.dumps({"op": "ping"}))

    async def _handle_message(self, msg: Any):
        if msg.get("event") == "error":
            logging.error("[bitget] error %s", msg)
            return
        if msg.get("op") == "ping":
            await self._ws.send(json.dumps({"op": "pong"}))
            return
        if msg.get("op") == "pong":
            return
        if "arg" in msg and "data" in msg:
            arg = msg["arg"]
            if arg.get("channel") != "ticker":
                return
            for d in msg.get("data", []):
                raw_symbol = d.get("instId") or arg.get("instId")
                bid = d.get("bidPr") or d.get("bestBid")
                ask = d.get("askPr") or d.get("bestAsk")
                bid_sz = d.get("bidSz") or d.get("bestBidSize")
                ask_sz = d.get("askSz") or d.get("bestAskSize")
                ts = d.get("ts") or d.get("systemTime")
                try:
                    await self._emit_quote(
                        symbol_norm=raw_symbol.replace("-", "").upper(),
                        raw_symbol=raw_symbol,
                        bid=float(bid or 0),
                        bid_size=float(bid_sz or 0),
                        ask=float(ask or 0),
                        ask_size=float(ask_sz or 0),
                        ts_exchange=int(ts) if ts else 0,
                        extra={"raw": d},
                    )
                except Exception as e:
                    logging.debug("[bitget] parse error: %s raw=%s", e, d)

# ---------------- Binance Futures ---------------- #

class BinanceFuturesClient(BaseWSClient):
    name = "binance"
    BASE = "wss://fstream.binance.com/stream?streams="

    def __init__(self, symbols: List[str], on_quote: Callable[[BestQuote], Awaitable[None]]):
        super().__init__(symbols, on_quote, heartbeat_interval=30, message_timeout=90)

    async def _connect(self):
        streams = "/".join([f"{s.lower()}@bookTicker" for s in self.input_symbols])
        url = self.BASE + streams
        logging.info("[binance] connecting %s", url)
        return await websockets.connect(url, ping_interval=None, ping_timeout=None, max_queue=2048)

    async def _build_subscribe(self):
        logging.info("[binance] combined stream active.")

    async def _handle_message(self, msg: Any):
        data = msg.get("data")
        if not data:
            return
        try:
            symbol = data["s"]
            bid = float(data["b"])
            ask = float(data["a"])
            bid_sz = float(data["B"])
            ask_sz = float(data["A"])
            ts = int(data.get("E") or data.get("T") or 0)
            await self._emit_quote(
                symbol_norm=symbol.upper(),
                raw_symbol=symbol.upper(),
                bid=bid,
                bid_size=bid_sz,
                ask=ask,
                ask_size=ask_sz,
                ts_exchange=ts,
                extra={"raw": data},
            )
        except Exception as e:
            logging.debug("[binance] parse error %s raw=%s", e, msg)

# ---------------- Bybit ---------------- #

class BybitClient(BaseWSClient):
    name = "bybit"
    WS_URL = "wss://stream.bybit.com/v5/public/linear"

    def __init__(self, symbols: List[str], on_quote: Callable[[BestQuote], Awaitable[None]]):
        super().__init__(symbols, on_quote, heartbeat_interval=20, message_timeout=60)

    async def _connect(self):
        logging.info("[bybit] connecting %s", self.WS_URL)
        return await websockets.connect(self.WS_URL, ping_interval=None, ping_timeout=None, max_queue=1024)

    async def _build_subscribe(self):
        args = [f"tickers.{s.upper()}" for s in self.input_symbols]
        req = {"op": "subscribe", "args": args}
        await self._ws.send(json.dumps(req))
        logging.info("[bybit] subscribed %s", args)

    async def _send_heartbeat(self):
        if self._ws:
            await self._ws.send(json.dumps({"op": "ping"}))

    async def _handle_message(self, msg: Any):
        if msg.get("op") == "pong":
            return
        if msg.get("op") == "ping":
            await self._ws.send(json.dumps({"op": "pong"}))
            return
        topic = msg.get("topic")
        if not topic or not topic.startswith("tickers."):
            return
        data = msg.get("data")
        if not data:
            return
        try:
            symbol = data.get("symbol") or topic.split(".", 1)[1]
            bid = float(data.get("bid1Price") or 0)
            ask = float(data.get("ask1Price") or 0)
            bid_sz = float(data.get("bid1Size") or 0)
            ask_sz = float(data.get("ask1Size") or 0)
            ts = int(data.get("ts") or data.get("timestamp") or 0)
            await self._emit_quote(
                symbol_norm=symbol.upper(),
                raw_symbol=symbol.upper(),
                bid=bid,
                bid_size=bid_sz,
                ask=ask,
                ask_size=ask_sz,
                ts_exchange=ts,
                extra={"raw": data},
            )
        except Exception as e:
            logging.debug("[bybit] parse error %s raw=%s", e, data)

# ---------------- OKX ---------------- #

class OKXClient(BaseWSClient):
    name = "okx"
    WS_URL = "wss://ws.okx.com:8443/ws/v5/public"

    def __init__(self, symbols: List[str], on_quote: Callable[[BestQuote], Awaitable[None]]):
        super().__init__(symbols, on_quote, heartbeat_interval=15, message_timeout=45)

    @staticmethod
    def _to_swap_inst(symbol: str) -> str:
        s = symbol.upper().replace("_", "").replace("-", "")
        if s.endswith("USDT"):
            base = s[:-4]
            return f"{base}-USDT-SWAP"
        return symbol

    async def _connect(self):
        logging.info("[okx] connecting %s", self.WS_URL)
        return await websockets.connect(self.WS_URL, ping_interval=None, ping_timeout=None, max_queue=1024)

    async def _build_subscribe(self):
        args = [{"channel": "tickers", "instId": self._to_swap_inst(s)} for s in self.input_symbols]
        req = {"op": "subscribe", "args": args}
        await self._ws.send(json.dumps(req))
        logging.info("[okx] subscribed %s", args)

    async def _send_heartbeat(self):
        if self._ws:
            await self._ws.send(json.dumps({"op": "ping"}))

    async def _handle_message(self, msg: Any):
        if msg.get("event") == "error":
            logging.error("[okx] error: %s", msg)
            return
        if msg.get("op") == "ping":
            await self._ws.send(json.dumps({"op": "pong"}))
            return
        if msg.get("op") == "pong":
            return

        if "arg" in msg and msg.get("arg", {}).get("channel") == "tickers":
            data_list = msg.get("data") or []
            for d in data_list:
                raw_inst = d.get("instId")
                bid = d.get("bidPx")
                ask = d.get("askPx")
                bid_sz = d.get("bidSz")
                ask_sz = d.get("askSz")
                ts = d.get("ts")
                try:
                    await self._emit_quote(
                        symbol_norm=raw_inst.replace("-", "").replace("SWAP", "").upper(),
                        raw_symbol=raw_inst,
                        bid=float(bid or 0),
                        bid_size=float(bid_sz or 0),
                        ask=float(ask or 0),
                        ask_size=float(ask_sz or 0),
                        ts_exchange=int(ts) if ts else 0,
                        extra={"raw": d},
                    )
                except Exception as e:
                    logging.debug("[okx] parse error %s raw=%s", e, d)

# --------------------------------------------------
# Aggregator / Runner + Strategy Integration
# --------------------------------------------------

class MultiExchangeRunner:
    def __init__(self, config: Dict[str, List[str]], strategy: CrossExchangeArbStrategy):
        self.config = config
        self.strategy = strategy
        self.clients: List[BaseWSClient] = []
        self._stop_evt = asyncio.Event()

    async def on_quote(self, quote: BestQuote):
        # 原打印或日志
        logging.debug(
            f"{quote.exchange.upper():7s} {quote.symbol:10s} "
            f"B:{quote.bid:.2f} A:{quote.ask:.2f} ex_ts={quote.ts_exchange} lat={quote.latency_ms}ms"
        )
        # 策略处理
        await self.strategy.on_quote(quote)

    def _build_clients(self):
        for ex, symbols in self.config.items():
            if not symbols:
                continue
            ex_lower = ex.lower()
            if ex_lower == "bitget":
                self.clients.append(BitgetClient(symbols, self.on_quote))
            elif ex_lower == "binance":
                self.clients.append(BinanceFuturesClient(symbols, self.on_quote))
            elif ex_lower == "bybit":
                self.clients.append(BybitClient(symbols, self.on_quote))
            elif ex_lower == "okx":
                self.clients.append(OKXClient(symbols, self.on_quote))
            else:
                logging.warning("Unknown exchange: %s", ex)

    async def start(self):
        self._build_clients()
        tasks = [asyncio.create_task(c.start(), name=f"{c.name}-main") for c in self.clients]
        await self._stop_evt.wait()
        # orderly shutdown
        await asyncio.gather(*(c.stop() for c in self.clients), return_exceptions=True)
        for t in tasks:
            if not t.done():
                t.cancel()
        await asyncio.gather(*tasks, return_exceptions=True)

    def stop(self):
        if not self._stop_evt.is_set():
            self._stop_evt.set()

# --------------------------------------------------
# Main
# --------------------------------------------------

async def main():
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s | %(levelname)-7s | %(message)s",
        datefmt="%H:%M:%S",
    )

    # 1. 配置策略
    strat_cfg = StrategyConfig(
        OPEN_SPREAD_PCT=0.002,         # 0.2%
        CLOSE_SPREAD_PCT=0.0005,       # 0.05%
        STOP_LOSS_SPREAD_PCT=0.004,    # 0.4%
        ORDER_SIZE=0.001,              # 0.001 BTC
        SYMBOL_WHITELIST=None,         # 例如 ["BTCUSDT"]
        PRINT_DECISIONS=True
    )

    price_book = PriceBook()
    position_mgr = PositionManager()
    executor = ExecutionClient()
    strategy = CrossExchangeArbStrategy(strat_cfg, position_mgr, price_book, executor)

    # 2. 订阅配置（可精简 symbol）
    config = {
        "bitget": ["OPUSDT","LDOUSDT","NEARUSDT","CRVUSDT","ZETAUSDT","LRCUSDT","ARBUSDT","LINKUSDT","BCHUSDT","MASKUSDT","ALGOUSDT","YFIUSDT","XLMUSDT","CROUSDT","ETHWUSDT","ETHUSDT","ETCUSDT","USDCUSDT","THETAUSDT","CHZUSDT","AXSUSDT","SANDUSDT","ADAUSDT","FLOWUSDT","AI16ZUSDT","INJUSDT","SUSHIUSDT","1INCHUSDT","MAJORUSDT","MYRIAUSDT","CFXUSDT","ZILUSDT","GMTUSDT","HMSTRUSDT","ORBSUSDT","ATOMUSDT","SOLUSDT","DOGSUSDT","ETHFIUSDT","SAHARAUSDT","KSMUSDT","AVAXUSDT","IDUSDT","BOMEUSDT","MINAUSDT","AAVEUSDT","LPTUSDT","IOTAUSDT","NEOUSDT","ACTUSDT","TONUSDT","TRXUSDT","CELOUSDT","XTZUSDT","YGGUSDT","COMPUSDT","BTCUSDT","BNBUSDT","NOTUSDT","HBARUSDT","SHELLUSDT","1000000MOGUSDT","DOGEUSDT","CETUSUSDT","XRPUSDT","QTUMUSDT","BANDUSDT","ICPUSDT","KAITOUSDT","CATIUSDT","IMXUSDT","SUIUSDT","LTCUSDT","BRETTUSDT","BNTUSDT","NILUSDT","SPKUSDT","FILUSDT","HUMAUSDT","LQTYUSDT","JUPUSDT","PARTIUSDT","AUCTIONUSDT","MAGICUSDT","DOTUSDT"],
        "binance": ["OPUSDT","LDOUSDT","NEARUSDT","CRVUSDT","ZETAUSDT","LRCUSDT","ARBUSDT","LINKUSDT","BCHUSDT","MASKUSDT","ALGOUSDT","YFIUSDT","XLMUSDT","CROUSDT","ETHWUSDT","ETHUSDT","ETCUSDT","USDCUSDT","THETAUSDT","CHZUSDT","AXSUSDT","SANDUSDT","ADAUSDT","FLOWUSDT","AI16ZUSDT","INJUSDT","SUSHIUSDT","1INCHUSDT","MAJORUSDT","MYRIAUSDT","CFXUSDT","ZILUSDT","GMTUSDT","HMSTRUSDT","ORBSUSDT","ATOMUSDT","SOLUSDT","DOGSUSDT","ETHFIUSDT","SAHARAUSDT","KSMUSDT","AVAXUSDT","IDUSDT","BOMEUSDT","MINAUSDT","AAVEUSDT","LPTUSDT","IOTAUSDT","NEOUSDT","ACTUSDT","TONUSDT","TRXUSDT","CELOUSDT","XTZUSDT","YGGUSDT","COMPUSDT","BTCUSDT","BNBUSDT","NOTUSDT","HBARUSDT","SHELLUSDT","1000000MOGUSDT","DOGEUSDT","CETUSUSDT","XRPUSDT","QTUMUSDT","BANDUSDT","ICPUSDT","KAITOUSDT","CATIUSDT","IMXUSDT","SUIUSDT","LTCUSDT","BRETTUSDT","BNTUSDT","NILUSDT","SPKUSDT","FILUSDT","HUMAUSDT","LQTYUSDT","JUPUSDT","PARTIUSDT","AUCTIONUSDT","MAGICUSDT","DOTUSDT"],
        "bybit": ["OPUSDT","LDOUSDT","NEARUSDT","CRVUSDT","ZETAUSDT","LRCUSDT","ARBUSDT","LINKUSDT","BCHUSDT","MASKUSDT","ALGOUSDT","YFIUSDT","XLMUSDT","CROUSDT","ETHWUSDT","ETHUSDT","ETCUSDT","USDCUSDT","THETAUSDT","CHZUSDT","AXSUSDT","SANDUSDT","ADAUSDT","FLOWUSDT","AI16ZUSDT","INJUSDT","SUSHIUSDT","1INCHUSDT","MAJORUSDT","MYRIAUSDT","CFXUSDT","ZILUSDT","GMTUSDT","HMSTRUSDT","ORBSUSDT","ATOMUSDT","SOLUSDT","DOGSUSDT","ETHFIUSDT","SAHARAUSDT","KSMUSDT","AVAXUSDT","IDUSDT","BOMEUSDT","MINAUSDT","AAVEUSDT","LPTUSDT","IOTAUSDT","NEOUSDT","ACTUSDT","TONUSDT","TRXUSDT","CELOUSDT","XTZUSDT","YGGUSDT","COMPUSDT","BTCUSDT","BNBUSDT","NOTUSDT","HBARUSDT","SHELLUSDT","1000000MOGUSDT","DOGEUSDT","CETUSUSDT","XRPUSDT","QTUMUSDT","BANDUSDT","ICPUSDT","KAITOUSDT","CATIUSDT","IMXUSDT","SUIUSDT","LTCUSDT","BRETTUSDT","BNTUSDT","NILUSDT","SPKUSDT","FILUSDT","HUMAUSDT","LQTYUSDT","JUPUSDT","PARTIUSDT","AUCTIONUSDT","MAGICUSDT","DOTUSDT"],
        "okx": ["OPUSDT","LDOUSDT","NEARUSDT","CRVUSDT","ZETAUSDT","LRCUSDT","ARBUSDT","LINKUSDT","BCHUSDT","MASKUSDT","ALGOUSDT","YFIUSDT","XLMUSDT","CROUSDT","ETHWUSDT","ETHUSDT","ETCUSDT","USDCUSDT","THETAUSDT","CHZUSDT","AXSUSDT","SANDUSDT","ADAUSDT","FLOWUSDT","AI16ZUSDT","INJUSDT","SUSHIUSDT","1INCHUSDT","MAJORUSDT","MYRIAUSDT","CFXUSDT","ZILUSDT","GMTUSDT","HMSTRUSDT","ORBSUSDT","ATOMUSDT","SOLUSDT","DOGSUSDT","ETHFIUSDT","SAHARAUSDT","KSMUSDT","AVAXUSDT","IDUSDT","BOMEUSDT","MINAUSDT","AAVEUSDT","LPTUSDT","IOTAUSDT","NEOUSDT","ACTUSDT","TONUSDT","TRXUSDT","CELOUSDT","XTZUSDT","YGGUSDT","COMPUSDT","BTCUSDT","BNBUSDT","NOTUSDT","HBARUSDT","SHELLUSDT","1000000MOGUSDT","DOGEUSDT","CETUSUSDT","XRPUSDT","QTUMUSDT","BANDUSDT","ICPUSDT","KAITOUSDT","CATIUSDT","IMXUSDT","SUIUSDT","LTCUSDT","BRETTUSDT","BNTUSDT","NILUSDT","SPKUSDT","FILUSDT","HUMAUSDT","LQTYUSDT","JUPUSDT","PARTIUSDT","AUCTIONUSDT","MAGICUSDT","DOTUSDT"],
    }

    runner = MultiExchangeRunner(config, strategy)

    loop = asyncio.get_running_loop()
    for sig in (signal.SIGINT, signal.SIGTERM):
        try:
            loop.add_signal_handler(sig, runner.stop)
        except NotImplementedError:
            pass

    await runner.start()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("Interrupted")