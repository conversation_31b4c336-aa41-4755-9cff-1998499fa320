from shared_imports import redis,logging,time,os,Config,asyncio,json,websockets,ConnectionClosedError,ConnectionClosedOK,copy
from trading_state import TradingState
from shared_imports import websockets,asyncio, json,ConnectionClosedError,ConnectionClosedOK,Config,datetime
from trading_state import TradingState
import asyncio
import json
import websockets
# from data_service_test import bitget_ws

state = TradingState()
state.init_symbols()
symbols = state.symbols


MAX_RECONNECT_ATTEMPTS = 5
RECONNECT_DELAY = 5  # seconds
CONNECTION_TIMEOUT = 10  # seconds


# 深度账本参数             排序     bid     ask
#   - binance   20               降序     升序
#   - bybit     50               降序     升序
#   - bitget    15               降序     升序
#   - Okx       15               降序     升序

# 通用WebSocket连接器
async def generic_ws_connector(exchange_name, ws_config, state:TradingState):
    """通用WebSocket连接器，减少重复代码"""
    reconnect_count = 0
    keepalive_task = None

    while reconnect_count < Config.MAX_RECONNECT_ATTEMPTS:
        try:
            print(f"[{exchange_name}] Connecting... (attempt {reconnect_count + 1})")

            # 为Bitget禁用内置ping机制，因为它使用自定义ping/pong
            ping_interval = None if exchange_name == 'Bitget' else 20
            ping_timeout = None if exchange_name == 'Bitget' else 10

            async with websockets.connect(
                ws_config['uri'],
                ping_interval=ping_interval,
                ping_timeout=ping_timeout,
                close_timeout=10
            ) as ws:
                # 发送订阅消息
                if 'subscribe_msg' in ws_config:
                    await ws.send(json.dumps(ws_config['subscribe_msg']))

                # 启动自定义keepalive任务（仅对Bitget）
                if 'keepalive' in ws_config:
                    keepalive_task = asyncio.create_task(ws_config['keepalive'](ws))

                print(f"[{exchange_name}] Connected successfully")
                reconnect_count = 0

                while True:
                    try:
                        msg = await asyncio.wait_for(ws.recv(), timeout=30)

                        # 处理Bitget的ping/pong消息
                        if exchange_name == 'Bitget' and msg == "pong":
                            continue

                        data = json.loads(msg)

                        # 使用解析函数处理数据
                        parsed = ws_config['parser'](data)
                        if parsed:
                            symbol, bid, ask = parsed
                            async with state.lock:
                                if symbol in state.shared_data:
                                    state.shared_data[symbol][exchange_name]["bid"] = bid
                                    state.shared_data[symbol][exchange_name]["ask"] = ask

                    except asyncio.TimeoutError:
                        print(f"[{exchange_name}] No data received for 30s, checking connection...")
                        if exchange_name != 'Bitget':  # 其他交易所使用标准ping
                            await ws.ping()
                        continue
                    except json.JSONDecodeError as e:
                        print(f"[{exchange_name}] JSON decode error: {e}")
                        continue

        except (ConnectionClosedError, ConnectionClosedOK) as e:
            print(f"[{exchange_name}] Connection closed: {e}")
        except Exception as e:
            print(f"[{exchange_name}] Error: {e}")
        finally:
            # 清理keepalive任务
            if keepalive_task and not keepalive_task.done():
                keepalive_task.cancel()
                try:
                    await keepalive_task
                except asyncio.CancelledError:
                    pass

        reconnect_count += 1
        if reconnect_count < Config.MAX_RECONNECT_ATTEMPTS:
            print(f"[{exchange_name}] Reconnecting in {Config.RECONNECT_DELAY}s...")
            await asyncio.sleep(Config.RECONNECT_DELAY)
        else:
            print(f"[{exchange_name}] Max reconnection attempts reached")
            break

# 各交易所的解析器
def parse_binance(data):
    if 'data' in data:
        payload = data['data']
        symbol = payload.get('s')
        bid = payload.get('b')
        ask = payload.get('a')
        if symbol and bid and ask:
            return symbol, float(bid), float(ask)
    return None

def parse_bybit(data):
    if 'data' in data and data.get('topic', '').startswith('tickers.'):
        payload = data['data']
        symbol = payload.get('symbol')
        bid = payload.get('bid1Price')
        ask = payload.get('ask1Price')
        if symbol and bid and ask:
            return symbol, float(bid), float(ask)
    return None

def parse_bitget(data):
    """解析Bitget WebSocket数据"""
    if 'data' in data and len(data['data']) > 0:
        item = data['data'][0]
        bid = item.get('bidPr')
        ask = item.get('askPr')
        symbol = item.get('instId')
        if bid and ask and symbol:
            try:
                return symbol, float(bid), float(ask)
            except (ValueError, TypeError):
                return None
    return None

def parse_okx(data):
    if 'data' in data and len(data['data']) > 0:
        item = data['data'][0]
        bid = item.get('bidPx')
        ask = item.get('askPx')
        symbol = item.get('instId', '').replace('-SWAP','').replace('-','')
        if bid and ask and symbol:
            return symbol, float(bid), float(ask)
    return None



# 各交易所连接函数
async def binance_ws(state:TradingState):
    params = [f"{s.lower()}@bookTicker" for s in state.symbols]
    uri = f"wss://fstream.binance.com/stream?streams=" + "/".join(params)
    config = {
        'uri': uri,
        'parser': parse_binance
    }
    await generic_ws_connector('Binance', config, state)

async def bybit_ws(state:TradingState):
    config = {
        'uri': "wss://stream.bybit.com/v5/public/linear",
        'subscribe_msg': {
            "op": "subscribe",
            "args": [f"tickers.{symbol}" for symbol in state.symbols]
        },
        'parser': parse_bybit
    }
    await generic_ws_connector('Bybit', config, state)

async def bitget_keepalive(ws):
    """Bitget自定义keepalive函数"""
    while True:
        await asyncio.sleep(25)  # ping every 25 seconds
        try:
            await ws.send("ping")
        except:
            break  # stop if connection closed

async def bitget_ws(state:TradingState):
    """Bitget WebSocket连接，现在使用generic_ws_connector"""
    config = {
        'uri': "wss://ws.bitget.com/v2/ws/public",
        'subscribe_msg': {
            "op": "subscribe",
            "args": [
                {
                    "instType": "USDT-FUTURES",
                    "channel": "ticker",
                    "instId": sym
                } for sym in state.symbols
            ]
        },
        'parser': parse_bitget,
        'keepalive': bitget_keepalive
    }
    await generic_ws_connector('Bitget', config, state)


async def okx_ws(state:TradingState):
    config = {
        'uri': "wss://ws.okx.com:8443/ws/v5/public",
        'subscribe_msg': {
            "op": "subscribe",
            "args": [{
                "channel": "tickers",
                "instId": symbol
            } for symbol in state.okx_symbols]
        },
        'parser': parse_okx
    }
    await generic_ws_connector('Okx', config, state)

async def save_market_data(state:TradingState):
    while True:
        try:
            state.redis_client.set("trading:exchange_data", json.dumps(state.shared_data))

        except Exception as e:
            print(f"Error updating Redis: {e}")

        await asyncio.sleep(0.01)
# 显示函数 (传入state参数)
def display_exchange_data(state: TradingState):
    print("+------------+-----------+-------------+-------------+")
    print("|   Symbol   | Exchange  | Bid Price   |  Ask Price  |")
    print("+------------+-----------+-------------+-------------+")
    for symbols, orderbooks in state.shared_data.items():
        for exchange, book in orderbooks.items():
            
            bid = book['bid'] if book['bid'] else "..."
            ask = book['ask'] if book['ask'] else "..."

            print(f"| {symbols:<10} | {exchange:<9} | {str(bid):<11} | {str(ask):<11} |")
    print("+------------+-----------+-------------+-------------+")        
async def display_terminal(state: TradingState):
    while True:
        display_exchange_data(state)
        print(f"Last update: {time.strftime('%Y-%m-%d %H:%M:%S')}")

        await asyncio.sleep(1)        
async def main():
    # 初始化

    print(f"Monitoring {len(state.symbols)} currencies")
    
    # 启动所有任务
    tasks = [
        binance_ws(state),
        bitget_ws(state),
        okx_ws(state),
        bybit_ws(state),
        # display_terminal(state),
        save_market_data(state),
    ]
    
    await asyncio.gather(*tasks)

if __name__ == '__main__':  
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n程序已手动终止")