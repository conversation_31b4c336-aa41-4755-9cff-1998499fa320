#!/usr/bin/env python3
"""
安装项目依赖的脚本
"""

import subprocess
import sys
import os

def install_package(package):
    """安装单个包"""
    try:
        print(f"正在安装 {package}...")
        result = subprocess.run([sys.executable, '-m', 'pip', 'install', package], 
                              capture_output=True, text=True, check=True)
        print(f"✅ {package} 安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {package} 安装失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False

def main():
    """主函数"""
    print("=== 项目依赖安装脚本 ===\n")
    
    # 检查requirements.txt是否存在
    if not os.path.exists('requirements.txt'):
        print("❌ 未找到 requirements.txt 文件")
        return
    
    # 读取requirements.txt
    with open('requirements.txt', 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # 解析包名
    packages = []
    for line in lines:
        line = line.strip()
        if line and not line.startswith('#'):
            packages.append(line)
    
    print(f"找到 {len(packages)} 个依赖包:")
    for pkg in packages:
        print(f"  - {pkg}")
    print()
    
    # 安装包
    success_count = 0
    for package in packages:
        if install_package(package):
            success_count += 1
        print()
    
    # 总结
    print("=== 安装总结 ===")
    print(f"成功安装: {success_count}/{len(packages)} 个包")
    
    if success_count == len(packages):
        print("🎉 所有依赖安装成功！")
        print("\n现在可以运行以下命令测试:")
        print("python test_exchanges.py")
    else:
        print("⚠️ 部分依赖安装失败，请检查错误信息")
        print("\n你也可以手动安装:")
        print("pip install -r requirements.txt")

if __name__ == "__main__":
    main()
