#!/usr/bin/env python3
"""
data_service.py  (方案 B：微批直写版)
------------------------------------------------
与“队列+消费者”版本相比的改动：
  - 去掉全局队列与独立消费者
  - 在每个 ws_connector 内，用 pending_updates 做局部缓存
  - 条件：数量 >= MICRO_BATCH_SIZE 或 间隔 >= MICRO_FLUSH_INTERVAL 秒 时批量 flush
  - 批量时获得 state.lock，统一写 shared_data + 调用 realtime_data_manager.update_price
  - 减少锁竞争，逻辑仍简单
保留特性：
  - 订阅 ack 确认
  - JSON / native ping 心跳
  - inactivity watchdog
  - 指数退避重连
  - parser 统一返回单个或 list[QuoteUpdate]
适用场景：
  - 中等频率（几千 ~ 数万 quotes/s）且希望代码较简单
  - 不需要跨交易所统一排序的严格时间序列处理
若后续吞吐继续升高或需要统一缓冲，可再切回“队列+消费者”架构。
"""

from __future__ import annotations

import asyncio
import json
import logging
import random
import signal
import time
from dataclasses import dataclass
from typing import Any, Dict, List, Optional, Callable

import websockets
from websockets import WebSocketClientProtocol

# ===================== 可调参数 =====================
MICRO_BATCH_SIZE = 200          # 达到多少条就 flush
MICRO_FLUSH_INTERVAL = 0.05     # 50ms 定时 flush
ACK_WAIT_SECONDS = 6            # 订阅 ack 等待时间
MAX_RECONNECT_EXP = 6           # 2^6=64，最大指数阶次
MAX_RECONNECT_DELAY = 60        # 最大退避秒
# ===================================================

# ----------------------------------------------------
# TradingState / realtime_data_manager 兼容
# ----------------------------------------------------
try:
    from trading_state import TradingState
except ImportError:
    class TradingState:
        def __init__(self):
            self.symbols = []
            self.shared_data: Dict[str, Dict[str, Dict[str, float]]] = {}
            self.lock = asyncio.Lock()

        def init_symbols(self):
            if not self.symbols:
                # 示例 symbol，可替换
                self.symbols = ["BTCUSDT"]
                for s in self.symbols:
                    self.shared_data[s] = {
                        "Binance": {"bid": 0.0, "ask": 0.0},
                        "Bybit": {"bid": 0.0, "ask": 0.0},
                        "OKX": {"bid": 0.0, "ask": 0.0},
                        "Bitget": {"bid": 0.0, "ask": 0.0},
                    }

try:
    from realtime_data_manager import realtime_data_manager
except ImportError:
    class DummyRDM:
        def update_price(self, symbol, exchange, bid, ask):
            pass
    realtime_data_manager = DummyRDM()

# ----------------------------------------------------
# 日志
# ----------------------------------------------------
LOG_FORMAT = "%(asctime)s | %(levelname)-7s | %(message)s"
logging.basicConfig(
    level=logging.INFO,
    format=LOG_FORMAT,
    datefmt="%H:%M:%S"
)
logger = logging.getLogger("data_service")

# ----------------------------------------------------
# 数据结构
# ----------------------------------------------------
@dataclass
class QuoteUpdate:
    symbol: str
    bid: float
    ask: float
    ts_exchange: Optional[int] = None

# ----------------------------------------------------
# 指标（精简版）
# ----------------------------------------------------
METRICS = {
    "ws_connected": 0,
    "ws_reconnects": 0,
    "ws_subscribe_ok": 0,
    "ws_subscribe_fail": 0,
    "msg_frames": 0,
    "parser_errors": 0,
    "quotes_buffered": 0,       # 进入 pending 的报价数量（累计）
    "batch_flush": 0,
    "batch_updates": 0,
    "inactivity_reconnects": 0
}

# ----------------------------------------------------
# 订阅 & 解析工具
# ----------------------------------------------------
def build_okx_inst(symbol: str) -> str:
    s = symbol.upper().replace("_", "").replace("-", "")
    if s.endswith("USDT"):
        base = s[:-4]
        return f"{base}-USDT-SWAP"
    return symbol.upper()

def ack_handler_okx(exchange_name: str, msg: dict, pending: set, metrics: dict):
    if msg.get("event") == "subscribe":
        arg = msg.get("arg", {})
        instId = arg.get("instId", "")
        if instId:
            norm = instId.replace("-", "").replace("SWAP", "").upper()
            if norm in pending:
                pending.discard(norm)
                metrics["ws_subscribe_ok"] += 1
    elif msg.get("event") == "error":
        metrics["ws_subscribe_fail"] += 1

def ack_handler_bybit(exchange_name: str, msg: dict, pending: set, metrics: dict):
    if msg.get("op") == "subscribe" and msg.get("success"):
        for a in msg.get("args", []):
            sym = a.split(".")[-1].upper()
            if sym in pending:
                pending.discard(sym)
                metrics["ws_subscribe_ok"] += 1
    elif msg.get("op") == "error":
        metrics["ws_subscribe_fail"] += 1

def ack_handler_bitget(exchange_name: str, msg: dict, pending: set, metrics: dict):
    if msg.get("event") == "subscribe":
        arg = msg.get("arg", {})
        instId = arg.get("instId")
        if instId:
            norm = instId.replace("-", "").upper()
            if norm in pending:
                pending.discard(norm)
                metrics["ws_subscribe_ok"] += 1
    elif msg.get("event") == "error":
        metrics["ws_subscribe_fail"] += 1

def ack_handler_binance(exchange_name: str, msg: dict, pending: set, metrics: dict):
    # Combined stream - treat all as success at once
    if pending:
        metrics["ws_subscribe_ok"] += len(pending)
        pending.clear()

def parser_binance(msg: dict):
    data = msg.get("data")
    if not data or "s" not in data:
        return None
    try:
        return QuoteUpdate(
            symbol=data["s"].upper(),
            bid=float(data["b"]),
            ask=float(data["a"]),
            ts_exchange=int(data.get("E") or 0)
        )
    except Exception:
        return None

def parser_bybit(msg: dict):
    topic = msg.get("topic")
    if not topic or not topic.startswith("tickers."):
        return None
    d = msg.get("data")
    if not d:
        return None
    try:
        sym = (d.get("symbol") or topic.split(".", 1)[1]).upper()
        return QuoteUpdate(
            symbol=sym,
            bid=float(d.get("bid1Price") or 0),
            ask=float(d.get("ask1Price") or 0),
            ts_exchange=int(d.get("ts") or d.get("timestamp") or 0)
        )
    except Exception:
        return None

def parser_okx(msg: dict):
    if msg.get("arg", {}).get("channel") != "tickers":
        return None
    out = []
    for d in msg.get("data", []):
        inst = d.get("instId")
        if not inst:
            continue
        norm = inst.replace("-", "").replace("SWAP", "").upper()
        try:
            out.append(QuoteUpdate(
                symbol=norm,
                bid=float(d.get("bidPx") or 0),
                ask=float(d.get("askPx") or 0),
                ts_exchange=int(msg.get("ts") or 0)
            ))
        except Exception:
            continue
    return out or None

def parser_bitget(msg: dict):
    if msg.get("arg", {}).get("channel") != "ticker":
        return None
    out = []
    for d in msg.get("data", []):
        inst = d.get("instId")
        if not inst:
            continue
        norm = inst.replace("-", "").upper()
        try:
            out.append(QuoteUpdate(
                symbol=norm,
                bid=float(d.get("bidPr") or d.get("bestBid") or 0),
                ask=float(d.get("askPr") or d.get("bestAsk") or 0),
                ts_exchange=int(d.get("ts") or d.get("systemTime") or 0)
            ))
        except Exception:
            continue
    return out or None

EXCHANGES: Dict[str, Dict[str, Any]] = {
    "Binance": {
        "name": "Binance",
        "build_uri": lambda symbols: "wss://fstream.binance.com/stream?streams=" + "/".join(
            f"{s.lower()}@bookTicker" for s in symbols
        ),
        "build_subscribe": None,
        "parser": parser_binance,
        "need_ack": False,
        "ack_handler": ack_handler_binance,
        "heartbeat": "native",
        "keepalive_interval": 25,
        "message_timeout": 90
    },
    "Bybit": {
        "name": "Bybit",
        "build_uri": lambda symbols: "wss://stream.bybit.com/v5/public/linear",
        "build_subscribe": lambda symbols: {
            "op": "subscribe",
            "args": [f"tickers.{s.upper()}" for s in symbols]
        },
        "parser": parser_bybit,
        "need_ack": True,
        "ack_handler": ack_handler_bybit,
        "heartbeat": "json",
        "keepalive_interval": 15,
        "message_timeout": 60
    },
    "OKX": {
        "name": "OKX",
        "build_uri": lambda symbols: "wss://ws.okx.com:8443/ws/v5/public",
        "build_subscribe": lambda symbols: {
            "op": "subscribe",
            "args": [{"channel": "tickers", "instId": build_okx_inst(s)} for s in symbols]
        },
        "parser": parser_okx,
        "need_ack": True,
        "ack_handler": ack_handler_okx,
        "heartbeat": "json",
        "keepalive_interval": 15,
        "message_timeout": 45
    },
    "Bitget": {
        "name": "Bitget",
        "build_uri": lambda symbols: "wss://ws.bitget.com/v2/ws/public",
        "build_subscribe": lambda symbols: {
            "op": "subscribe",
            "args": [
                {"instType": "USDT-FUTURES", "channel": "ticker", "instId": s.upper()}
                for s in symbols
            ]
        },
        "parser": parser_bitget,
        "need_ack": True,
        "ack_handler": ack_handler_bitget,
        "heartbeat": "json",
        "keepalive_interval": 15,
        "message_timeout": 45
    },
}

# ----------------------------------------------------
# 控制消息判定与心跳
# ----------------------------------------------------
def _is_control_msg(exchange: str, msg: dict) -> bool:
    if exchange == "Bitget" and (msg.get("op") in ("ping", "pong") or msg.get("event") in ("error", "subscribe")):
        return True
    if exchange == "Bybit" and msg.get("op") in ("ping", "pong", "subscribe", "error"):
        return True
    if exchange == "OKX" and (msg.get("op") in ("ping", "pong") or msg.get("event") in ("error", "subscribe")):
        return True
    if exchange == "Binance":
        return False
    return False

def _looks_like_ack(exchange: str, msg: dict) -> bool:
    if exchange == "OKX" and msg.get("event") in ("subscribe", "error"):
        return True
    if exchange == "Bybit" and msg.get("op") in ("subscribe", "error"):
        return True
    if exchange == "Bitget" and msg.get("event") in ("subscribe", "error"):
        return True
    return False

async def _send_heartbeat(exchange: str, ws: WebSocketClientProtocol, mode: Optional[str]):
    if ws.closed:
        return
    try:
        if mode == "json":
            await ws.send(json.dumps({"op": "ping"}))
        elif mode == "native":
            await ws.ping()
    except Exception:
        pass

def _handle_control(exchange: str, msg: dict, ws: WebSocketClientProtocol):
    if exchange in ("Bitget", "Bybit", "OKX"):
        if msg.get("op") == "ping":
            try:
                asyncio.create_task(ws.send(json.dumps({"op": "pong"})))
            except Exception:
                pass
        if msg.get("event") == "error" or msg.get("op") == "error":
            logger.error(f"[{exchange}] ctrl error: {msg}")

# ----------------------------------------------------
# 解析与缓冲
# ----------------------------------------------------
def _parse_message(exchange: str, msg: dict, parser: Callable) -> List[QuoteUpdate]:
    try:
        parsed = parser(msg)
    except Exception as e:
        METRICS["parser_errors"] += 1
        logger.debug(f"[{exchange}] parser exception: {e} raw={msg}")
        return []
    if not parsed:
        return []
    if isinstance(parsed, QuoteUpdate):
        parsed = [parsed]
    out: List[QuoteUpdate] = []
    for q in parsed:
        if q.bid > 0 and q.ask > 0:
            out.append(q)
    return out

async def _flush_pending(state: TradingState, exchange_name: str, pending: List[QuoteUpdate]):
    """
    批量写入 shared_data + realtime_data_manager
    """
    if not pending:
        return
    METRICS["batch_flush"] += 1
    updates = 0
    async with state.lock:
        for q in pending:
            sym = q.symbol
            if sym not in state.shared_data:
                state.shared_data[sym] = {}
            if exchange_name not in state.shared_data[sym]:
                state.shared_data[sym][exchange_name] = {"bid": 0.0, "ask": 0.0}
            state.shared_data[sym][exchange_name]["bid"] = q.bid
            state.shared_data[sym][exchange_name]["ask"] = q.ask
            try:
                realtime_data_manager.update_price(sym, exchange_name, q.bid, q.ask)
            except Exception:
                pass
            updates += 1
    METRICS["batch_updates"] += updates

# ----------------------------------------------------
# WebSocket 连接器（微批版）
# ----------------------------------------------------
async def ws_connector(exchange_name: str, symbols: List[str], state: TradingState):
    cfg = EXCHANGES[exchange_name]
    uri = cfg["build_uri"](symbols)
    build_subscribe = cfg["build_subscribe"]
    parser = cfg["parser"]
    ack_handler = cfg["ack_handler"]
    need_ack = cfg["need_ack"]
    heartbeat_mode = cfg["heartbeat"]
    keepalive_interval = cfg["keepalive_interval"]
    message_timeout = cfg["message_timeout"]

    reconnect_attempt = 0

    while True:
        ws: Optional[WebSocketClientProtocol] = None
        pending_updates: List[QuoteUpdate] = []
        last_flush = time.time()

          # 退避
        try:
            METRICS["ws_reconnects"] += 1
            reconnect_attempt += 1
            logger.info(f"[{exchange_name}] connecting (attempt={reconnect_attempt}) {uri}")

            ws = await websockets.connect(
                uri,
                ping_interval=None,
                ping_timeout=None,
                close_timeout=10,
                compression=None
            )
            METRICS["ws_connected"] += 1
            logger.info(f"[{exchange_name}] connected.")

            # 订阅
            if build_subscribe:
                sub_msg = build_subscribe(symbols)
                if isinstance(sub_msg, list):
                    for sm in sub_msg:
                        await ws.send(json.dumps(sm))
                        logger.debug(f"[{exchange_name}] -> SUB {sm}")
                else:
                    await ws.send(json.dumps(sub_msg))
                    logger.debug(f"[{exchange_name}] -> SUB {sub_msg}")
            else:
                logger.info(f"[{exchange_name}] combined stream (no explicit subscribe).")

            # ack 阶段
            pending_set = set(s.upper() for s in symbols)
            prebuffer: List[dict] = []
            if need_ack:
                ack_deadline = time.time() + ACK_WAIT_SECONDS
                while pending_set and time.time() < ack_deadline:
                    try:
                        raw = await asyncio.wait_for(ws.recv(), timeout=ack_deadline - time.time())
                    except asyncio.TimeoutError:
                        break
                    if isinstance(raw, bytes):
                        raw = raw.decode()
                    try:
                        msg = json.loads(raw)
                    except Exception:
                        continue
                    if _is_control_msg(exchange_name, msg):
                        _handle_control(exchange_name, msg, ws)
                        continue
                    ack_handler(exchange_name, msg, pending_set, METRICS)
                    if not _looks_like_ack(exchange_name, msg):
                        prebuffer.append(msg)
                if pending_set:
                    logger.warning(f"[{exchange_name}] subscription pending not confirmed: {pending_set}")
            else:
                METRICS["ws_subscribe_ok"] += len(pending_set)
                pending_set.clear()

            reconnect_attempt = 0  # 成功后重置
            last_msg_time = time.time()

            # 处理 ack 期间缓存的行情
            for pb in prebuffer:
                quotes = _parse_message(exchange_name, pb, parser)
                if quotes:
                    pending_updates.extend(quotes)
                    METRICS["quotes_buffered"] += len(quotes)

            # 初始 flush （若已积累）
            if pending_updates:
                await _flush_pending(state, exchange_name, pending_updates)
                pending_updates.clear()
                last_flush = time.time()

            # 主 recv 循环
            while True:
                try:
                    raw = await asyncio.wait_for(ws.recv(), timeout=keepalive_interval)
                    if isinstance(raw, bytes):
                        raw = raw.decode()
                except asyncio.TimeoutError:
                    await _send_heartbeat(exchange_name, ws, heartbeat_mode)
                else:
                    last_msg_time = time.time()
                    METRICS["msg_frames"] += 1
                    try:
                        msg = json.loads(raw)
                    except Exception:
                        continue

                    if _is_control_msg(exchange_name, msg):
                        _handle_control(exchange_name, msg, ws)
                        continue

                    quotes = _parse_message(exchange_name, msg, parser)
                    if quotes:
                        pending_updates.extend(quotes)
                        METRICS["quotes_buffered"] += len(quotes)

                # 决定是否 flush
                now = time.time()
                if pending_updates and (
                    len(pending_updates) >= MICRO_BATCH_SIZE or
                    (now - last_flush) >= MICRO_FLUSH_INTERVAL
                ):
                    await _flush_pending(state, exchange_name, pending_updates)
                    pending_updates.clear()
                    last_flush = now

                # inactivity watchdog
                if time.time() - last_msg_time > message_timeout:
                    METRICS["inactivity_reconnects"] += 1
                    logger.warning(f"[{exchange_name}] inactivity {time.time()-last_msg_time:.1f}s > {message_timeout}s -> reconnect")
                    break

            # 循环结束前 flush 剩余
            if pending_updates:
                await _flush_pending(state, exchange_name, pending_updates)
                pending_updates.clear()

        except asyncio.CancelledError:
            raise
        except Exception as e:
            logger.error(f"[{exchange_name}] loop error: {e}", exc_info=False)
        finally:
            if ws:
                try:
                    await ws.close()
                except Exception:
                    pass
            # flush any leftover (异常情况下)
            if pending_updates:
                try:
                    await _flush_pending(state, exchange_name, pending_updates)
                except Exception:
                    pass
                pending_updates.clear()

            # 指数退避 + jitter
            delay = min(2 ** min(reconnect_attempt, MAX_RECONNECT_EXP), MAX_RECONNECT_DELAY)
            jitter = random.uniform(0, delay * 0.3)
            await asyncio.sleep(delay + jitter)

# ----------------------------------------------------
# 指标输出
# ----------------------------------------------------
async def metrics_reporter(interval: int = 10):
    while True:
        await asyncio.sleep(interval)
        logger.info(
            "[STATS] connected=%d reconnects=%d sub_ok=%d sub_fail=%d frames=%d buffered=%d batch_flush=%d batch_updates=%d parser_err=%d inactivity=%d",
            METRICS["ws_connected"],
            METRICS["ws_reconnects"],
            METRICS["ws_subscribe_ok"],
            METRICS["ws_subscribe_fail"],
            METRICS["msg_frames"],
            METRICS["quotes_buffered"],
            METRICS["batch_flush"],
            METRICS["batch_updates"],
            METRICS["parser_errors"],
            METRICS["inactivity_reconnects"]
        )

# ----------------------------------------------------
# 启动入口
# ----------------------------------------------------
async def start_data_service(exchange_symbols_map: Dict[str, List[str]], state: Optional[TradingState] = None):
    if state is None:
        state = TradingState()
        state.init_symbols()

    # 初始化 shared_data 对应结构
    for ex, syms in exchange_symbols_map.items():
        for s in syms:
            su = s.upper()
            if su not in state.shared_data:
                state.shared_data[su] = {}
            if ex not in state.shared_data[su]:
                state.shared_data[su][ex] = {"bid": 0.0, "ask": 0.0}

    tasks = []
    for ex, syms in exchange_symbols_map.items():
        if ex not in EXCHANGES:
            logger.warning(f"Exchange {ex} not supported, skip.")
            continue
        if not syms:
            continue
        tasks.append(asyncio.create_task(ws_connector(ex, [s.upper() for s in syms], state), name=f"ws-{ex}"))

    tasks.append(asyncio.create_task(metrics_reporter(), name="metrics"))
    await asyncio.gather(*tasks)

def _build_default_symbol_map() -> Dict[str, List[str]]:
    return {
        "Binance": ["BTCUSDT"],
        "Bybit": ["BTCUSDT"],
        "OKX": ["BTCUSDT"],
        "Bitget": ["BTCUSDT"],
    }

def main():
    state = TradingState()
    state.init_symbols()
    symbol_map = _build_default_symbol_map()

    loop = asyncio.get_event_loop()

    main_task = loop.create_task(start_data_service(symbol_map, state))

    def _graceful_shutdown():
        logger.info("Termination signal received, cancelling tasks ...")
        for t in asyncio.all_tasks(loop):
            t.cancel()

    for sig in (signal.SIGINT, signal.SIGTERM):
        try:
            loop.add_signal_handler(sig, _graceful_shutdown)
        except NotImplementedError:
            pass

    try:
        loop.run_until_complete(main_task)
    except KeyboardInterrupt:
        logger.info("Interrupted by user.")
    finally:
        pending = asyncio.all_tasks(loop)
        for p in pending:
            p.cancel()
        loop.run_until_complete(asyncio.gather(*pending, return_exceptions=True))
        loop.close()

if __name__ == "__main__":
    main()