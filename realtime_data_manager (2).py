#!/usr/bin/env python3
"""
实时数据管理器 - 为套利交易提供零延迟的市场数据 (稳定版)
"""

import time
import copy
import threading
from typing import Dict, Optional, Tuple
import json
import redis
from logging_setup import setup_loggers
import logging
# 假设 config_copy.py 文件中包含一个名为 Config 的类，且该类有 MIN_SPREAD_PCT_THRESHOLD 属性
from config_copy import Config


redis_client = redis.Redis(host = 'localhost', port = 6379, db=0, decode_responses=True)

# 用于独立执行的虚拟Config类
# class Config:
#     MIN_SPREAD_PCT_THRESHOLD = 0.001 # 示例值: 0.1%

# 设置日志记录器
# setup_loggers()
data_logger = logging.getLogger('data_logger')
logging.basicConfig(level=logging.INFO) # 为日志记录提供基础配置


class RealtimeDataManager:
    """
    实时数据管理器 - 提供零延迟的市场数据访问
    此版本经过重构，以提高稳定性和线程安全性。
    """
    
    def __init__(self):
        """
        初始化数据管理器，确保关键数据结构被预先创建。
        """
        # 使用可重入锁（RLock）来防止并发更新时可能出现的竞争条件。
        self.lock = threading.RLock()
        
        # shared_data 字典现在初始化时会包含一个默认的 'best_spread' 结构，
        # 这样可以防止在第一次访问时出现 KeyError。
        self.shared_data: Dict = {
            "best_spread": self._get_default_spread_structure()
        }
        self.last_update_time: Dict[str, float] = {}  # 存储每个 "币种-交易所" 对的最后更新时间戳
        self.data_freshness_threshold: float = 5.0  # 数据超过这个时间（秒）被认为是陈旧的
        self.spread_time = {} # 价差维持时间
        
        # Redis连接，用于数据持久化和监控
        try:
            self.redis_client = redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)
            self.redis_client.ping() # 检查连接状态
            self.redis_connected = True
            data_logger.info("成功连接到 Redis。")
        except redis.ConnectionError:
            self.redis_connected = False
            data_logger.warning("Redis 连接失败。管理器将只使用内存数据。")

    def _get_default_spread_structure(self, symbol: Optional[str] = None) -> Dict:
        """返回价差信息的默认字典结构。"""
        return {"symbol": symbol, "spread": None, "buy_exchange": None, "sell_exchange": None, "buy_price": None, "sell_price": None}

    def calculate_spread(self, symbol: str, exchange_name_1: str, exchange_name_2: str) -> Optional[Dict]:
        """
        安全地计算给定币种在两个交易所之间的价差。
        使用 .get() 方法来防止因数据缺失而导致的 KeyError。
        """
        with self.lock:
            symbol_data = self.shared_data.get(symbol, {})
            data1 = symbol_data.get(exchange_name_1)
            data2 = symbol_data.get(exchange_name_2)

            # 如果任一交易所数据不存在，则无法计算
            if not data1 or not data2:
                return None

            ask1 = data1.get("ask")
            bid1 = data1.get("bid")
            ask2 = data2.get("ask")
            bid2 = data2.get("bid")

            # 在计算前，确保所有的价格都存在且不为 None
            if any(p is None for p in [ask1, bid1, ask2, bid2]):
                return None

            # 套利机会：在交易所1买入，在交易所2卖出
            if bid2 > ask1:
                spread = 2 * (bid2 - ask1) / (bid2 + ask1)
                return {"spread": spread, "buy_exchange": exchange_name_1, "sell_exchange": exchange_name_2, "buy_price": ask1, "sell_price": bid2}
            
            # 套利机会：在交易所2买入，在交易所1卖出
            elif bid1 > ask2:
                spread = 2 * (bid1 - ask2) / (bid1 + ask2)
                return {"spread": spread, "buy_exchange": exchange_name_2, "sell_exchange": exchange_name_1, "buy_price": ask2, "sell_price": bid1}
            
            return None

    def update_price(self, symbol: str, exchange: str, bid: float, ask: float, ts_flush: float, ts_exchange: float) -> None:
        """
        从 WebSocket 流更新价格数据。这是新数据进入系统的主要入口。
        此逻辑经过重构，以保证健壮性并避免竞争条件。
        """
        redis_client.set("trading:"+symbol+"@"+exchange+"[bid]", bid)
        redis_client.set("trading:"+symbol+"@"+exchange+"[ask]", ask)
        with self.lock:
            # --- 1. 使用 setdefault 安全地初始化数据结构 ---
            # setdefault 是一个原子操作，它会获取一个键的值，如果键不存在，则设置一个默认值。
            symbol_data = self.shared_data.setdefault(symbol, {})
            symbol_data.setdefault(exchange, {"bid": None, "ask": None})
            symbol_data.setdefault("spreads", {
                "Bybit-Bitget": {"exchanges": ["Bybit", "Bitget"], "spread_info": None},
                "Bybit-Okx": {"exchanges": ["Bybit", "Okx"], "spread_info": None},
                "Bybit-Binance": {"exchanges": ["Bybit", "Binance"], "spread_info": None},
                "Bitget-Okx": {"exchanges": ["Bitget", "Okx"], "spread_info": None},
                "Bitget-Binance": {"exchanges": ["Bitget", "Binance"], "spread_info": None},
                "Okx-Binance": {"exchanges": ["Okx", "Binance"], "spread_info": None},
            })
            symbol_data.setdefault("best_spread", self._get_default_spread_structure(symbol))

            # --- 2. 更新当前币种/交易所的价格 ---
            symbol_data[exchange]["bid"] = bid
            symbol_data[exchange]["ask"] = ask
            self.last_update_time[f"{symbol}_{exchange}"] = time.time()

            # --- 3. 重新计算价差并找到当前币种的最佳价差 ---
            current_symbol_best_spread = self._get_default_spread_structure(symbol)

            for spread_name, spread_config in symbol_data["spreads"].items():
                exchanges = spread_config["exchanges"]
                # 只有当被更新的交易所是交易对的一部分时，才重新计算
                if exchange in exchanges:
                    spread_info = self.calculate_spread(symbol, exchanges[0], exchanges[1])
                    spread_config["spread_info"] = spread_info
                
                # 检查当前价差是否是该币种的最新最佳价差
                current_spread = spread_config["spread_info"]
                if current_spread and current_spread.get("spread", 0) > (current_symbol_best_spread.get("spread") or 0):
                    current_symbol_best_spread = current_spread

            # 更新该币种的最佳价差
            symbol_data["best_spread"] = current_symbol_best_spread
            if current_symbol_best_spread.get("spread"):
                 symbol_data["best_spread"]["symbol"] = symbol

            # if symbol_data["best_spread"].get("spread") >= Config.MIN_SPREAD_PCT_THRESHOLD:
            #     now_ts = time.time()
            #     if self.spread_time[symbol] is None:
            #         self.spread_time[symbol] = now_ts
            #     else:
            #         if self.spread_time[symbol] - now_ts >= Config.TIME_THRESHOLD:
            #             self.spread_time[symbol] = now_ts
            #             self.shared_data["best_spread"] = symbol_data["best_spread"]
            #             self.shared_data["best_spread"]["last_updated"] = now_ts
            #             self.shared_data["best_spread"]["ts_flush"] = ts_flush
            #             self.shared_data["best_spread"]["ts_exchange"] = ts_exchange
            #             redis_client.set("trading:best_spread", json.dumps(self.shared_data["best_spread"]))
            #         else:
            #             # self.spread_time[symbol] = None
            #             self.reset_best_spread()
            #             redis_client.set("trading:best_spread", json.dumps(self.shared_data["best_spread"]))
            # else:
            #     self.spread_time[symbol] = None
            #     self.reset_best_spread()
            #     redis_client.set("trading:best_spread", json.dumps(self.shared_data["best_spread"]))


            # --- 4. 更新全局最佳价差 ---
            global_best_spread = self.shared_data["best_spread"]
            
            # 如果被更新的币种正好是全局最优价差的持有者，其价差值可能已经下降。
            # 我们必须重置全局最优价差，让它与其他所有币种的最优价差重新比较。
            # 这里我们简化处理，只与当前币种的新最优价差进行比较。
            if global_best_spread and global_best_spread.get("symbol") == symbol:
                self.reset_best_spread()
                global_best_spread = self.shared_data["best_spread"]

            # 检查当前币种的新最优价差是否优于全局最优价差
            new_symbol_spread_val = symbol_data["best_spread"].get("spread")
            global_spread_val = global_best_spread.get("spread")

            if new_symbol_spread_val is not None and (global_spread_val is None or new_symbol_spread_val > global_spread_val):
                # if new_symbol_spread_val >= Config.MIN_SPREAD_PCT_THRESHOLD:
                self.shared_data["best_spread"] = symbol_data["best_spread"]
                self.shared_data["best_spread"]["last_updated"] = time.time()
                self.shared_data["best_spread"]["ts_flush"] = ts_flush
                self.shared_data["best_spread"]["ts_exchange"] = ts_exchange
                redis_client.set("trading:best_spread", json.dumps(self.shared_data["best_spread"]))
                # print(f'发现新的最优价差: {self.shared_data["best_spread"]}')
    
    def get_realtime_data(self) -> Dict:
        """获取整个实时市场数据结构的深拷贝。"""
        with self.lock:
            return copy.deepcopy(self.shared_data)
        
    def get_best_spread(self) -> Dict:
        """获取当前所有币种中发现的最佳价差。"""
        # with self.lock:
        #     return copy.deepcopy(self.shared_data["best_spread"])
        return json.loads(redis_client.get("trading:best_spread"))
    
    def reset_best_spread(self) -> None:
        """将最佳价差重置为其默认的空状态。"""
        with self.lock:
            self.shared_data["best_spread"] = self._get_default_spread_structure()
    
    def get_symbol_data(self, symbol: str) -> Optional[Dict]:
        """获取特定币种的所有实时数据。"""
        with self.lock:
            return copy.deepcopy(self.shared_data.get(symbol))
    
    def get_exchange_price(self, symbol: str, exchange: str) -> Tuple[Optional[float], Optional[float]]:
        """获取特定币种和交易所的买一价和卖一价。"""
        with self.lock:
            symbol_data = self.shared_data.get(symbol, {})
            exchange_data = symbol_data.get(exchange, {})
            return exchange_data.get("bid"), exchange_data.get("ask")
    
    def is_data_fresh(self, symbol: str, exchange: str) -> bool:
        """检查一个 "币种-交易所" 对的数据是否是新鲜的。"""
        with self.lock:
            key = f"{symbol}_{exchange}"
            last_update = self.last_update_time.get(key)
            if last_update is None:
                return False
            return (time.time() - last_update) < self.data_freshness_threshold
        
    def update_order_fill(self, exchange: str, symbol: str, side: str, filled_qty: str) -> None:
        """
        根据订单成交信息，构建键并更新Redis。
        这个方法是线程安全的，因为它只与Redis交互。
        """
        # 检查Redis连接是否在初始化时成功建立
        if not self.redis_connected:
            data_logger.warning("Redis客户端不可用，跳过订单更新。")
            return

        # 确保side是小写的 'buy' 或 'sell'
        side_lower = side.lower()
        if side_lower not in ['buy', 'sell']:
            data_logger.warning(f"[{exchange}] 未知的订单方向: {side}，跳过Redis更新。")
            return

        # 构建与您示例中完全一致的Redis键
        redis_key = f"trading:order:{symbol}:{exchange}:{side_lower}"
        
        try:
            # 使用类实例的redis_client
            self.redis_client.set(redis_key, filled_qty)
            data_logger.info(f"✅ [REDIS ORDER SET] Key: {redis_key}, Value: {filled_qty}")
        except Exception as e:
            data_logger.error(f"❌ [REDIS ORDER FAILED] 写入失败: Key: {redis_key}, Error: {e}")

# 全局实例
realtime_data_manager = RealtimeDataManager()
