# 日志系统配置说明

## 概述

本项目现在支持带日期的日志文件，可以更好地管理和查看历史日志记录。

## 新增文件

### 1. `websocket_logging.py`
提供WebSocket交易专用的日志配置功能：
- 带日期时间的日志文件名
- 每日轮转日志
- 控制台和文件双输出
- 第三方库日志级别控制

### 2. `logging_setup.py` (已更新)
原有的日志配置文件，现在支持日期文件名：
- `decision_log_YYYY-MM-DD.txt`
- `output_log_YYYY-MM-DD.txt`
- `trade_history_YYYY-MM-DD.log`

## 使用方法

### 方式1: 基本带日期时间的日志

```python
from websocket_logging import setup_websocket_logging

# 创建日志记录器
logger = setup_websocket_logging(
    log_dir='./logs',
    logger_name="MyLogger",
    include_console=True
)

# 使用日志
logger.info("这是一条信息日志")
logger.warning("这是一条警告日志")
logger.error("这是一条错误日志")
```

生成的日志文件名格式：`websocket_trades_2024-01-15_14-30-25.log`

### 方式2: 每日轮转日志

```python
from websocket_logging import setup_daily_rotating_logger

# 创建轮转日志记录器
logger = setup_daily_rotating_logger(
    log_dir='./logs',
    base_filename='websocket_trades',
    logger_name="MyLogger"
)
```

生成的日志文件：
- 当前日志：`websocket_trades.log`
- 历史日志：`websocket_trades.log.2024-01-14`, `websocket_trades.log.2024-01-13` 等

### 方式3: 在trades_ws.py中使用

```python
# 在文件顶部导入
from websocket_logging import setup_websocket_logging

# 替换原有的日志配置
logger = setup_websocket_logging(
    log_dir='./logs/trades',
    logger_name="MultiExchangeListener"
)
```

## 日志目录结构

```
项目根目录/
├── logs/                          # WebSocket日志目录
│   ├── trades/                    # 交易相关日志
│   │   ├── websocket_trades_2024-01-15_14-30-25.log
│   │   └── websocket_trades_2024-01-15_15-45-10.log
│   └── data/                      # 数据管理器日志
│       └── websocket_trades_2024-01-15_14-30-25.log
└── cross_exchange_re/log/         # 原有日志目录
    ├── decision_log_2024-01-15.txt
    ├── output_log_2024-01-15.txt
    └── trade_history_2024-01-15.log
```

## 配置参数

### setup_websocket_logging() 参数

- `log_dir`: 日志目录路径 (默认: './logs')
- `log_level`: 日志级别 (默认: logging.INFO)
- `include_console`: 是否同时输出到控制台 (默认: True)
- `logger_name`: 日志记录器名称 (默认: "WebSocketTrader")

### setup_daily_rotating_logger() 参数

- `log_dir`: 日志目录路径 (默认: './logs')
- `base_filename`: 基础文件名 (默认: 'websocket_trades')
- `log_level`: 日志级别 (默认: logging.INFO)
- `logger_name`: 日志记录器名称 (默认: "WebSocketTrader")

## 日志格式

所有日志都使用统一格式：
```
2024-01-15 14:30:25 | INFO    | MultiExchangeListener | [Binance] WebSocket connection established.
2024-01-15 14:30:26 | WARNING | MultiExchangeListener | [Binance] Failed to extend listenKey
2024-01-15 14:30:27 | ERROR   | MultiExchangeListener | [Binance] Connection error: timeout
```

## 测试

运行测试脚本验证日志配置：

```bash
# 测试所有日志配置
python test_logging.py

# 测试WebSocket日志示例
python trades_ws_logging_example.py
```

## 优势

1. **日期管理**: 每个日志文件都包含创建日期，便于管理
2. **自动轮转**: 支持每日自动轮转，避免单个文件过大
3. **双输出**: 同时输出到文件和控制台，便于调试
4. **级别控制**: 可以控制不同模块的日志级别
5. **编码支持**: 支持UTF-8编码，正确显示中文日志

## 注意事项

1. 确保有足够的磁盘空间存储日志文件
2. 定期清理旧的日志文件，避免占用过多空间
3. 在生产环境中可以考虑只输出到文件，不输出到控制台
4. 可以根据需要调整日志级别，减少不必要的日志输出

## 迁移指南

### 从旧的日志配置迁移

1. 将 `logging.basicConfig()` 替换为 `setup_websocket_logging()`
2. 将 `logging.getLogger()` 的调用移到配置之后
3. 更新导入语句，添加 `from websocket_logging import setup_websocket_logging`

### 示例迁移

**旧代码：**
```python
import logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("MyLogger")
```

**新代码：**
```python
from websocket_logging import setup_websocket_logging
logger = setup_websocket_logging(logger_name="MyLogger")
```
