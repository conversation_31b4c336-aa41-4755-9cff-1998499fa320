#!/usr/bin/env python3
"""
币安WebSocket连接测试脚本
用于验证修复后的币安websocket是否能正常接收订单推送
"""

import asyncio
import logging
from trades_ws import binance_listener, BINANCE_EXCHANGE_NAME

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s | %(levelname)-7s | %(name)s | %(message)s",
    datefmt="%H:%M:%S"
)

logger = logging.getLogger("BinanceWSTest")

async def test_binance_websocket():
    """测试币安websocket连接"""
    logger.info("开始测试币安WebSocket连接...")
    
    try:
        # 运行币安监听器
        await binance_listener()
    except KeyboardInterrupt:
        logger.info("测试被用户中断")
    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}")

if __name__ == "__main__":
    print("=" * 60)
    print("币安WebSocket订单推送测试")
    print("=" * 60)
    print("此脚本将测试修复后的币安websocket连接")
    print("请确保:")
    print("1. api_keys.py中配置了有效的币安API密钥")
    print("2. API密钥有期货交易权限")
    print("3. 网络连接正常")
    print("=" * 60)
    print("按Ctrl+C停止测试")
    print("=" * 60)
    
    try:
        asyncio.run(test_binance_websocket())
    except KeyboardInterrupt:
        print("\n测试已停止")
