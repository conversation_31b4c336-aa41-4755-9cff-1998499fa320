from flask import Flask, jsonify, request
import redis
import json
from flask_cors import CORS
import math

app = Flask(__name__)
# fix for status: CORS(Cross-Origin Resource Sharing) Error
CORS(app)

redis_client = redis.Redis(host='localhost', port = 6379,db=0,decode_responses=True)

@app.route('/health')
def health():
    try:
        redis_client.ping()
        return{"status": "healthy", "redis":"connected"}
    
    except:
        return {"status": "error", "redis":"disconnected"}, 500
    
@app.route('/exchange_data')
def get_exchange_data():
    """"""
    try:
        market_json = redis_client.get("trading:exchange_data")
        if market_json:
            return jsonify(json.loads(market_json))
        return {"error": "No market data available"}

    except Exception as e:
        return {"error": str(e)}, 500

@app.route('/api/market-data')
def get_market_data():
    try:
        market_json = redis_client.get("trading:exchange_data")
        if market_json:
            return jsonify(json.loads(market_json))
        return {"error": "No market data available"}, 404
    except Exception as e:
        return {"error": str(e)}, 500
    
@app.route('/api/balance')
def get_balance():
    """
    获取余额信息。
    此API作为一个适配器，将后端存储的新数据模型
    转换为前端组件期望的旧数据结构。
    """
    try:
        # 1. 从Redis获取由PortfolioManager存储的最新余额数据
        # 这个key现在是唯一的数据源
        balance_json = redis_client.get("trading:balance")
        
        if not balance_json:
            return jsonify({"error": "No balance data available"}), 404

        # 2. 将JSON字符串解析为Python字典
        backend_data = json.loads(balance_json)

        # 3. 进行数据转换 (适配层)
        # 将后端的新键名映射到前端期望的旧键名
        frontend_response = {
            "initial_capital": backend_data.get("total_initial_usdt_capital"),
            "total_balance": backend_data.get("total_usdt_balance"),
            "total_pnl": backend_data.get("total_pnl_usdt"),
            "roi_percentage": backend_data.get("roi_percentage"),
            "exchange_balances": {}
        }

        # 4. 遍历并转换嵌套的 exchange_balances 字典
        if "exchange_balances" in backend_data:
            for ex_name, ex_data in backend_data["exchange_balances"].items():
                frontend_response["exchange_balances"][ex_name] = {
                    "capital": ex_data.get("initial_usdt_capital"),
                    "total": ex_data.get("account_usdt_value"),
                    "available": ex_data.get("available_usdt_balance"),
                    # "used": ex_data.get("used_margin_usdt")
                    # 前端需要的 utilization 会在组件内部计算，我们无需提供
                }
        
        # 5. 返回适配后的数据
        return jsonify(frontend_response)

    except Exception as e:
        # 记录异常会更有帮助，例如: app.logger.error(f"Error in /api/balance: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/current-position')
def get_current_position():
    """Get current active position - matches display_trade structure"""
    #TODO
    try:
        positions_data = redis_client.get("trading:current_position")
        if positions_data:
            return jsonify(json.loads(positions_data))
        else:
            return ''
    except Exception as e:
        return {"error": str(e)}, 500

@app.route('/api/trade-history')
def get_trade_history():
    """Get paginated trade history sorted by latest trade"""
    try:
        # Pagination parameters
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 20))

        history_data = redis_client.get("trading:formatted_history")
        if not history_data:
            return {"error": "No trade history available"}, 404

        history = json.loads(history_data)

        trade_pairs = history.get("trade_pairs", [])

        # Sort trades by close_time (fall back to open_time if missing)
        trade_pairs_sorted = sorted(
            trade_pairs,
            key=lambda t: t.get("close_time") or t.get("open_time", 0),
            reverse=True
        )

        total_items = len(trade_pairs_sorted)
        total_pages = math.ceil(total_items / per_page)

        start = (page - 1) * per_page
        end = start + per_page
        paginated_trades = trade_pairs_sorted[start:end]

        return jsonify({
            "last_updated": history.get("last_updated"),
            "summary": history.get("summary"),
            "page": page,
            "per_page": per_page,
            "total_pages": total_pages,
            "total_items": total_items,
            "trade_pairs": paginated_trades
        })

    except Exception as e:
        return {"error": str(e)}, 500

@app.route('/api/latest-opportunity')
def get_latest_opportunity():
    """Get latest arbitrage opportunity with spread info"""
    try:
        opportunity_data = redis_client.get("trading:best_spread")
        if opportunity_data:
            return jsonify(json.loads(opportunity_data))
        return {"error": "No opportunities available"}, 404
    except Exception as e:
        return {"error": str(e)}, 500

@app.route('/api/metrics')
def get_metrics():
    """Get trading metrics and statistics"""
    try:
        metrics = redis_client.hgetall("trading:metrics")
        return jsonify(metrics)
    except Exception as e:
        return {"error": str(e)}, 500

@app.route('/api/opportunities')
def get_opportunities():
    """Get latest arbitrage opportunities"""
    try:
        opportunities = redis_client.get("trading:opportunities")
        if opportunities:
            return jsonify(json.loads(opportunities))
        return {"error": "No opportunities data available"}, 404
    except Exception as e:
        return {"error": str(e)}, 500

if __name__ == '__main__':
    app.run(debug=True,host='0.0.0.0', port = 5036)