import time
from trader import TraderState, unified_place_order,OrderSignal
from logging_setup import setup_loggers
from trader import connect_to_okx
from exchange import OkxExchangeHTTP
import uuid

trader_state = TraderState()
decision_logger, output_logger,history_logger = setup_loggers()
decision_logger.info(f'模拟开始')

okx_exchange = OkxExchangeHTTP('Okx','85919a48-8a5a-484c-9b3a-e0403d469b1f', '965C4759E597A89C83FA08D6B4DF4AC1', 'cntest001@O',isTest=True)
# okx_exchange = OkxExchangeHTTP('Okx','ecc247b6-7c2d-4d97-a751-81c0fb828a6b', '3A67F1CA4D528D5010880BD0E37348E6', 'cntest001@O',isTest=False)
okx_exchange.connect()
trader_state.exchanges['Okx'] = okx_exchange
total_equity, total_available_balance = okx_exchange.get_balance_info()
if total_equity:
    decision_logger.info(f'Okx 剩余可用资金: {total_available_balance}')
else:
    decision_logger.info(f'Okx 剩余可用资金查询失败')
# https://www.okx.com/docs-v5/en/#trading-account-rest-api-get-balance

# acc_configs = okx_exchange.session.account.get_account_config()
# set_lv_response= okx_exchange.session.account.set_account_level("2")
# acc_mode = okx_exchange._get_position_mode()
# okx_exchange.session.account.set_position_mode('net_mode')
# acc_mode = okx_exchange._get_position_mode()

okx_exchange_api = trader_state.exchanges['Okx']
# buy_order_signal = OrderSignal('linear','QTUMUSDT','buy','market','10','Okx')


# okx_buy_res = unified_place_order(trader_state,buy_order_signal)


# print(okx_buy_res)
"""(True, {'code': '0', 'data': [{'clOrdId': '', 'ordId': '2793186467546947584', 'sCode': '0', 'sMsg': 'Order placed', 'tag': '538a3965e538BCDE', 'ts': '*************'}], 'inTime': '****************', 'msg': '', 'outTime': '****************'})
INFO:httpx:HTTP Request: POST https://www.okx.com/api/v5/trade/order "HTTP/2 200 OK"
"""

# sell_order_signal = OrderSignal('linear','QTUMUSDT','sell','market','10','Okx')
# okx_sell_res = unified_place_order(trader_state, sell_order_signal)

# print(okx_sell_res)
"""(True, {'code': '0', 'data': [{'clOrdId': '', 'ordId': '2793187029986336768', 'sCode': '0', 'sMsg': 'Order placed', 'tag': '538a3965e538BCDE', 'ts': '*************'}], 'inTime': '****************', 'msg': '', 'outTime': '****************'})"""

# okx 获取余额速率限制 2秒10次，完全够用
# history = okx_exchange.session.account.get_positions_history()
# print(history)
"""{'code': '0', 'data': [{'cTime': '*************', 'ccy': 'USDT', 'closeAvgPx': '2.345', 'closeTotalPos': '10', 'direction': 'long', 'fee': '-0.016433', 'fundingFee': '0', 'instId': 'QTUM-USDT-SWAP', 'instType': 'SWAP', 'lever': '3.0', 'liqPenalty': '0', 'mgnMode': 'cross', 'nonSettleAvgPx': '', 'openAvgPx': '2.354', 'openMaxPos': '10', 'pnl': '-0.09', 'pnlRatio': '-0.***************', 'posId': '2793186550124404736', 'posSide': 'net', 'realizedPnl': '-0.106433', 'settledPnl': '', 'triggerPx': '', 'type': '2', 'uTime': '*************', 'uly': 'QTUM-USDT'}], 'msg': ''}"""
# okx_exchange_api.get_balance_info()

# insts = okx_exchange_api.session.account.get_instruments(instType='FUTURES')
# print('hello')

# try api3usdt
lot_size = okx_exchange_api.get_lot_size('API3USDT')
print(lot_size)

# orderbook = okx_exchange_api.session.marketdata.get_orderbook()
