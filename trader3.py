from abc import ABC, abstractmethod
import json
from time import sleep
import time
from typing import Dict
import uuid
import redis
from trading_state import TradingState
from config_copy import Config
from api_keys import EXCHANGE_API_KEYS
from logging_setup import logging, setup_loggers
from exchange import Bitgetv1HTTP,BitgetV2HTTP,OkxExchangeHTTP,BybitExchangeHTTP,BaseExchangeHTTP,BinanceExchangeHTTP
from realtime_data_manager import realtime_data_manager
from dataclasses import dataclass
import math
import sys
import json

setup_loggers()
decision_logger = logging.getLogger('decision_logger')
history_logger = logging.getLogger('history_logger')

class ExchangeAccount:
    """
    管理单个交易所账户的USDT本位财务状态。
    所有价值单位均为USDT。
    """
    def __init__(self, name: str, initial_usdt_capital: float = 0.0):
        self.name = name
        self.initial_usdt_capital = initial_usdt_capital
        
        # 这些值将由API定期更新。初始状态下，总值和可用余额等于初始资本。
        self.account_usdt_capital: float = initial_usdt_capital
        self.account_usdt_value: float = initial_usdt_capital
        self.available_usdt_balance: float = initial_usdt_capital

    def update_balance(self, account_usdt_capital: float, account_usdt_value: float, available_usdt_balance: float):
        """
        使用从交易所API获取的最新USDT余额数据来更新账户状态。
        """
        self.account_usdt_capital = account_usdt_capital
        self.account_usdt_value = account_usdt_value
        self.available_usdt_balance = available_usdt_balance

    @property
    def pnl_usdt(self) -> float:
        """
        实时计算此交易所的USDT盈亏。
        计算公式: 当前账户USDT总值 - 初始USDT本金
        """
        return self.account_usdt_value - self.initial_usdt_capital

    @property
    def used_margin_usdt(self) -> float:
        """
        实时计算当前已使用的USDT保证金。
        计算公式: 账户USDT总值 - 可用USDT余额
        """
        return self.account_usdt_value - self.available_usdt_balance
    
class PortfolioManager:
    """
    聚合所有ExchangeAccount的数据，提供整个投资组合的USDT本位视图。
    """
    def __init__(self, initial_usdt_capitals: dict[str, float]):
        self.accounts: dict[str, ExchangeAccount] = {
            name: ExchangeAccount(name, capital)
            for name, capital in initial_usdt_capitals.items()
        }

    def update_exchange_balance(self, exchange_name: str, account_usdt_capital: float, account_usdt_value: float, available_usdt_balance: float):
        """
        查找对应的交易所账户并更新其USDT余额。
        这是更新投资组合状态的唯一入口。
        """
        if exchange_name in self.accounts:
            self.accounts[exchange_name].update_balance(account_usdt_capital, account_usdt_value, available_usdt_balance)
        else:
            decision_logger.info(f"警告: 尝试更新一个不存在的交易所账户 '{exchange_name}'")

    @property
    def total_initial_usdt_capital(self) -> float:
        """计算所有账户的总初始USDT本金。"""
        return sum(account.initial_usdt_capital for account in self.accounts.values())

    @property
    def total_usdt_balance(self) -> float:
        """计算所有账户当前的USDT总值。"""
        return sum(account.account_usdt_value for account in self.accounts.values())

    @property
    def total_pnl_usdt(self) -> float:
        """计算整个投资组合的总USDT盈亏。"""
        return self.total_usdt_balance - self.total_initial_usdt_capital

    @property
    def roi_percentage(self) -> float:
        """计算整体的投资回报率 (ROI) 百分比。"""
        initial_capital = self.total_initial_usdt_capital
        if initial_capital == 0:
            return 0.0
        return (self.total_pnl_usdt / initial_capital) * 100

    def to_dict(self) -> dict:
        """
        将整个投资组合的状态序列化为字典，以便持久化或用于API响应。
        键名清晰地反映了USDT单位。
        """
        return {
            "total_initial_usdt_capital": self.total_initial_usdt_capital,
            "total_usdt_balance": self.total_usdt_balance,
            "total_pnl_usdt": self.total_pnl_usdt,
            "roi_percentage": self.roi_percentage,
            "exchange_balances": {
                name: {
                    "initial_usdt_capital": acc.initial_usdt_capital,
                    "account_usdt_value": acc.account_usdt_value,
                    "available_usdt_balance": acc.available_usdt_balance,
                    "used_margin_usdt": acc.used_margin_usdt,
                    "pnl_usdt": acc.pnl_usdt,
                }
                for name, acc in self.accounts.items()
            }
        }


class TradeHistoryManager:
    def __init__(self,redis_client):
        self.redis_client = redis_client
        self.trade_history = {}
        self._load_from_redis()

    def _load_from_redis(self):
        decision_logger.info("从redis加载历史数据")
        all_trade_ids = self.redis_client.zrange("trades:by_time", 0, -1)

        for trade_id in all_trade_ids:
            trade_data = self.redis_client.hgetall(f'trade:detail:{trade_id}')

            open_record = json.loads(trade_data.get('open_record', '{}'))
            close_record = json.loads(trade_data.get('close_record', '{}')) if trade_data.get('close_record') else None

            if open_record:
                self.trade_history[trade_id] = {
                    "open": open_record,
                    "close": close_record
                }
        decision_logger.info(f"加载完成，共 {len(self.trade_history)} 笔交易")

class OrderSignal:
    def __init__(self,category:str,
                 symbol: str,
                 side: str,  # buy/sell
                orderType: str,  # market/limit, not supporting limit yet TODO
                quantity: float,
                exchange: str,
                action: str,
                price: float=None,
                ):
        if orderType == "limit" and price is None:
            raise ValueError("Limit orders must have a price specified.")
        self.category = category
        self.symbol = symbol
        self.orderType = orderType
        self.side = side
        self.quantity = quantity
        self.exchange = exchange
        self.id = uuid.uuid4()
        self.timestamp = time.time()
        self.action = action
        self.price = price
   

class TraderState:

    def __init__(self):
        self.active_trades = []
        self.trade_history = {}
        self.exchanges = {'Bybit': None, 'Okx': None, 'Bitget': None, 'Binance': None}
        self.test_exchanges = ['Bybit','Okx','Bitget','Binance']
        self.redis_client = redis.Redis(host = 'localhost', port = 6379, db=0, decode_responses=True)
        self.trade_history_manager = TradeHistoryManager(self.redis_client)
        self.portfolio_manager = PortfolioManager(Config.INITIAL_USDT_CAPITALS)

    def generate_and_store_from_history(self):
        """
        从 self.trade_history 生成 API 所需的格式，并存入 Redis。
        这是一个 O(M) 操作, M是独立交易的数量。
        """
        trade_pairs = []
        summary = {
            'total_trades': len(self.trade_history), # 总交易数就是 self.trade_history 的长度
            'open_trades': 0,
            'profitable_trades': 0,
            'total_pnl': 0.0
        }

        # 遍历 self.trade_history 中的每一笔配对好的交易
        for trade_id, trade_data in self.trade_history.items():
            open_record = trade_data['open']
            close_record = trade_data['close']

            open_buy_detail = open_record['real']['buy_record']
            open_sell_detail = open_record['real']['sell_record']

            trade_pair = {
                'symbol': open_buy_detail['symbol'],
                'buy_exchange': open_buy_detail['exchange'],
                'sell_exchange': open_sell_detail['exchange'],
                'open_buy_price': open_buy_detail['avg_price'],
                'open_sell_price': open_sell_detail['avg_price'],
                'open_time': open_buy_detail['our_time'],
                'status': 'OPEN',
                'pnl': 0.0,
                'close_time': None,
                'close_buy_price': None,
                'close_sell_price': None
            }

            if close_record:
                close_buy_detail = close_record['real']['buy_record']
                close_sell_detail = close_record['real']['sell_record']
                
                # TODO: 替换为真实的 PnL 逻辑
                pnl = close_record.get('virtual', {}).get('pnl', 0.0) 

                trade_pair.update({
                    'status': 'CLOSED',
                    'close_time': close_buy_detail['our_time'],
                    'close_buy_price': close_buy_detail['avg_price'],
                    'close_sell_price': close_sell_detail['avg_price'],
                    'pnl': pnl
                })
                
                summary['total_pnl'] += pnl
                if pnl >= 0:
                    summary['profitable_trades'] += 1
            else:
                summary['open_trades'] += 1
                # TODO: 更新实时 PnL
                # trade_pair['pnl'] = open_record.get('virtual', {}).get('current_pnl', 0.0)

            trade_pairs.append(trade_pair)

        final_data_to_store = {
            'trade_pairs': trade_pairs,
            'summary': summary,
            'last_updated': time.time()
        }

        self.redis_client.set("trading:formatted_history", json.dumps(final_data_to_store))
        decision_logger.info(f"已从 self.trade_history 生成并存储最新交易历史到 Redis。")

    def update_and_save(self, full_trade_record):
        """
        增量更新Redis和内存（更健壮的版本）。
        """
        # Use .get() to safely access keys that might not exist.
        trade_id = full_trade_record.get('trade_id')
        action = full_trade_record.get('action')

        # ✅ **Crucial Check 1: Ensure trade_id and action exist**
        if not trade_id or not action:
            # Using logger instead of print
            decision_logger.error(f"记录缺少 'trade_id' 或 'action'。跳过: {full_trade_record}")
            return

        # 1. 更新内存
        self.update_trade_history(full_trade_record)

        # 2. 增量更新Redis
        pipe = self.redis_client.pipeline()
        
        trade_detail_key = f"trade:detail:{trade_id}"
        summary_key = "trading:summary"

        if action == 'open':
            # Safely get the open_time using nested .get() calls
            open_time = full_trade_record.get('real', {}).get('buy_record', {}).get('our_time')
            
            # ✅ **Crucial Check 2: Ensure the timestamp for ZADD exists**
            if open_time is None:
                decision_logger.error(f"开仓记录 {trade_id} 缺少 'our_time'。无法更新Redis。")
                return

            pipe.hset(trade_detail_key, "open_record", json.dumps(full_trade_record))
            pipe.hset(trade_detail_key, "status", "OPEN")
            pipe.zadd("trades:by_time", {trade_id: open_time})
            pipe.hincrby(summary_key, "total_trades", 1)
            pipe.hincrby(summary_key, "open_trades", 1)

        elif action == 'close':
            close_time = full_trade_record.get('real', {}).get('close_buy_record', {}).get('our_time')
            pnl = full_trade_record.get('virtual', {}).get('pnl', 0.0) # This part is already safe

            # ✅ **Crucial Check 3: Ensure the timestamp for ZADD exists**
            if close_time is None:
                decision_logger.error(f"平仓记录 {trade_id} 缺少 'our_time'。无法更新Redis。")
                return

            pipe.hset(trade_detail_key, "close_record", json.dumps(full_trade_record))
            pipe.hset(trade_detail_key, "status", "CLOSED")
            pipe.hset(trade_detail_key, "pnl", pnl)
            pipe.zadd("trades:by_time", {trade_id: close_time})
            pipe.hincrby(summary_key, "open_trades", -1)
            pipe.hincrbyfloat(summary_key, "total_pnl", pnl)
            if pnl >= 0:
                pipe.hincrby(summary_key, "profitable_trades", 1)

        # 执行所有命令
        pipe.execute()
        # Using logger instead of print
        decision_logger.info(f"增加Redis中的交易: {trade_id}")

    def update_trade_history(self, full_trade_record):
        """
        高效地更新内存中的 trade_self.trade_history。
        这是一个 O(1) 操作。
        """
        trade_id = full_trade_record['trade_id']
        action = full_trade_record['action']

        if action == 'open':
            # 如果是开仓，创建一个新条目
            if trade_id in self.trade_history_manager.trade_history:
                decision_logger.info(f"警告: 收到重复的开仓ID {trade_id}，将覆盖。")
            self.trade_history_manager.trade_history[trade_id] = {
                "open": full_trade_record,
                "close": None
            }
            decision_logger.info(f"新交易已开仓: {trade_id}")

        elif action == 'close':
            # 如果是平仓，更新现有条目
            if trade_id in self.trade_history_manager.trade_history:
                self.trade_history_manager.trade_history[trade_id]['close'] = full_trade_record
                decision_logger.info(f"交易已平仓: {trade_id}")
            else:
                # 异常情况：收到了一个没有开仓记录的平仓ID
                decision_logger.info(f"错误: 收到未知的平仓ID {trade_id}，无法匹配。")

    def calculate_trade_amount(self, symbol, buy_exchange_api:BaseExchangeHTTP, buy_price, sell_exchange_api:BaseExchangeHTTP,sell_price):
        """计算交易数量 - 带缓存命中检测"""
        decision_logger.info(f"🔍 开始计算交易数量: {symbol}, 买方:{buy_exchange_api.exchange_name}, 卖方:{sell_exchange_api.exchange_name}")

        # 获取余额信息 - 检测缓存命中
        decision_logger.info(f"📊 获取 {buy_exchange_api.exchange_name} 余额信息...")
        buy_has_cache = buy_exchange_api._balance_cache is not None
        decision_logger.info(f"   余额缓存状态: {'✅ 有缓存，将使用缓存' if buy_has_cache else '❌ 无缓存，将调用API'}")

        _, buy_available = buy_exchange_api.get_balance_info(force_update=False)
        if buy_available == '' or buy_available is None:
            decision_logger.error(f'获取 {buy_exchange_api.exchange_name} 余额失败')
            return 0
        decision_logger.info(f"   ✅ {buy_exchange_api.exchange_name} 可用余额: {buy_available}")

        decision_logger.info(f"📊 获取 {sell_exchange_api.exchange_name} 余额信息...")
        sell_has_cache = sell_exchange_api._balance_cache is not None
        decision_logger.info(f"   余额缓存状态: {'✅ 有缓存，将使用缓存' if sell_has_cache else '❌ 无缓存，将调用API'}")

        _, sell_available = sell_exchange_api.get_balance_info(force_update=False)
        if sell_available == '' or sell_available is None:
            decision_logger.error(f'获取 {sell_exchange_api.exchange_name} 余额失败')
            return 0
        decision_logger.info(f"   ✅ {sell_exchange_api.exchange_name} 可用余额: {sell_available}")
        
        buy_available = float(buy_available)
        sell_available = float(sell_available)
        usdt_available = min(buy_available,sell_available)
        buy_available = float(usdt_available) * 0.85
        buy_amount_draft = buy_available / buy_price
        sell_available = float(usdt_available) * 0.85
        sell_amount_draft = sell_available / sell_price
        order_amount = min(buy_amount_draft,sell_amount_draft)

        # 获取符号信息 - 检测缓存命中
        decision_logger.info(f"📋 获取 {buy_exchange_api.exchange_name} {symbol} 符号信息...")
        buy_symbol_loaded = buy_exchange_api._symbol_info_loaded
        buy_symbol_cached = symbol in buy_exchange_api._symbol_info_cache if buy_symbol_loaded else False
        decision_logger.info(f"   符号缓存状态: {'✅ 已加载' if buy_symbol_loaded else '❌ 未加载'}, {'✅ 有缓存' if buy_symbol_cached else '❌ 无缓存'}")

        buy_ct_val = None
        if buy_exchange_api.exchange_name == 'Okx':
            buy_lot,max_buy_q,buy_ct_val = buy_exchange_api.get_symbol_info(symbol)
        else:
            buy_lot,max_buy_q,_ = buy_exchange_api.get_symbol_info(symbol)
        decision_logger.info(f"   ✅ {buy_exchange_api.exchange_name} {symbol}: min_qty={buy_lot}, max_qty={max_buy_q}, ct_val={buy_ct_val}")
        if buy_lot == None:
            buy_lot = 1.0

        decision_logger.info(f"📋 获取 {sell_exchange_api.exchange_name} {symbol} 符号信息...")
        sell_symbol_loaded = sell_exchange_api._symbol_info_loaded
        sell_symbol_cached = symbol in sell_exchange_api._symbol_info_cache if sell_symbol_loaded else False
        decision_logger.info(f"   符号缓存状态: {'✅ 已加载' if sell_symbol_loaded else '❌ 未加载'}, {'✅ 有缓存' if sell_symbol_cached else '❌ 无缓存'}")

        sell_ct_val = None
        if sell_exchange_api.exchange_name == 'Okx':
            sell_lot,max_sell_q,sell_ct_val = sell_exchange_api.get_symbol_info(symbol)
        else:
            sell_lot,max_sell_q,_ = sell_exchange_api.get_symbol_info(symbol)
        decision_logger.info(f"   ✅ {sell_exchange_api.exchange_name} {symbol}: min_qty={sell_lot}, max_qty={max_sell_q}, ct_val={sell_ct_val}")
        if sell_lot == None:
            sell_lot = 1.0


        # our_lot_size = buy_lot, sell_lot 的最小公倍数    
        our_lot_size = buy_exchange_api.lcm(buy_lot,sell_lot)
        if our_lot_size == 0:
            return 0
        order_amount = math.floor(order_amount / our_lot_size) * our_lot_size

        # 检查最大交易量限制，避免None值比较
        if max_buy_q is not None:
            order_amount = min(order_amount, max_buy_q)
        if max_sell_q is not None:
            order_amount = min(order_amount, max_sell_q)

        # 如果任一交易所的最大交易量为None，记录警告
        if max_buy_q is None or max_sell_q is None:
            decision_logger.warning(f"交易量限制查询失败: max_buy_q={max_buy_q}, max_sell_q={max_sell_q}")

        # OKX 合约面值修正：当包含 OKX 时，需要考虑张数取整对实际建仓数量的影响
        if buy_exchange_api.exchange_name == 'Okx' or sell_exchange_api.exchange_name == 'Okx':
            if buy_exchange_api.exchange_name == 'Okx' and buy_ct_val is not None:
                # 买方是 OKX，计算实际可交易的张数
                okx_contracts = int(order_amount / buy_ct_val)  # 向下取整到整张
                actual_order_amount = okx_contracts * buy_ct_val  # 实际建仓数量
                decision_logger.info(f"OKX买方张数修正: {order_amount:.6f} 币 -> {okx_contracts} 张 -> {actual_order_amount:.6f} 币")
                order_amount = actual_order_amount

            elif sell_exchange_api.exchange_name == 'Okx' and sell_ct_val is not None:
                # 卖方是 OKX，计算实际可交易的张数
                okx_contracts = int(order_amount / sell_ct_val)  # 向下取整到整张
                actual_order_amount = okx_contracts * sell_ct_val  # 实际建仓数量
                decision_logger.info(f"OKX卖方张数修正: {order_amount:.6f} 币 -> {okx_contracts} 张 -> {actual_order_amount:.6f} 币")
                order_amount = actual_order_amount

            # 如果修正后的数量为0或过小，返回0
            if order_amount <= 0:
                decision_logger.warning("OKX张数修正后交易量为0，跳过此次套利")
                return 0

        # now put into orderbook and get the number of trades within slippage
        # TODO

        # 缓存命中总结
        decision_logger.info(f"💡 缓存命中总结:")
        decision_logger.info(f"   余额缓存: {buy_exchange_api.exchange_name}={'✅命中' if buy_has_cache else '❌未命中'}, {sell_exchange_api.exchange_name}={'✅命中' if sell_has_cache else '❌未命中'}")
        decision_logger.info(f"   符号缓存: {buy_exchange_api.exchange_name}={'✅命中' if buy_symbol_cached else '❌未命中'}, {sell_exchange_api.exchange_name}={'✅命中' if sell_symbol_cached else '❌未命中'}")
        decision_logger.info(f"🎯 最终交易数量: {order_amount}")

        return order_amount
        # return 100
    
    def get_current_position_size(self):
        return len(self.active_trades)

    def _handle_partial_open_failure(self, successful_exchange, successful_record, failure_reason):
        """处理建仓部分失败的情况"""
        decision_logger.error(f"🚨 建仓部分失败处理: {failure_reason}")
        decision_logger.error(f"成功的交易所: {successful_exchange}")
        decision_logger.error(f"成功的订单记录: {successful_record}")

        # 尝试自动处理
        if successful_record and 'orderid' in successful_record:
            orderid = successful_record['orderid']
            decision_logger.error(f"尝试撤销订单: {orderid}")

            # TODO: 实现自动撤销
            cancel_success = self._attempt_cancel_order(successful_exchange, orderid)

            if cancel_success:
                decision_logger.info(f"✅ 成功撤销订单 {orderid}")
            else:
                decision_logger.error(f"❌ 撤销订单失败 {orderid}")
                decision_logger.error("⚠️ 存在单边仓位风险，需要手动处理！")

                # 记录风险仓位
                self._record_risk_position(successful_exchange, successful_record, failure_reason)
        else:
            decision_logger.error("⚠️ 无法获取订单ID，需要手动检查和处理！")

    def _attempt_cancel_order(self, exchange_name, orderid):
        """尝试撤销订单"""
        try:
            exchange_api = self.exchanges[exchange_name]

            # TODO: 实现各交易所的撤销订单API
            if hasattr(exchange_api, 'cancel_order'):
                return exchange_api.cancel_order(orderid)

            # decision_logger.warning(f"撤销订单功能尚未实现: {exchange_name}")
            return False

        except Exception as e:
            decision_logger.error(f"撤销订单异常: {e}")
            return False

    def _record_risk_position(self, exchange_name, order_record, risk_reason):
        """记录风险仓位"""
        risk_position = {
            'timestamp': time.time(),
            'exchange': exchange_name,
            'order_record': order_record,
            'risk_reason': risk_reason,
            'status': 'NEEDS_MANUAL_HANDLING'
        }

        # TODO: 保存到风险仓位数据库
        decision_logger.error(f"🚨 记录风险仓位: {risk_position}")

        # TODO: 发送告警通知
        # self._send_risk_alert(risk_position)

    def refresh_all_balances(self):
        """强制刷新所有交易所的余额缓存"""
        decision_logger.info("强制刷新所有交易所余额...")

        for exchange_name, exchange_api in self.exchanges.items():
            if exchange_api is not None:
                decision_logger.info(f"刷新 {exchange_name} 余额...")
                balance = exchange_api.get_balance_info(force_update=True)
                self.portfolio_manager.update_exchange_balance(exchange_name, Config.INITIAL_USDT_CAPITALS[exchange_name], balance[0], balance[1])
                if balance and balance[0] is not None:
                    decision_logger.info(f"{exchange_name} 余额: 总资产={balance[0]}, 可用={balance[1]}")
                else:
                    decision_logger.error(f"{exchange_name} 余额获取失败")

        # 3. (可选但推荐) 更新后，立即将最新的整体状态持久化到Redis
        latest_state = self.portfolio_manager.to_dict()
        self.redis_client.set("trading:balance", json.dumps(latest_state))

        decision_logger.info("余额刷新完成")

    def load_all_symbol_info(self):
        """加载所有交易所的符号信息到缓存"""
        decision_logger.info("开始加载所有交易所的符号信息...")

        for exchange_name, exchange_api in self.exchanges.items():
            if exchange_api is not None:
                decision_logger.info(f"加载 {exchange_name} 符号信息...")
                try:
                    exchange_api.load_all_symbol_info()
                    symbol_count = len(exchange_api._symbol_info_cache)
                    decision_logger.info(f"{exchange_name} 成功加载 {symbol_count} 个符号信息")
                except Exception as e:
                    decision_logger.error(f"{exchange_name} 符号信息加载失败: {e}")

        decision_logger.info("符号信息加载完成")

    def estimate_market_impact(self, symbol, buy_exchange, sell_exchange, trade_amount):
        """预估市价单的市场影响（滑点）"""
        # 基础滑点预估（可以根据历史数据优化）
        base_slippage = 0.02  # 0.02% 基础滑点

        # 根据交易量调整滑点
        if trade_amount > 10000:  # 大额交易
            volume_slippage = 0.01
        elif trade_amount > 1000:  # 中等交易
            volume_slippage = 0.005
        else:  # 小额交易
            volume_slippage = 0.002

        # 根据交易所调整滑点（不同交易所流动性不同）
        exchange_slippage = {
            'Bybit': 0.002,
            'Okx': 0.003,
            'Bitget': 0.004,
            'Binance': 0.002
        }

        buy_slippage = exchange_slippage.get(buy_exchange, 0.005)
        sell_slippage = exchange_slippage.get(sell_exchange, 0.005)

        total_slippage = base_slippage + volume_slippage + buy_slippage + sell_slippage

        decision_logger.debug(f"📊 滑点预估 {symbol}: 基础{base_slippage:.3f}% + 交易量{volume_slippage:.3f}% + 交易所{buy_slippage:.3f}%/{sell_slippage:.3f}% = {total_slippage:.3f}%")

        return total_slippage

    def validate_spread_before_trading(self, symbol, calculated_spread_pct, buy_exchange, sell_exchange, trade_amount):
        """建仓前验证价差的可行性"""
        decision_logger.info(f"🔍 建仓前价差验证: {symbol}")

        # 1. 获取最新实时价格
        latest_data = self.get_symbol_realtime_data(symbol, [buy_exchange, sell_exchange])

        if not latest_data:
            decision_logger.error("❌ 无法获取实时数据，取消建仓")
            return False, None, None

        # 2. 检查数据新鲜度
        max_age = max(latest_data["exchanges"][ex]["age"] for ex in [buy_exchange, sell_exchange])
        if max_age > 2.0:
            decision_logger.warning(f"⚠️ 数据较旧({max_age:.3f}s)，价差可能不准确")

        # 3. 使用最新价格重新计算价差
        buy_price = latest_data["exchanges"][buy_exchange]["ask"]
        sell_price = latest_data["exchanges"][sell_exchange]["bid"]

        actual_spread = sell_price - buy_price
        actual_spread_pct = 2 * actual_spread / (buy_price + sell_price) * 100

        # 4. 价差偏差检查
        spread_deviation = abs(actual_spread_pct - calculated_spread_pct)
        decision_logger.info(f"📊 价差对比: 计算{calculated_spread_pct:.5f}% vs 实际{actual_spread_pct:.5f}% (偏差{spread_deviation:.5f}%)")

        if spread_deviation > 0.02:  # 偏差超过0.02%
            decision_logger.warning(f"⚠️ 价差偏差较大: {spread_deviation:.5f}%")

        # 5. 预估滑点影响
        estimated_slippage = self.estimate_market_impact(symbol, buy_exchange, sell_exchange, trade_amount)
        conservative_spread_pct = actual_spread_pct - estimated_slippage

        decision_logger.info(f"📊 滑点分析: 实际价差{actual_spread_pct:.5f}% - 预估滑点{estimated_slippage:.5f}% = 保守价差{conservative_spread_pct:.5f}%")

        # 6. 最终决策
        min_profitable_spread = 0.05  # 最小盈利价差阈值
        if conservative_spread_pct >= min_profitable_spread:
            decision_logger.info(f"✅ 保守价差{conservative_spread_pct:.5f}% >= 阈值{min_profitable_spread:.5f}%，可以建仓")
            return True, buy_price, sell_price
        else:
            decision_logger.warning(f"❌ 保守价差{conservative_spread_pct:.5f}% < 阈值{min_profitable_spread:.5f}%，取消建仓")
            return False, None, None

    def get_realtime_market_data(self, use_redis_fallback=True):
        """获取实时市场数据
        Args:
            use_redis_fallback: 如果实时数据不可用，是否回退到Redis
        Returns:
            市场数据字典或None
        """
        # 优先使用实时数据管理器
        try:
            realtime_data = realtime_data_manager.get_realtime_data()
            if realtime_data:
                decision_logger.debug("✅ 使用实时数据管理器数据")
                return realtime_data
        except Exception as e:
            decision_logger.warning(f"实时数据管理器访问失败: {e}")

        # 回退到Redis数据
        if use_redis_fallback:
            try:
                raw_data = self.redis_client.get("trading:exchange_data")
                if raw_data:
                    data = json.loads(raw_data)
                    decision_logger.debug("⚠️ 使用Redis缓存数据（可能有延迟）")
                    return data
            except Exception as e:
                decision_logger.error(f"Redis数据获取失败: {e}")

        decision_logger.error("❌ 无法获取市场数据")
        return None

    def get_symbol_realtime_data(self, symbol, required_exchanges=None):
        """获取指定symbol的实时数据，带数据质量检查"""
        if required_exchanges is None:
            required_exchanges = ['Bybit', 'Okx', 'Bitget', 'Binance']

        # 使用实时数据管理器获取套利数据
        arbitrage_data = realtime_data_manager.get_arbitrage_data(symbol, required_exchanges)

        if arbitrage_data:
            # 检查数据新鲜度
            max_age = max(ex_data["age"] for ex_data in arbitrage_data["exchanges"].values())
            if max_age < 2.0:  # 数据年龄小于2秒
                decision_logger.debug(f"✅ {symbol} 实时数据质量良好，最大年龄: {max_age:.3f}秒")
                return arbitrage_data
            else:
                decision_logger.warning(f"⚠️ {symbol} 数据较旧，最大年龄: {max_age:.3f}秒")

        # 回退到传统方式
        decision_logger.warning(f"❌ {symbol} 实时数据不可用，回退到Redis")
        return None

    def _handle_partial_close_failure(self, successful_exchange, failed_exchange,
                                    successful_record, failed_record, failure_reason):
        """处理平仓部分失败的情况"""
        decision_logger.error(f"平仓部分失败处理: {failure_reason}")
        decision_logger.error(f"成功的交易所: {successful_exchange}")
        decision_logger.error(f"失败的交易所: {failed_exchange}")

        # TODO: 实现风险处理逻辑
        # 1. 记录部分平仓状态
        # 2. 尝试重新平仓失败的部分
        # 3. 如果重试失败，标记为风险仓位
        # 4. 发送告警通知

        decision_logger.error("⚠️ 部分平仓失败，存在风险敞口，需要手动处理！")
    
    def open_position(self, symbol, buy_exchange, sell_exchange, buy_price, sell_price) -> bool:
        """open positions, add to trade history and active trades return True if success, False if fail"""

        succeed = False
        buy_exchange_api = self.exchanges[buy_exchange]
        sell_exchange_api = self.exchanges[sell_exchange]
        time_before_calculate = time.time()
        trade_amount = self.calculate_trade_amount(symbol,buy_exchange_api,buy_price,sell_exchange_api,sell_price)
        time_after_calculate = time.time()
        decision_logger.info(f'time before calculate: {time_before_calculate} time after calculate: {time_after_calculate}, diff: {time_after_calculate - time_before_calculate}')
        decision_logger.info(f'货币: {symbol}, {buy_price}@{buy_exchange}, {sell_price}@{sell_exchange})')

        decision_logger.info(f'计算得出总交易量(开/平): {trade_amount}')

        if trade_amount == 0:
            decision_logger.info(f'计算交易量为0，不交易')
            return False, {"trade_amount": 0}
        buy_amount = trade_amount
        sell_amount = trade_amount

        buy_record = {}
        sell_record = {}

        # connect to real api
        time_before_buy = time.time()
        buy_order_signal = OrderSignal('linear',symbol,'buy','limit',trade_amount,buy_exchange,'open', buy_price)
        buy_succeed, buy_orderid = self.unified_place_order(buy_order_signal)
        time_after_buy = time.time()
        decision_logger.info(f'time before buy: {time_before_buy} time after buy: {time_after_buy}, diff: {time_after_buy - time_before_buy}')

        time_before_sell = time.time()
        sell_order_signal = OrderSignal('linear',symbol,'sell','limit',trade_amount,sell_exchange,'open', sell_price)
        sell_succeed, sell_orderid = self.unified_place_order(sell_order_signal)
        time_after_sell = time.time()
        decision_logger.info(f'time before sell: {time_before_sell} time after sell: {time_after_sell}, diff: {time_after_sell - time_before_sell}')

        if buy_succeed and buy_orderid:
            cnt = 0
            while cnt < 3:
                buy_record = self.exchanges[buy_order_signal.exchange].get_order_detail(buy_orderid)
                if buy_record is not None:
                    break
                cnt += 1
        else:
            decision_logger.error(f"{buy_order_signal.exchange} 下单失败或缺少订单ID")

        if sell_succeed and sell_orderid:
            cnt = 0
            while cnt < 3:
                sell_record = self.exchanges[sell_order_signal.exchange].get_order_detail(sell_orderid)
                if sell_record is not None:
                    break
                cnt += 1
        else:
            decision_logger.error(f"{sell_order_signal.exchange} 下单失败或缺少订单ID")

        # 处理建仓失败情况
        if not buy_succeed:
            decision_logger.error(f'买单@{buy_exchange} 失败. info: {buy_record}')

        if not sell_succeed:
            decision_logger.error(f'卖单@{sell_exchange} 失败. info: {sell_record}')

        # 检查是否需要处理部分成功的情况
        if buy_succeed and not sell_succeed:
            decision_logger.error(f"建仓部分失败：买单成功但卖单失败，需要撤销买单")
            self._handle_partial_open_failure(buy_exchange, buy_record, "sell_failed")
            return False, {"buy_succeed": True, "sell_succeed": False, "action": "partial_failure_handled"}

        elif not buy_succeed and sell_succeed:
            decision_logger.error(f"建仓部分失败：卖单成功但买单失败，需要撤销卖单")
            self._handle_partial_open_failure(sell_exchange, sell_record, "buy_failed")
            return False, {"buy_succeed": False, "sell_succeed": True, "action": "partial_failure_handled"}

        elif not buy_succeed and not sell_succeed:
            decision_logger.error(f"建仓完全失败：买单和卖单都失败")
            return False, {"buy_succeed": False, "sell_succeed": False, "action": "complete_failure"}

        # 只有在两个订单都成功时才继续
        if buy_succeed and sell_succeed:
            actual_buy_amount = trade_amount

            # 安全地获取订单ID
            buy_orderid = None
            sell_orderid = None

            # decision_logger.info(f'buy_record: {buy_record}')
            if buy_record and 'orderid' in buy_record:
                buy_orderid = buy_record['orderid']
            else:
                decision_logger.error(f"买单记录中没有orderid字段: {buy_record}")
                return False, {"buy_succeed": False, "sell_succeed": None, "action": "complete_failure"}

            if sell_record and 'orderid' in sell_record:
                sell_orderid = sell_record['orderid']
            else:
                decision_logger.error(f"卖单记录中没有orderid字段: {sell_record}")
                return False, {"buy_succeed": True, "sell_succeed": False, "action": "complete_failure"}

            # 获取订单详情
            buy_detail = buy_record # buy_exchange_api.get_order_detail(buy_orderid)
            sell_detail = sell_record # sell_exchange_api.get_order_detail(sell_orderid)

            # 检查订单详情是否获取成功
            if not buy_detail:
                decision_logger.error(f"无法获取买单详情，订单ID: {buy_orderid}")
                return False, {"buy_succeed": False, "sell_succeed": None, "action": "complete_failure"}

            if not sell_detail:
                decision_logger.error(f"无法获取卖单详情，订单ID: {sell_orderid}")
                return False, {"buy_succeed": True, "sell_succeed": False, "action": "complete_failure"}


            # 使用真实成交信息
            actual_buy_price = float(buy_detail.get('avg_price', buy_price))
            actual_sell_price = float(sell_detail.get('avg_price', sell_price))
            actual_buy_amount = float(buy_detail.get('exec_qty', buy_amount))
            actual_sell_amount = float(sell_detail.get('exec_qty', sell_amount))

            # 记录真实建仓信息
            decision_logger.info(f"真实建仓信息:")
            decision_logger.info(f'{actual_buy_price},{actual_sell_price}')
            if actual_buy_price == 0:
                actual_buy_price = buy_price
            if actual_sell_price == 0:
                actual_sell_price = sell_price
            decision_logger.info(f'{actual_buy_amount},{actual_sell_amount}')
            if actual_buy_amount == 0:
                actual_buy_amount = buy_amount
            if actual_sell_amount == 0:
                actual_sell_amount = sell_amount
            if actual_buy_price > 0 and actual_sell_price > 0:
                decision_logger.info(f"  买入: {buy_exchange} {actual_buy_amount:.6f} @ {actual_buy_price:.6f}")
                decision_logger.info(f"  卖出: {sell_exchange} {actual_sell_amount:.6f} @ {actual_sell_price:.6f}")
                decision_logger.info(f"  理论价差: {(2 * (sell_price - buy_price) / (sell_price + buy_price)):.6f} 理论买入价：{buy_price} 理论卖出价：{sell_price}")
                decision_logger.info(f"  实际价差: {(2 * (actual_sell_price - actual_buy_price) / (actual_sell_price + actual_buy_price)):.6f} 实际买入价：{actual_buy_price} 实际卖出价：{actual_sell_price}")

            # freeze capitals
            # update balances
            # TODO

            virtual_trade_record = {
                'symbol':symbol,
                'buy_exchange':buy_exchange,
                'buy_price': buy_price,  # 使用真实成交价
                'buy_amount': buy_amount,  # 使用真实成交量
                'sell_exchange':sell_exchange,
                'sell_price': sell_price,  # 使用真实成交价
                'sell_amount': sell_amount,  # 使用真实成交量
                'time_stamp':time.time()
            }

            full_trade_record = {
                'trade_id':str(uuid.uuid4()),  # 转换为字符串
                'action':'open',
                'real':{
                    'buy_record':buy_record,
                    'sell_record':sell_record,
                    'symbol':symbol,
                    'buy_exchange':buy_exchange,
                    'buy_price': actual_buy_price,  # 使用真实成交价
                    'buy_amount': actual_buy_amount,  # 使用真实成交量
                    'sell_exchange':sell_exchange,
                    'sell_price': actual_sell_price,  # 使用真实成交价
                    'sell_amount': actual_sell_amount,  # 使用真实成交量
                },
                'virtual': virtual_trade_record,
                'time_stamp': time.time()
            }
            self.update_and_save(full_trade_record)


            self.active_trades.append(full_trade_record)
            history_log = json.dumps(full_trade_record,indent=4)
            history_logger.info(f'新纪录:\n{history_log}')

            # current_position
            if actual_buy_price > 0 and actual_sell_price > 0:
                actual_spread_pct = (2 * (actual_sell_price - actual_buy_price) / (actual_sell_price + actual_buy_price))
            else:
                actual_spread_pct = 0

            current_position = {
                                "symbol": full_trade_record['real']['symbol'],
                                "best_buy_exchange": full_trade_record['real']['buy_exchange'],
                                "best_buy_price": full_trade_record['real']['buy_price'],
                                "best_sell_exchange": full_trade_record['real']['sell_exchange'],
                                "best_sell_price": full_trade_record['real']['sell_price'],
                                "open_spread_pct": actual_spread_pct,
                                "trade_time": time.time()
                                }
            # redis
            trader_state.redis_client.set("trading:current_position",json.dumps(current_position))

            # balance info update
            for exchange_name in [buy_exchange,sell_exchange]:
                account_usdt_capital = Config.INITIAL_USDT_CAPITALS[exchange_name]
                account_usdt_value, available_usdt_balance = self.exchanges[exchange_name].get_balance_info(True)
                self.portfolio_manager.update_exchange_balance(
                    exchange_name=exchange_name,
                    account_usdt_capital=account_usdt_capital,
                    account_usdt_value=account_usdt_value,
                    available_usdt_balance=available_usdt_balance
                )
    
            # 3. (可选但推荐) 更新后，立即将最新的整体状态持久化到Redis
            latest_state = self.portfolio_manager.to_dict()
            self.redis_client.set("trading:balance", json.dumps(latest_state))
            return True, full_trade_record
        
        else:
            decision_logger.info('开仓失败')
            hist = {'buy_succeed': buy_succeed,
                        'sell_succeed': sell_succeed}
            history_log = json.dumps(hist)
            history_logger.info(f'新纪录:\n{history_log}')

            # balance info update
            for exchange_name in [buy_exchange,sell_exchange]:
                account_usdt_capital = Config.INITIAL_USDT_CAPITALS[exchange_name]
                account_usdt_value, available_usdt_balance = self.exchanges[exchange_name].get_balance_info(True)
                self.portfolio_manager.update_exchange_balance(
                    exchange_name=exchange_name,
                    account_usdt_capital=account_usdt_capital,
                    account_usdt_value=account_usdt_value,
                    available_usdt_balance=available_usdt_balance
                )
    
            # 3. (可选但推荐) 更新后，立即将最新的整体状态持久化到Redis
            latest_state = self.portfolio_manager.to_dict()
            self.redis_client.set("trading:balance", json.dumps(latest_state))
        
            return False, hist
    
    def close_position(self, symbol, prev_buy_exchange, prev_sell_exchange, close_buy_price, close_sell_price)-> bool:
        """
        prev_buy_exchange: 之前购入symbol (做多) 的交易所
        prev_sell_exchange: 之前出售symbol (做空) 的交易所

        """
        succeed = False

        # 买多少平多少
        trade_amount = self.active_trades[0]['virtual']['buy_amount']
        # ideally
        close_sell_record = {}
        close_buy_record = {}

        close_buy_order_signal = OrderSignal('linear',symbol,'sell','limit',trade_amount,prev_buy_exchange,'close', close_buy_price)
        close_buy_succeed, close_buy_orderid = self.unified_place_order(close_buy_order_signal)

        close_sell_order_signal = OrderSignal('linear',symbol,'buy','limit',trade_amount,prev_sell_exchange,'close', close_sell_price)
        close_sell_succeed, close_sell_orderid = self.unified_place_order(close_sell_order_signal)

        if close_buy_succeed and close_buy_orderid:
            close_buy_record = self.exchanges[close_buy_order_signal.exchange].get_order_detail(close_buy_orderid)
        else:
            decision_logger.error(f"{close_buy_order_signal.exchange} 下单失败或缺少订单ID")

        if close_sell_succeed and close_sell_orderid:
            close_sell_record = self.exchanges[close_sell_order_signal.exchange].get_order_detail(close_sell_orderid)
        else:
            decision_logger.error(f"{close_sell_order_signal.exchange} 下单失败或缺少订单ID")

        if not close_buy_succeed:
            decision_logger.info(f'平多失败, info: {close_buy_record}')

        if not close_sell_succeed:
            decision_logger.info(f'平空失败, info: {close_sell_record}')

        # 使用真实成交信息
        actual_close_buy_price = float(close_buy_record.get('avg_price', close_buy_price))
        actual_close_sell_price = float(close_sell_record.get('avg_price', close_sell_price))
        actual_close_buy_amount = float(close_buy_record.get('exec_qty', trade_amount))
        actual_close_sell_amount = float(close_sell_record.get('exec_qty', trade_amount))


        
        decision_logger.info(f'{actual_close_buy_price},{actual_close_sell_price}')
        if actual_close_buy_price == 0:
            actual_close_buy_price = close_buy_price
        if actual_close_sell_price == 0:
            actual_close_sell_price = close_sell_price
        decision_logger.info(f'{actual_close_buy_price},{actual_close_sell_price}')
        decision_logger.info(f'{actual_close_buy_amount},{actual_close_sell_amount}')
        if actual_close_buy_amount == 0:
            actual_close_buy_amount = trade_amount
        if actual_close_sell_amount == 0:
            actual_close_sell_amount = trade_amount
        decision_logger.info(f'{actual_close_buy_amount},{actual_close_sell_amount}')

        # if all succeed
        succeed = True
        if succeed:
            # free previous frozen capitals
            virtual_trade_record = {
                'symbol': symbol,
                'prev_buy_exchange': prev_buy_exchange,
                'prev_sell_exchange': prev_sell_exchange,
                'buy_exchange': prev_sell_exchange,
                'buy_amount': trade_amount,
                'sell_exchange': prev_buy_exchange,
                'sell_amount': trade_amount,
                'time_stamp':time.time()
            }
            full_trade_record = {
                'trade_id': self.active_trades[0]['trade_id'], 
                'action': 'close',
                'real':{
                    'close_sell_record':close_sell_record,
                    'close_buy_record':close_buy_record,# 使用真实成交量
                    'symbol':symbol,
                    'close_buy_exchange':buy_exchange,
                    'close_buy_price': actual_close_buy_price,  # 使用真实成交价
                    'close_buy_amount': actual_close_buy_amount,  # 使用真实成交量
                    'close_sell_exchange':sell_exchange,
                    'close_sell_price': actual_close_sell_price,  # 使用真实成交价
                    'close_sell_amount': actual_close_sell_amount,  # 使用真实成交量
                },
                'virtual': virtual_trade_record,
                'timestamp':time.time() 
            }
            self.update_and_save(full_trade_record)

            # self.active_trades.pop()
            # history_log = json.dumps(full_trade_record,indent=4)
            # history_logger.info(f'新纪录:\n{history_log}')

            # # redis
            # self.redis_client.delete("trading:current_position")

            # balance info update
            # for exchange_name in [prev_buy_exchange,prev_sell_exchange]:
            #     account_usdt_capital = Config.INITIAL_USDT_CAPITALS[exchange_name]
            #     account_usdt_value, available_usdt_balance = self.exchanges[exchange_name].get_balance_info(True)
            #     self.portfolio_manager.update_exchange_balance(
            #         exchange_name=exchange_name,
            #         account_usdt_capital=account_usdt_capital,
            #         account_usdt_value=account_usdt_value,
            #         available_usdt_balance=available_usdt_balance
            #     )
    
            # 3. (可选但推荐) 更新后，立即将最新的整体状态持久化到Redis
            # latest_state = self.portfolio_manager.to_dict()
            # self.redis_client.set("trading:balance", json.dumps(latest_state))
            return True, full_trade_record
        # balance info update
        for exchange_name in [prev_buy_exchange,prev_sell_exchange]:
            account_usdt_capital = Config.INITIAL_USDT_CAPITALS[exchange_name]
            account_usdt_value, available_usdt_balance = self.exchanges[exchange_name].get_balance_info(True)
            self.portfolio_manager.update_exchange_balance(
                exchange_name=exchange_name,
                account_usdt_capital=account_usdt_capital,
                account_usdt_value=account_usdt_value,
                available_usdt_balance=available_usdt_balance
            )

        # 3. (可选但推荐) 更新后，立即将最新的整体状态持久化到Redis
        latest_state = self.portfolio_manager.to_dict()
        self.redis_client.set("trading:balance", json.dumps(latest_state))
        return False, {"close_buy_succeed":close_buy_succeed,
                       "close_sell_succeed": close_sell_succeed}

    def can_open_positions(self) -> bool:
        return Config.MAX_POSITION_SIZE > len(self.active_trades)
    
    def check_open_conditions(self,max_spread_pct) -> bool:
        """return True of False, if should open -> True"""
        # TODO maybe also check balance
        return max_spread_pct >= Config.MIN_SPREAD_PCT_THRESHOLD
    
    def monitor_active_trades(self):
        for active_trade in self.active_trades:
            # calculate current_spread_pct but fixing the buy_exchange and sell_exchange.
            # have to sell@buy_exchange and buy@sell_exchange to close positions
            symbol = active_trade['virtual']['symbol']
            buy_exchange = active_trade['virtual']['buy_exchange']
            sell_exchange = active_trade['virtual']['sell_exchange']
            bid_from_buy_exchange = float(self.redis_client.get("trading:"+symbol+"@"+buy_exchange+"[bid]"))
            ask_from_sell_exchange = float(self.redis_client.get("trading:"+symbol+"@"+sell_exchange+"[ask]"))
            current_spread, current_spread_pct = calculate_closing_spread(bid_from_buy_exchange, ask_from_sell_exchange)

        return current_spread, current_spread_pct, bid_from_buy_exchange, ask_from_sell_exchange
    
    def check_close_conditions(self, spread_pct):
        """return True, threshold if meet the threshold, else False, threshold"""
        # TODO
        ask_from_buy = self.active_trades[0]['real']['buy_price']
        bid_from_sell = self.active_trades[0]['real']['sell_price']
        open_spread, open_spread_pct = calculate_opening_spread(ask_from_buy, bid_from_sell)
        res = float(open_spread_pct) * float(Config.MAGIC_THRESHOLD)
        return spread_pct < res, res
                
    def unified_place_order(self, order_signal:OrderSignal) :
        # select an exchange and place order
        exchange_api = self.exchanges[order_signal.exchange]

        orderid = None
        succeed = False
        res = None

        # 格式化数量，避免浮点精度问题
        formatted_qty = round(order_signal.quantity, 6)

        if order_signal.exchange == 'Okx':
            # OKX需要将币的数量转换为张数
            contracts_qty = exchange_api.coins_to_contracts(order_signal.symbol, order_signal.quantity)
            if contracts_qty is None or contracts_qty <= 0:
                decision_logger.error(f"OKX单位转换失败: {order_signal.quantity} 币 -> {contracts_qty} 张")
                return False, None

            decision_logger.info(f"OKX下单转换: {order_signal.quantity} 币 -> {contracts_qty} 张")
            # TODO
            if order_signal.orderType == 'market':
                succeed, res = exchange_api.place_order(category=order_signal.category,
                                                symbol=order_signal.symbol,
                                                side=order_signal.side,
                                                orderType=order_signal.orderType,
                                                qty=contracts_qty,action=order_signal.action)
            elif order_signal.orderType == 'limit':
                succeed, res = exchange_api.place_limit_order(category=order_signal.category,
                                                symbol=order_signal.symbol,
                                                side=order_signal.side,
                                                orderType=order_signal.orderType,
                                                qty=contracts_qty,action=order_signal.action,price=order_signal.price)
            if succeed and res:
                orderid = res['data'][0]['clOrdId']
        elif order_signal.exchange == 'Bitget':
            # Bitget使用币的数量，直接下单
            if order_signal.orderType == 'market':
                succeed, res = exchange_api.place_order(category=order_signal.category,
                                                symbol=order_signal.symbol,
                                                side=order_signal.side,
                                                orderType=order_signal.orderType,
                                                qty=formatted_qty,action=order_signal.action)
            elif order_signal.orderType == 'limit':
                succeed, res = exchange_api.place_limit_order(category=order_signal.category,
                                symbol=order_signal.symbol,
                                side=order_signal.side,
                                orderType=order_signal.orderType,
                                qty=formatted_qty,
                                price = order_signal.price,
                                action=order_signal.action)
            if succeed and res:
                orderid = res['data']['clientOid'] # 使用统一的 orderid 字段
        elif order_signal.exchange == 'Binance':
            # Binance使用币的数量，直接下单
            # 
            if order_signal.orderType == 'market':
                succeed, res = exchange_api.place_order(category=order_signal.category,
                                                symbol=order_signal.symbol,
                                                side=order_signal.side,
                                                orderType=order_signal.orderType,
                                                qty=formatted_qty)
            elif order_signal.orderType == 'limit':
                succeed, res = exchange_api.place_limit_order(category=order_signal.category,
                                symbol=order_signal.symbol,
                                side=order_signal.side,
                                orderType=order_signal.orderType,
                                qty=formatted_qty,
                                price=order_signal.price)
            if succeed and res:
                orderid = res['clientOrderId']
        else:
            # Bybit使用币的数量，直接下单
            if order_signal.orderType == 'market':
                succeed, res = exchange_api.place_order(category=order_signal.category,
                                    symbol=order_signal.symbol,
                                    side=order_signal.side,
                                    orderType=order_signal.orderType,
                                    qty=str(formatted_qty))  # Bybit 需要字符串格式
            elif order_signal.orderType == 'limit':
                succeed, res = exchange_api.place_limit_order(category=order_signal.category,
                    symbol=order_signal.symbol,
                    side=order_signal.side,
                    orderType=order_signal.orderType,
                    qty=str(formatted_qty),
                    price=order_signal.price)  # Bybit 需要字符串格式
            if succeed and res:
                orderid = res['result']['orderLinkId']

        return succeed, orderid
        # 只有在下单成功且有订单ID时才查询订单详情
        # if succeed and orderid:
        #     try:
        #         order_detail = exchange_api.get_order_detail(orderid)
        #         return succeed, order_detail
        #     except Exception as e:
        #         decision_logger.error(f"获取订单详情失败: {e}")
        #         return succeed, res  # 返回原始下单响应
        # else:
        #     decision_logger.error(f"{order_signal.exchange} 下单失败或缺少订单ID")
        #     return succeed, res
    
    def find_opportunity(self,data):
        """return opportunity: symbol, and its spread, buy_exchange(ask), sell_exchange(bid), None if error"""
        if not data:
            decision_logger.warning("❌ 市场数据为空")
            return None, 0, None, None, None

        max_spread_pct = 0
        best_opportunity = None
        best_buy_exchange = None
        best_sell_exchange = None
        best_exchange_data = None

        decision_logger.debug(f"🔍 扫描套利机会，可用symbols: {list(data.keys())}")

        # go over data for each piece:
        for symbol, exchange_data in data.items():
            # 验证exchange_data的完整性
            if not exchange_data or not isinstance(exchange_data, dict):
                decision_logger.warning(f"❌ {symbol} 交易所数据为空或格式错误")
                continue

            # calculate spread, check if it's greatest
            spread, spread_pct,buy_exchange,sell_exchange = calculate_symbol_spread(exchange_data)

            if spread is None:
                decision_logger.debug(f'❌ {symbol} 数据不完整，跳过')
                continue

            if spread_pct > max_spread_pct:
                # 验证交易所数据的可用性
                if (buy_exchange in exchange_data and sell_exchange in exchange_data and
                    exchange_data[buy_exchange].get("ask") is not None and
                    exchange_data[sell_exchange].get("bid") is not None):

                    max_spread_pct = spread_pct
                    best_opportunity = symbol
                    best_buy_exchange = buy_exchange
                    best_sell_exchange = sell_exchange
                    best_exchange_data = exchange_data

                    decision_logger.debug(f"✅ 发现更好机会: {symbol} {spread_pct:.5f}% {buy_exchange}→{sell_exchange}")
                else:
                    decision_logger.warning(f"❌ {symbol} 最佳交易所数据不完整: {buy_exchange}, {sell_exchange}")

        # 安全检查：确保找到了有效的套利机会
        if best_opportunity is not None and best_buy_exchange is not None and best_sell_exchange is not None:
            try:
                latest_opportunity = {
                    "symbol": best_opportunity,
                    "best_buy_exchange": best_buy_exchange,
                    "best_buy_price": data[best_opportunity][best_buy_exchange]["ask"],
                    "best_sell_exchange": best_sell_exchange,
                    "best_sell_price": data[best_opportunity][best_sell_exchange]["bid"],
                    "open_spread_pct": max_spread_pct,
                    "time_stamp_opportunity": time.time()
                }
                self.redis_client.set("trading:latest_opportunity",json.dumps(latest_opportunity))
            except KeyError as e:
                decision_logger.error(f"❌ 套利机会数据访问错误: {e}")
                decision_logger.error(f"   best_opportunity: {best_opportunity}")
                decision_logger.error(f"   best_buy_exchange: {best_buy_exchange}")
                decision_logger.error(f"   best_sell_exchange: {best_sell_exchange}")
                decision_logger.error(f"   可用数据: {list(data.keys()) if data else 'None'}")
                return None, 0, None, None, None

        return best_opportunity, max_spread_pct, best_buy_exchange, best_sell_exchange,best_exchange_data



    def parse_order_result(self, exchange_name, order_result):
        """解析各交易所的订单结果，提取成交价格和数量
        返回: (avg_price, filled_qty, order_id, status)
        """
        if not order_result:
            return None, None, None, 'FAILED'

        try:
            if exchange_name == 'Binance':
                # Binance 返回格式
                avg_price = float(order_result.get('avgPrice', '0'))
                filled_qty = float(order_result.get('executedQty', '0'))
                order_id = order_result.get('orderId')
                status = order_result.get('status', 'UNKNOWN')
                return avg_price, filled_qty, order_id, status

            elif exchange_name == 'Bybit':
                # Bybit 返回格式
                if order_result.get('retCode') == 0 and order_result.get('result'):
                    result = order_result['result']
                    avg_price = float(result.get('avgPrice', '0'))
                    filled_qty = float(result.get('cumExecQty', '0'))
                    order_id = result.get('orderId')
                    status = result.get('orderStatus', 'UNKNOWN')
                    return avg_price, filled_qty, order_id, status

            elif exchange_name == 'Okx':
                # OKX 返回格式
                if order_result.get('code') == '0' and order_result.get('data'):
                    data = order_result['data'][0]
                    # OKX 下单后需要查询订单详情获取成交信息
                    order_id = data.get('ordId')
                    # 这里返回订单ID，实际成交信息需要后续查询
                    return None, None, order_id, 'PENDING'

            elif exchange_name == 'Bitget':
                # Bitget v2 返回格式
                if order_result.get('code') == '00000' and order_result.get('data'):
                    data = order_result['data']
                    order_id = data.get('orderId')
                    # Bitget 下单后也需要查询订单详情
                    return None, None, order_id, 'PENDING'

        except Exception as e:
            decision_logger.error(f"解析{exchange_name}订单结果失败: {e}")

        return None, None, None, 'FAILED'

    def get_order_details(self, exchange_name, exchange_api, order_id, symbol):
        """查询订单详情获取真实成交信息
        返回: (avg_price, filled_qty, status)
        """
        if not order_id:
            return None, None, 'FAILED'

        try:
            if exchange_name == 'Okx':
                # OKX 查询订单详情
                symbol_converted = exchange_api._symbol_convert(symbol)
                response = exchange_api.session.trade.get_order(instId=symbol_converted, ordId=order_id)
                if response.get('code') == '0' and response.get('data'):
                    order_data = response['data'][0]
                    avg_price = float(order_data.get('avgPx', '0'))
                    filled_qty = float(order_data.get('accFillSz', '0'))
                    status = order_data.get('state', 'UNKNOWN')
                    return avg_price, filled_qty, status

            elif exchange_name == 'Bitget':
                # Bitget v2 查询订单详情
                symbol_converted = exchange_api._convert_symbol(symbol)
                response = exchange_api.base_api.get("/api/v2/mix/order/detail", {
                    "symbol": symbol_converted,
                    "orderId": order_id
                })
                if response.get('code') == '00000' and response.get('data'):
                    order_data = response['data']
                    avg_price = float(order_data.get('priceAvg', '0'))
                    filled_qty = float(order_data.get('baseVolume', '0'))
                    status = order_data.get('state', 'UNKNOWN')
                    return avg_price, filled_qty, status

        except Exception as e:
            decision_logger.error(f"查询{exchange_name}订单详情失败: {e}")

        return None, None, 'FAILED'

def calculate_symbol_spread(exchange_data):
    """return spread,spread_pct, buy_exchange(min_ask['exchange']),sell_exchange(max_bid['exchange'])
        buy_exchange = exchange that gives ask price.
        """
    # find max ask min bid 对应的交易所
    # 在最便宜的卖家 买入
    # 在出价最高的买家 卖出
    max_bid = {"exchange": None, "price": Config.NEG_INF}
    min_ask = {"exchange": None, "price": Config.POS_INF}

    for exchange, data in exchange_data.items():

        if data["bid"] is not None and data["bid"] > max_bid["price"]:
            max_bid = {"exchange": exchange, "price": data["bid"]}
 
        # 检查卖价是否存在且比当前最小值低
        if data["ask"] is not None and data["ask"] < min_ask["price"]:
            min_ask = {"exchange": exchange, "price": data["ask"]}

    if max_bid['price'] is not Config.NEG_INF and min_ask['price'] is not Config.POS_INF:
        max_bid_price = max_bid['price']
        min_ask_price = min_ask['price']
        spread = max_bid_price - min_ask_price
        spread_pct = 2 * spread / (max_bid_price + min_ask_price)
        return spread,spread_pct, min_ask['exchange'], max_bid['exchange']
    
    decision_logger.info("calculate_symbol_spread error")
    return None, None, None, None
    
    # calculate_spread(max_bid,min_ask)


def calculate_spread(price_a, price_b):
    spread = price_a - price_b
    spread_pct = 2 * spread / (price_a + price_b)
    return spread, spread_pct

def calculate_opening_spread(ask_from_buy, bid_from_sell):
    #这个有可能反了 # 复查结束 ok
    spread =  bid_from_sell - ask_from_buy
    spread_pct = 2 * spread / (bid_from_sell + ask_from_buy)
    return spread, spread_pct

def calculate_closing_spread(bid_from_prev_buy, ask_from_prev_sell):
    #这个有可能反了需要复查 #复查结束 ok 确实反了
    spread =  ask_from_prev_sell - bid_from_prev_buy
    spread_pct =2 * spread / (bid_from_prev_buy + ask_from_prev_sell)
    return spread, spread_pct

def connect_to_sim_bybit(trader_state:TraderState):
    exchange_name = 'Bybit'
    bybit_exchange = BybitExchangeHTTP('Bybit', api_key=EXCHANGE_API_KEYS['bybit']['api_key'],api_secret=EXCHANGE_API_KEYS['bybit']['secret'],isTest=True)

    is_connected = bybit_exchange.connect()
    if is_connected:
        decision_logger.info(f'Bybit session is connected.')

    trader_state.exchanges['Bybit'] = bybit_exchange
    total_equity, total_available_balance = bybit_exchange.get_balance_info()
    if total_equity is None or total_available_balance is None:
        return False
    
    #新增
    # trader_state.portfolio_manager.update_exchange_balance(exchange_name,Config.INITIAL_USDT_CAPITALS['Bybit'],total_available_balance)

    trader_state.balance_info["exchange_balances"][exchange_name]['available'] = total_available_balance
    decision_logger.info(f'{total_equity}, {total_available_balance}')
    if total_available_balance:
        decision_logger.info(f'Bybit 剩余可用资金: {total_available_balance}')
        return True
    else:
        decision_logger.info(f'Bybit 剩余可用资金查询失败')
        # 不应该能到这里
        return False

    # 示例， 买比特币
    # bybit_exchange.place_order(category="linear",symbol="BTCUSDT",side="buy",orderType="market",qty="0.001")

def connect_to_real_bybit(trader_state):
    bybit_exchange = BybitExchangeHTTP('Bybit', api_key=EXCHANGE_API_KEYS['bybit']['api_key'],api_secret=EXCHANGE_API_KEYS['bybit']['secret']) 
    is_connected = bybit_exchange.connect()
    if is_connected:
        decision_logger.info(f'Bybit已连接')
    trader_state.exchanges['Bybit'] = bybit_exchange
    
    total_equity, total_available_balance = bybit_exchange.get_balance_info()

    decision_logger.info(f'{total_equity}, {total_available_balance}')
    if total_equity is not None:
        decision_logger.info(f'Bybit 剩余可用资金: {total_available_balance}')
    else:
        decision_logger.info(f'Bybit 剩余可用资金查询失败')
    exchange_name = 'Bybit'
    # trader_state.portfolio_manager.update_exchange_balance(exchange_name,Config.INITIAL_USDT_CAPITALS['Bybit'],total_available_balance)

def connect_to_real_bitget(trader_state):
    # demo, 无法连接
    # api_key = "bg_3a6ae37342a327fa91fe66ec806a3231"
    # api_secret = "ab448cbf5be214d5b1b047bee7d922fdf05bf32f61e2be178bce72fcbc536c86"

    # real
    api_key = EXCHANGE_API_KEYS['bitget']['api_key']
    api_secret = EXCHANGE_API_KEYS['bitget']['secret']
    api_passphrase = EXCHANGE_API_KEYS['bitget']['passphrase']

    # client = Client(api_key, api_secret, passphrase=api_passphrase)
    # result = client.mix_get_accounts(productType='UMCBL')
    # decision_logger.info(result)
    # 尝试使用 Bitget v2 API，如果不可用则回退到 v1
    try:
        bitget_exchange = BitgetV2HTTP('Bitget',api_key=api_key,api_secret=api_secret,passphrase=api_passphrase)
        decision_logger.info("使用 Bitget v2 API")
    except Exception as e:
        decision_logger.warning(f"Bitget v2 API 不可用，回退到 v1: {e}")
        bitget_exchange = Bitgetv1HTTP('Bitget',api_key=api_key,api_secret=api_secret,passphrase=api_passphrase)
    is_connected = bitget_exchange.connect()
    if is_connected:
        decision_logger.info(f'Bitget已连接')
    trader_state.exchanges['Bitget'] = bitget_exchange
    
    total_equity, total_available_balance = bitget_exchange.get_balance_info()
    
    if total_equity is not None:
        decision_logger.info(f'Bitget 剩余可用资金: {total_available_balance}')
    else:
        decision_logger.info(f'Bitget 剩余可用资金查询失败')
    exchange_name = 'Bitget'
    # trader_state.portfolio_manager.update_exchange_balance(exchange_name,Config.INITIAL_USDT_CAPITALS['Bitget'],total_available_balance)
        
    # succ, place_order_response = bitget_exchange.place_order('linear','BTCUSDT','buy','market','0.001')
    # decision_logger.info(place_order_response)

def connect_to_sim_bitget_v1(trader_state:TraderState):

    api_key = EXCHANGE_API_KEYS['bitget']['api_key']
    api_secret = EXCHANGE_API_KEYS['bitget']['secret']
    api_passphrase = EXCHANGE_API_KEYS['bitget']['passphrase']

    # client = Client(api_key, api_secret, passphrase=api_passphrase)
    # result = client.mix_get_accounts(productType='UMCBL')
    # decision_logger.info(result)
    # 尝试使用 Bitget v2 API，如果不可用则回退到 v1
    try:
        bitget_exchange = BitgetV2HTTP('Bitget',api_key=api_key,api_secret=api_secret,passphrase=api_passphrase)
        decision_logger.info("使用 Bitget v2 API")
    except Exception as e:
        decision_logger.warning(f"Bitget v2 API 不可用，回退到 v1: {e}")
        bitget_exchange = Bitgetv1HTTP('Bitget',api_key=api_key,api_secret=api_secret,passphrase=api_passphrase)
    is_connected = bitget_exchange.connect()
    if is_connected:
        decision_logger.info(f'Bitget已连接')
    trader_state.exchanges['Bitget'] = bitget_exchange
    
    total_equity, total_available_balance = bitget_exchange.get_balance_info()
    if total_equity is not None:
        decision_logger.info(f'Bitget 剩余可用资金: {total_available_balance}')
    else:
        decision_logger.info(f'Bitget 剩余可用资金查询失败')
        
    # succeed, place_order_response = bitget_exchange.place_order('linear','BTCUSDT','buy','market','0.001')
    # decision_logger.info(place_order_response)

def connect_to_okx(trader_state):
        # 模拟
    exchange_name = 'Okx'

    # api = OkxRestClient('85919a48-8a5a-484c-9b3a-e0403d469b1f', '965C4759E597A89C83FA08D6B4DF4AC1', 'cntest001@O',simulation=True)
    okx_exchange = OkxExchangeHTTP('Okx',EXCHANGE_API_KEYS['okx']['api_key'], EXCHANGE_API_KEYS['okx']['secret'], EXCHANGE_API_KEYS['okx']['passphrase'])

    # okx_exchange = OkxExchangeHTTP('Okx','ecc247b6-7c2d-4d97-a751-81c0fb828a6b', '3A67F1CA4D528D5010880BD0E37348E6', 'cntest001@O')
    okx_exchange.connect()
    trader_state.exchanges['Okx'] = okx_exchange
    total_equity, total_available_balance = okx_exchange.get_balance_info()

    if total_equity is not None:
        decision_logger.info(f'Okx 剩余可用资金: {total_available_balance}')
    else:
        decision_logger.info(f'Okx 剩余可用资金查询失败')
    # trader_state.portfolio_manager.update_exchange_balance(exchange_name,Config.INITIAL_USDT_CAPITALS['Okx'],total_available_balance)

    # ret =okx_exchange.place_order('linear','BTCUSDT','buy','market','0.01')
    # ret_code = ret['code']
    # ret_message = ret['data'][0]['sMsg']
    # decision_logger.info('hello')

def connect_to_binance(trader_state):
    binance_exchange = BinanceExchangeHTTP('Binance',EXCHANGE_API_KEYS['binance']['api_key'], EXCHANGE_API_KEYS['binance']['secret'])
    binance_exchange.connect()
    trader_state.exchanges['Binance'] = binance_exchange
    total_equity, total_available_balance = binance_exchange.get_balance_info()
    if total_equity is not None:
        decision_logger.info(f'Binance 剩余可用资金: {total_available_balance}')
    else:
        decision_logger.info(f'Binance 剩余可用资金查询失败')
    exchange_name = 'Binance'
    # trader_state.portfolio_manager.update_exchange_balance(exchange_name,Config.INITIAL_USDT_CAPITALS['Binance'],total_available_balance)

def connect_to_sim_okx(trader_state:TraderState):
        # 模拟
    # api = OkxRestClient('85919a48-8a5a-484c-9b3a-e0403d469b1f', '965C4759E597A89C83FA08D6B4DF4AC1', 'cntest001@O',simulation=True)
    exchange_name = 'Okx'
    okx_exchange = OkxExchangeHTTP(exchange_name,EXCHANGE_API_KEYS['okx']['api_key'], EXCHANGE_API_KEYS['okx']['secret'], EXCHANGE_API_KEYS['okx']['passphrase'], isTest=True)

    # okx_exchange = OkxExchangeHTTP('Okx','ecc247b6-7c2d-4d97-a751-81c0fb828a6b', '3A67F1CA4D528D5010880BD0E37348E6', 'cntest001@O')
    okx_exchange.connect()
    trader_state.exchanges['Okx'] = okx_exchange
    total_equity, total_available_balance = okx_exchange.get_balance_info()

    if total_equity is None or total_available_balance is None:
        return False
    
    trader_state.set_balance(exchange_name,total_available_balance)
    trader_state.calculate_total_exchange_capital(exchange_name)
    if total_equity:
        decision_logger.info(f'Okx 剩余可用资金: {total_available_balance}')
        return True
    else:
        decision_logger.info(f'Okx 剩余可用资金查询失败')
        return False


def connect_to_exchanges(trader_state:TraderState) ->  bool:
    if not connect_to_sim_bybit(trader_state):
        return False
    # connect_to_sim_bitget_v1(trader_state)
    if not connect_to_sim_okx(trader_state):
        return False

    return True

if __name__ == "__main__":
    
    trader_state = TraderState()
    connect_to_real_bybit(trader_state)
    connect_to_real_bitget(trader_state)
    connect_to_okx(trader_state)
    connect_to_binance(trader_state)
    

    # 初始化缓存 - 在主循环开始前建立缓存
    decision_logger.info("=== 初始化缓存系统 ===")

    # 1. 加载所有符号信息
    decision_logger.info("加载符号信息缓存...")
    trader_state.load_all_symbol_info()

    # 2. 刷新余额缓存
    decision_logger.info("初始化余额缓存...")
    trader_state.refresh_all_balances()

    decision_logger.info("=== 缓存系统初始化完成，开始交易循环 ===")

    # accounts conncected
    ts_limit = None
    record = None
    buy_order_is_closed = False
    sell_order_is_closed = False
    ts_balance_update = time.time()
    while True:
        if trader_state.can_open_positions():
            best_spread = realtime_data_manager.get_best_spread()
            # {"symbol": symbol, "spread": None, "buy_exchange": None, "sell_exchange": None, "buy_price": None, "sell_price": None}
            if best_spread["spread"] is not None and trader_state.check_open_conditions(best_spread["spread"]):
                symbol = best_spread["symbol"]
                spread = best_spread["spread"]
                buy_exchange = best_spread["buy_exchange"]
                sell_exchange = best_spread["sell_exchange"]
                buy_price = best_spread["buy_price"]
                sell_price = best_spread["sell_price"]
                decision_logger.info(f'符合开仓条件，当前开仓价差比:{spread:.5f}'
                                    f'币种: {symbol}. 买@{buy_exchange}, buy_price:{buy_price}.'
                                    f'卖@{sell_exchange}, sell_price:{sell_price}.')

                ts_before_op = time.time()
                success, record = trader_state.open_position(symbol, buy_exchange, sell_exchange, buy_price, sell_price)
                ts_after_op = time.time()
                ts_limit = ts_after_op
                decision_logger.info(f'时间：ts_exchange: {time.strftime('%Y-%m-%d %H:%M:%S.%f', time.localtime(best_spread["ts_exchange"]))} ts_flush: {time.strftime('%Y-%m-%d %H:%M:%S.%f', time.localtime(best_spread["ts_flush"]))} ts_redis_stored: {time.strftime('%Y-%m-%d %H:%M:%S.%f', time.localtime(best_spread["last_updated"]))} ts_before_op: {time.strftime('%Y-%m-%d %H:%M:%S.%f', time.localtime(ts_before_op))} ts_after_op: {time.strftime('%Y-%m-%d %H:%M:%S.%f', time.localtime(ts_after_op))}')
                decision_logger.info(f'耗时：队列: {best_spread["ts_flush"] - best_spread["ts_exchange"]}s before_redis: {best_spread["last_updated"] - best_spread["ts_flush"]}s redis: {ts_before_op - best_spread["last_updated"]}s op: {ts_after_op - ts_before_op}s')
                if success:
                    decision_logger.info(f'开仓成功')
                else:
                    decision_logger.info(f'开仓失败')
                    # break
        else:
            # 从redis中获取限价单状态 若单边成交 则取消未成交一方 改为市价下单
            if not buy_order_is_closed and not sell_order_is_closed:
                trade_id = record['trade_id']
                symbol = record['real']['symbol']
                buy_exchange = record['real']['buy_exchange']
                sell_exchange = record['real']['sell_exchange']
                buy_amount = record['real']['buy_amount']
                sell_amount = record['real']['sell_amount']
                buy_order_id = record['real']['buy_record']['orderid']
                sell_order_id = record['real']['sell_record']['orderid']
                buy_order_qty = trader_state.redis_client.get(f"trading:order:{symbol}:{buy_exchange}:buy")
                sell_order_qty = trader_state.redis_client.get(f"trading:order:{symbol}:{sell_exchange}:sell")
                buy_order_qty = float(buy_order_qty or 0)
                sell_order_qty = float(sell_order_qty or 0)
                if buy_order_qty == 0 and sell_order_qty == 0:
                    # decision_logger.info('双边限价单建仓均未成交，继续等待')
                    continue
                elif buy_order_qty == buy_amount and sell_order_qty == 0:
                    rtn_cancel_sell = trader_state._attempt_cancel_order(sell_exchange, sell_order_id)
                    sell_order_signal = OrderSignal('linear',symbol,'sell','market',buy_order_qty,sell_exchange,'open')
                    sell_succeed, sell_orderid = trader_state.unified_place_order(sell_order_signal)
                    if sell_succeed and sell_orderid:
                        sell_record = trader_state.exchanges[sell_exchange].get_order_detail(sell_orderid)
                        record['real']['sell_record'] = sell_record
                        record['real']['sell_amount'] = float(sell_record['exec_qty'])
                        record['real']['sell_price'] = float(sell_record['avg_price'])
                        trader_state.update_and_save(record)
                        current_position = {
                            "symbol": record['real']['symbol'],
                            "best_buy_exchange": record['real']['buy_exchange'],
                            "best_buy_price": record['real']['buy_price'],
                            "best_sell_exchange": record['real']['sell_exchange'],
                            "best_sell_price": record['real']['sell_price'],
                            "open_spread_pct": (2 * (record['real']['sell_price'] - record['real']['buy_price']) / (record['real']['buy_price'] + record['real']['sell_price'])),
                            "trade_time": time.time()
                        }
                        # redis
                        trader_state.redis_client.set("trading:current_position",json.dumps(current_position))
                        buy_order_is_closed = True
                        sell_order_is_closed = True
                        decision_logger.info('多单限价单已成交，空单改市价单建仓')
                        trader_state.redis_client.delete(f"trading:order:{symbol}:{buy_exchange}:buy")
                        trader_state.redis_client.delete(f"trading:order:{symbol}:{sell_exchange}:sell")
                elif buy_order_qty == 0 and sell_order_qty == sell_amount:
                    rtn_cancel_buy = trader_state._attempt_cancel_order(buy_exchange, buy_order_id)
                    buy_order_signal = OrderSignal('linear',symbol,'buy','market',sell_order_qty,buy_exchange,'open')
                    buy_succeed, buy_orderid = trader_state.unified_place_order(buy_order_signal)
                    if buy_succeed and buy_orderid:
                        buy_record = trader_state.exchanges[buy_exchange].get_order_detail(buy_orderid)
                        record['real']['buy_record'] = buy_record
                        record['real']['buy_amount'] = float(buy_record['exec_qty'])
                        record['real']['buy_price'] = float(buy_record['avg_price'])
                        trader_state.update_and_save(record)
                        current_position = {
                            "symbol": record['real']['symbol'],
                            "best_buy_exchange": record['real']['buy_exchange'],
                            "best_buy_price": record['real']['buy_price'],
                            "best_sell_exchange": record['real']['sell_exchange'],
                            "best_sell_price": record['real']['sell_price'],
                            "open_spread_pct": (2 * (record['real']['sell_price'] - record['real']['buy_price']) / (record['real']['buy_price'] + record['real']['sell_price'])),
                            "trade_time": time.time()
                        }
                        # redis
                        trader_state.redis_client.set("trading:current_position",json.dumps(current_position))
                        buy_order_is_closed = True
                        sell_order_is_closed = True
                        decision_logger.info('空单限价单已成交，多单改市价单建仓')
                        trader_state.redis_client.delete(f"trading:order:{symbol}:{buy_exchange}:buy")
                        trader_state.redis_client.delete(f"trading:order:{symbol}:{sell_exchange}:sell")
                elif buy_order_qty == buy_amount and sell_order_qty == sell_amount:
                    buy_order_is_closed = True
                    sell_order_is_closed = True
                    decision_logger.info('双边限价单建仓均成交')
                    trader_state.redis_client.delete(f"trading:order:{symbol}:{buy_exchange}:buy")
                    trader_state.redis_client.delete(f"trading:order:{symbol}:{sell_exchange}:sell")
                else: # 其他部分建仓的情况先全取消挂单、平仓处理
                    rtn_cancel_buy = trader_state._attempt_cancel_order(buy_exchange, buy_order_id)
                    rtn_cancel_sell = trader_state._attempt_cancel_order(sell_exchange, sell_order_id)
                    close_buy_order_signal = OrderSignal('linear',symbol,'sell','market',buy_order_qty,buy_exchange,'close')
                    close_buy_succeed, close_buy_orderid = trader_state.unified_place_order(close_buy_order_signal)
                    close_sell_order_signal = OrderSignal('linear',symbol,'buy','market',sell_order_qty,sell_exchange,'close')
                    close_sell_succeed, close_sell_orderid = trader_state.unified_place_order(close_sell_order_signal)
                    trader_state.active_trades.pop()
                    trader_state.redis_client.delete("trading:current_position")
                    buy_order_is_closed = False
                    sell_order_is_closed = False
                    decision_logger.info('双边限价单均未完全成交，全取消并平仓，等待下一次机会')
                    trader_state.redis_client.delete(f"trading:order:{symbol}:{buy_exchange}:buy")
                    trader_state.redis_client.delete(f"trading:order:{symbol}:{sell_exchange}:sell")

            if buy_order_is_closed and sell_order_is_closed:
                # decision_logger.info('双边限价单建仓均成交')
                # decision_logger.info(f'有活跃仓位')
                #TODO 暂时设定：开仓的话这个循环不监视现有仓位，因为我们现在没有超过1仓位并且我想要避免加入仓位之后立刻监视，因为数据还未刷新
                # 假设数据已经刷新
                # monitor active trades
                current_spread, current_spread_pct, bid_from_buy_exchange, ask_from_sell_exchange = trader_state.monitor_active_trades()
                # check close_conditions
                
                should_close_position, target_threshold = trader_state.check_close_conditions(current_spread_pct)
                # decision_logger.info(f'当前清仓价差比:{current_spread_pct:.5f} target_threshold: {target_threshold:.6f}')
                if should_close_position:

                    decision_logger.info(f'符合平仓条件, 条件: current_spread_pct: {current_spread_pct:.6f} <= target_threshold: {target_threshold:.6f}')
                    # close_postion
                    vrecord = trader_state.active_trades[0]['virtual']
                    symbol = vrecord['symbol']
                    prev_buy_exchange = vrecord['buy_exchange']
                    prev_sell_exchange = vrecord['sell_exchange']

                    success, close_info = trader_state.close_position(symbol,prev_buy_exchange,prev_sell_exchange, bid_from_buy_exchange, ask_from_sell_exchange)
                    # buy_order_is_closed = False
                    # sell_order_is_closed = False
                    if success:
                        decision_logger.info(f'平仓成功')
                    else:
                        decision_logger.info(f'close info: {close_info}')
                    decision_logger.info(f'真实平仓情况：')
                    decision_logger.info(f'平多：{close_info["real"]["close_buy_exchange"]} {close_info["real"]["close_buy_amount"]} @ {bid_from_buy_exchange}')
                    decision_logger.info(f'平空：{close_info["real"]["close_sell_exchange"]} {close_info["real"]["close_sell_amount"]} @ {ask_from_sell_exchange}')
                    decision_logger.info(f'理论价差：{current_spread_pct:.6f} 理论平多价：{bid_from_buy_exchange} 理论平空价：{ask_from_sell_exchange}')
                    decision_logger.info(f'实际价差：{2 * (close_info["real"]["close_sell_price"] - close_info["real"]["close_buy_price"]) / (close_info["real"]["close_sell_price"] + close_info["real"]["close_buy_price"]):.6f} 实际平多价：{close_info["real"]["close_buy_price"]} 实际平空价：{close_info["real"]["close_sell_price"]}')

                    
                    ts_close_limit = time.time()
                    close_record = close_info
                    close_buy_order_is_closed = False
                    close_sell_order_is_closed = False
                    while True:
                        ts_now = time.time()
                        if ts_now - ts_close_limit < Config.CP_WAIT_TIME:
                            continue
                        else:
                            if not close_buy_order_is_closed and not close_sell_order_is_closed:
                                close_buy_record = trader_state.exchanges[record['real']['buy_exchange']].get_order_detail(close_record['real']['close_buy_record']['orderid'])
                                close_sell_record = trader_state.exchanges[record['real']['sell_exchange']].get_order_detail(close_record['real']['close_sell_record']['orderid'])
                                if close_buy_record['status'] == 'filled' or close_buy_record['status'] == 'Filled' or close_buy_record['status'] == 'FILLED':
                                    decision_logger.info(f'close buy order filled')
                                    close_buy_order_is_closed = True
                                elif close_buy_record['status'] == 'canceled' or close_buy_record['status'] == 'Canceled' or close_buy_record['status'] == 'CANCELLED':
                                    decision_logger.info(f'close buy order already cancelled')
                                else:
                                    decision_logger.info(f'close_buy_record: {close_buy_record}')
                                    rtn_cancel_close_buy = trader_state._attempt_cancel_order(record['real']['buy_exchange'], close_record['real']['close_buy_record']['orderid'])
                                    if rtn_cancel_close_buy:
                                        decision_logger.info(f'close buy order cancelled')
                                
                                if close_sell_record['status'] == 'filled' or close_sell_record['status'] == 'Filled' or close_sell_record['status'] == 'FILLED':
                                    decision_logger.info(f'close sell order filled')
                                    close_sell_order_is_closed = True
                                elif close_sell_record['status'] == 'canceled' or close_sell_record['status'] == 'Canceled' or close_sell_record['status'] == 'CANCELLED':
                                    decision_logger.info(f'close sell order already cancelled')
                                else:
                                    decision_logger.info(f'close_sell_record: {close_sell_record}')
                                    rtn_cancel_close_sell = trader_state._attempt_cancel_order(record['real']['sell_exchange'], close_record['real']['close_sell_record']['orderid'])
                                    if rtn_cancel_close_sell:
                                        decision_logger.info(f'close sell order cancelled')

                        if not close_buy_order_is_closed and not close_sell_order_is_closed:
                            # trader_state.active_trades.pop()
                            # trader_state.redis_client.delete("trading:current_position")
                            decision_logger.info('双边限价单平仓均未成交，回退至判断价差收敛逻辑')
                            break
                        elif close_buy_order_is_closed and not close_sell_order_is_closed:
                            close_sell_order_signal = OrderSignal('linear',symbol,'buy','market',record['real']['sell_amount'],record['real']['sell_exchange'],'close')
                            close_sell_succeed, close_sell_orderid = trader_state.unified_place_order(close_sell_order_signal)
                            if len(trader_state.active_trades) > 0:
                                trader_state.active_trades.pop()
                            trader_state.redis_client.delete("trading:current_position")
                            close_buy_order_is_closed = True
                            close_sell_order_is_closed = True
                            decision_logger.info('做多限价单平仓未成交，做空限价单平仓成交，市价全平回到建仓判断逻辑')
                            break
                        elif not close_buy_order_is_closed and close_sell_order_is_closed:
                            close_buy_order_signal = OrderSignal('linear',symbol,'sell','market',record['real']['buy_amount'],record['real']['buy_exchange'],'close')
                            close_buy_succeed, close_buy_orderid = trader_state.unified_place_order(close_buy_order_signal)
                            if len(trader_state.active_trades) > 0:
                                trader_state.active_trades.pop()
                            trader_state.redis_client.delete("trading:current_position")
                            close_buy_order_is_closed = True
                            close_sell_order_is_closed = True
                            decision_logger.info('做多限价单平仓成交，做空限价单平仓未成交，市价全平回到建仓判断逻辑')
                            break
                        elif close_buy_order_is_closed and close_sell_order_is_closed:
                            decision_logger.info('双边限价单平仓均成交，回到建仓判断逻辑')
                            break

                        sleep(0.01)
                    
                    if close_buy_order_is_closed and close_sell_order_is_closed:
                        buy_order_is_closed = False
                        sell_order_is_closed = False
                        if len(trader_state.active_trades) > 0:
                            trader_state.active_trades.pop()
                        history_log = json.dumps(close_record,indent=4)
                        history_logger.info(f'新纪录:\n{history_log}')
                        
                        # redis
                        trader_state.redis_client.delete("trading:current_position")
                        # break
                    # break

            continue

            ts_now = time.time()
            if ts_now - ts_limit < Config.OP_WAIT_TIME:
                continue
            else:
                # record = {
                #     'trade_id':str(uuid.uuid4()),  # 转换为字符串
                #     'action':'open',
                #     'real':{
                #         'buy_record':buy_record,
                #         'sell_record':sell_record,
                #         'symbol':symbol,
                #         'buy_exchange':buy_exchange,
                #         'buy_price': actual_buy_price,  # 使用真实成交价
                #         'buy_amount': actual_buy_amount,  # 使用真实成交量
                #         'sell_exchange':sell_exchange,
                #         'sell_price': actual_sell_price,  # 使用真实成交价
                #         'sell_amount': actual_sell_amount,  # 使用真实成交量
                #     },
                #     'virtual': virtual_trade_record,
                #     'time_stamp': time.time()
                # }
                if not buy_order_is_closed and not sell_order_is_closed:
                    # rtn_cancel_buy, rsp_cancel_buy = trader_state._attempt_cancel_order(record['real']['buy_exchange'], record['real']['buy_record']['orderid'])
                    # rtn_cancel_sell, rsp_cancel_sell = trader_state._attempt_cancel_order(record['real']['sell_exchange'], record['real']['sell_record']['orderid'])


                    buy_record = trader_state.exchanges[record['real']['buy_exchange']].get_order_detail(record['real']['buy_record']['orderid'])
                    sell_record = trader_state.exchanges[record['real']['sell_exchange']].get_order_detail(record['real']['sell_record']['orderid'])
                    if buy_record['status'] == 'filled' or buy_record['status'] == 'Filled' or buy_record['status'] == 'FILLED':
                        decision_logger.info(f'buy order filled')
                        buy_order_is_closed = True
                    elif buy_record['status'] == 'canceled' or buy_record['status'] == 'Canceled' or buy_record['status'] == 'CANCELLED':
                        decision_logger.info(f'buy order already cancelled')
                    else:
                        decision_logger.info(f'buy_record: {buy_record}')
                        rtn_cancel_buy = trader_state._attempt_cancel_order(record['real']['buy_exchange'], record['real']['buy_record']['orderid'])
                        if rtn_cancel_buy:
                            decision_logger.info(f'buy order cancelled')
                    
                    if sell_record['status'] == 'filled' or sell_record['status'] == 'Filled' or sell_record['status'] == 'FILLED':
                        decision_logger.info(f'sell order filled')
                        sell_order_is_closed = True
                    elif sell_record['status'] == 'canceled' or sell_record['status'] == 'Canceled' or sell_record['status'] == 'CANCELLED':
                        decision_logger.info(f'sell order already cancelled')
                    else:
                        decision_logger.info(f'sell_record: {sell_record}')
                        rtn_cancel_sell = trader_state._attempt_cancel_order(record['real']['sell_exchange'], record['real']['sell_record']['orderid'])
                        if rtn_cancel_sell:
                            decision_logger.info(f'sell order cancelled')

            if not buy_order_is_closed and not sell_order_is_closed:
                trader_state.active_trades.pop()
                trader_state.redis_client.delete("trading:current_position")
                buy_order_is_closed = False
                sell_order_is_closed = False
                decision_logger.info('双边限价单建仓均未成交，重新找币对建仓')
            elif buy_order_is_closed and not sell_order_is_closed:
                close_buy_order_signal = OrderSignal('linear',symbol,'sell','market',record['real']['buy_amount'],record['real']['buy_exchange'],'close')
                close_buy_succeed, close_buy_orderid = trader_state.unified_place_order(close_buy_order_signal)
                trader_state.active_trades.pop()
                trader_state.redis_client.delete("trading:current_position")
                buy_order_is_closed = False
                sell_order_is_closed = False
                decision_logger.info('做多限价单建仓成交，做空限价单建仓未成交，重新找币对建仓')
                # break
            elif not buy_order_is_closed and sell_order_is_closed:
                close_sell_order_signal = OrderSignal('linear',symbol,'buy','market',record['real']['sell_amount'],record['real']['sell_exchange'],'close')
                close_sell_succeed, close_sell_orderid = trader_state.unified_place_order(close_sell_order_signal)
                trader_state.active_trades.pop()
                trader_state.redis_client.delete("trading:current_position")
                buy_order_is_closed = False
                sell_order_is_closed = False
                decision_logger.info('做空限价单建仓成交，做多限价单建仓未成交，重新找币对建仓')
                # break
            elif buy_order_is_closed and sell_order_is_closed:
                # decision_logger.info('双边限价单建仓均成交')
                # decision_logger.info(f'有活跃仓位')
                #TODO 暂时设定：开仓的话这个循环不监视现有仓位，因为我们现在没有超过1仓位并且我想要避免加入仓位之后立刻监视，因为数据还未刷新
                # 假设数据已经刷新
                # monitor active trades
                current_spread, current_spread_pct, bid_from_buy_exchange, ask_from_sell_exchange = trader_state.monitor_active_trades()
                # check close_conditions
                # decision_logger.info(f'当前清仓价差比:{current_spread_pct:.5f}')
                
                should_close_position, target_threshold = trader_state.check_close_conditions(current_spread_pct)
                if should_close_position:

                    decision_logger.info(f'符合平仓条件, 条件: current_spread_pct: {current_spread_pct:.6f} <= target_threshold: {target_threshold:.6f}')
                    # close_postion
                    vrecord = trader_state.active_trades[0]['virtual']
                    symbol = vrecord['symbol']
                    prev_buy_exchange = vrecord['buy_exchange']
                    prev_sell_exchange = vrecord['sell_exchange']

                    success, close_info = trader_state.close_position(symbol,prev_buy_exchange,prev_sell_exchange, bid_from_buy_exchange, ask_from_sell_exchange)
                    # buy_order_is_closed = False
                    # sell_order_is_closed = False
                    if success:
                        decision_logger.info(f'平仓成功')
                    else:
                        decision_logger.info(f'close info: {close_info}')
                    decision_logger.info(f'真实平仓情况：')
                    decision_logger.info(f'平多：{close_info["real"]["close_buy_exchange"]} {close_info["real"]["close_buy_amount"]} @ {bid_from_buy_exchange}')
                    decision_logger.info(f'平空：{close_info["real"]["close_sell_exchange"]} {close_info["real"]["close_sell_amount"]} @ {ask_from_sell_exchange}')
                    decision_logger.info(f'理论价差：{current_spread_pct:.6f} 理论平多价：{bid_from_buy_exchange} 理论平空价：{ask_from_sell_exchange}')
                    decision_logger.info(f'实际价差：{2 * (close_info["real"]["close_sell_price"] - close_info["real"]["close_buy_price"]) / (close_info["real"]["close_sell_price"] + close_info["real"]["close_buy_price"]):.6f} 实际平多价：{close_info["real"]["close_buy_price"]} 实际平空价：{close_info["real"]["close_sell_price"]}')

                    
                    ts_close_limit = time.time()
                    close_record = close_info
                    close_buy_order_is_closed = False
                    close_sell_order_is_closed = False
                    while True:
                        ts_now = time.time()
                        if ts_now - ts_close_limit < Config.CP_WAIT_TIME:
                            continue
                        else:
                            if not close_buy_order_is_closed and not close_sell_order_is_closed:
                                close_buy_record = trader_state.exchanges[record['real']['buy_exchange']].get_order_detail(close_record['real']['close_buy_record']['orderid'])
                                close_sell_record = trader_state.exchanges[record['real']['sell_exchange']].get_order_detail(close_record['real']['close_sell_record']['orderid'])
                                if close_buy_record['status'] == 'filled' or close_buy_record['status'] == 'Filled' or close_buy_record['status'] == 'FILLED':
                                    decision_logger.info(f'close buy order filled')
                                    close_buy_order_is_closed = True
                                elif close_buy_record['status'] == 'canceled' or close_buy_record['status'] == 'Canceled' or close_buy_record['status'] == 'CANCELLED':
                                    decision_logger.info(f'close buy order already cancelled')
                                else:
                                    decision_logger.info(f'close_buy_record: {close_buy_record}')
                                    rtn_cancel_close_buy = trader_state._attempt_cancel_order(record['real']['buy_exchange'], close_record['real']['close_buy_record']['orderid'])
                                    if rtn_cancel_close_buy:
                                        decision_logger.info(f'close buy order cancelled')
                                
                                if close_sell_record['status'] == 'filled' or close_sell_record['status'] == 'Filled' or close_sell_record['status'] == 'FILLED':
                                    decision_logger.info(f'close sell order filled')
                                    close_sell_order_is_closed = True
                                elif close_sell_record['status'] == 'canceled' or close_sell_record['status'] == 'Canceled' or close_sell_record['status'] == 'CANCELLED':
                                    decision_logger.info(f'close sell order already cancelled')
                                else:
                                    decision_logger.info(f'close_sell_record: {close_sell_record}')
                                    rtn_cancel_close_sell = trader_state._attempt_cancel_order(record['real']['sell_exchange'], close_record['real']['close_sell_record']['orderid'])
                                    if rtn_cancel_close_sell:
                                        decision_logger.info(f'close sell order cancelled')

                        if not close_buy_order_is_closed and not close_sell_order_is_closed:
                            # trader_state.active_trades.pop()
                            # trader_state.redis_client.delete("trading:current_position")
                            decision_logger.info('双边限价单平仓均未成交，回退至判断价差收敛逻辑')
                            break
                        elif close_buy_order_is_closed and not close_sell_order_is_closed:
                            close_sell_order_signal = OrderSignal('linear',symbol,'buy','market',record['real']['sell_amount'],record['real']['sell_exchange'],'close')
                            close_sell_succeed, close_sell_orderid = trader_state.unified_place_order(close_sell_order_signal)
                            if len(trader_state.active_trades) > 0:
                                trader_state.active_trades.pop()
                            trader_state.redis_client.delete("trading:current_position")
                            close_buy_order_is_closed = True
                            close_sell_order_is_closed = True
                            decision_logger.info('做多限价单平仓未成交，做空限价单平仓成交，市价全平回到建仓判断逻辑')
                            break
                        elif not close_buy_order_is_closed and close_sell_order_is_closed:
                            close_buy_order_signal = OrderSignal('linear',symbol,'sell','market',record['real']['buy_amount'],record['real']['buy_exchange'],'close')
                            close_buy_succeed, close_buy_orderid = trader_state.unified_place_order(close_buy_order_signal)
                            if len(trader_state.active_trades) > 0:
                                trader_state.active_trades.pop()
                            trader_state.redis_client.delete("trading:current_position")
                            close_buy_order_is_closed = True
                            close_sell_order_is_closed = True
                            decision_logger.info('做多限价单平仓成交，做空限价单平仓未成交，市价全平回到建仓判断逻辑')
                            break
                        elif close_buy_order_is_closed and close_sell_order_is_closed:
                            decision_logger.info('双边限价单平仓均成交，回到建仓判断逻辑')
                            break

                        sleep(0.01)
                    
                    if close_buy_order_is_closed and close_sell_order_is_closed:
                        buy_order_is_closed = False
                        sell_order_is_closed = False
                        if len(trader_state.active_trades) > 0:
                            trader_state.active_trades.pop()
                        history_log = json.dumps(close_record,indent=4)
                        history_logger.info(f'新纪录:\n{history_log}')
                        
                        # redis
                        trader_state.redis_client.delete("trading:current_position")
                        # break
                    # break
            # 每5秒更新一次各交易所余额
            # if time.time() - ts_balance_update > 30:
            #     trader_state.refresh_all_balances()
            #     ts_balance_update = time.time()
        sleep(0.01)
