#!/usr/bin/env python3
"""
测试修复后的交易所连接问题
"""

import time
from logging_setup import setup_loggers
import logging

# 设置日志
setup_loggers()
decision_logger = logging.getLogger('decision_logger')
output_logger = logging.getLogger('output_logger')

def test_datetime_import():
    """测试datetime导入问题是否修复"""
    print("=== 测试 datetime 导入问题 ===")
    try:
        import time
        current_time = time.time()
        print(f"✅ time.time() 正常工作: {current_time}")
        return True
    except Exception as e:
        print(f"❌ time 模块问题: {e}")
        return False

def test_exchange_imports():
    """测试交易所类导入"""
    print("\n=== 测试交易所类导入 ===")
    success_count = 0
    
    try:
        from exchange import BybitExchangeHTTP
        print("✅ BybitExchangeHTTP 导入成功")
        success_count += 1
    except Exception as e:
        print(f"❌ BybitExchangeHTTP 导入失败: {e}")
    
    try:
        from exchange import BinanceExchangeHTTP
        print("✅ BinanceExchangeHTTP 导入成功")
        success_count += 1
    except Exception as e:
        print(f"❌ BinanceExchangeHTTP 导入失败: {e}")
    
    try:
        from exchange import OkxExchangeHTTP
        print("✅ OkxExchangeHTTP 导入成功")
        success_count += 1
    except Exception as e:
        print(f"❌ OkxExchangeHTTP 导入失败: {e}")
    
    try:
        from exchange import Bitgetv1HTTP
        print("✅ Bitgetv1HTTP 导入成功")
        success_count += 1
    except Exception as e:
        print(f"❌ Bitgetv1HTTP 导入失败: {e}")
    
    return success_count == 4

def test_basic_instantiation():
    """测试基本实例化（不连接API）"""
    print("\n=== 测试基本实例化 ===")
    
    try:
        from exchange import BybitExchangeHTTP, BinanceExchangeHTTP, OkxExchangeHTTP, Bitgetv1HTTP
        
        # 测试实例化
        bybit = BybitExchangeHTTP('Bybit', api_key='test', api_secret='test')
        print("✅ BybitExchangeHTTP 实例化成功")
        
        binance = BinanceExchangeHTTP('Binance', api_key='test', api_secret='test')
        print("✅ BinanceExchangeHTTP 实例化成功")
        
        okx = OkxExchangeHTTP('OKX', api_key='test', api_secret='test', passphrase='test')
        print("✅ OkxExchangeHTTP 实例化成功")
        
        bitget = Bitgetv1HTTP('Bitget', api_key='test', api_secret='test', passphrase='test')
        print("✅ Bitgetv1HTTP 实例化成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 实例化测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=== 修复验证测试 ===\n")
    
    results = []
    
    # 测试1: datetime导入问题
    results.append(test_datetime_import())
    
    # 测试2: 交易所类导入
    results.append(test_exchange_imports())
    
    # 测试3: 基本实例化
    results.append(test_basic_instantiation())
    
    # 总结
    print(f"\n=== 测试总结 ===")
    passed = sum(results)
    total = len(results)
    print(f"通过测试: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有基础修复验证通过！")
        print("\n现在可以尝试运行:")
        print("python trader.py")
    else:
        print("⚠️ 部分测试失败，需要进一步检查")
    
    return passed == total

if __name__ == "__main__":
    main()
