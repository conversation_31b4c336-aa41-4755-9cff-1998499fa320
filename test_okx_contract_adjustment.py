#!/usr/bin/env python3
"""
测试 OKX 合约面值修正功能
"""

from logging_setup import setup_loggers
import logging
import math

# 设置日志
setup_loggers()
decision_logger = logging.getLogger('decision_logger')
output_logger = logging.getLogger('output_logger')

def simulate_okx_contract_adjustment():
    """模拟 OKX 合约面值修正过程"""
    print("=== 模拟 OKX 合约面值修正 ===")
    
    # 模拟不同的合约面值和交易量
    test_cases = [
        {
            "symbol": "BTCUSDT",
            "ct_val": 0.01,  # 1张 = 0.01 BTC
            "order_amount": 0.1,  # 想要交易 0.1 BTC
            "description": "BTC合约，想要交易0.1 BTC"
        },
        {
            "symbol": "ETHUSDT", 
            "ct_val": 0.1,   # 1张 = 0.1 ETH
            "order_amount": 1.5,  # 想要交易 1.5 ETH
            "description": "ETH合约，想要交易1.5 ETH"
        },
        {
            "symbol": "BTCUSDT",
            "ct_val": 0.01,
            "order_amount": 0.005,  # 想要交易 0.005 BTC (小于1张)
            "description": "BTC合约，想要交易0.005 BTC (小于1张)"
        },
        {
            "symbol": "DOGEUSDT",
            "ct_val": 100,   # 1张 = 100 DOGE
            "order_amount": 250,  # 想要交易 250 DOGE
            "description": "DOGE合约，想要交易250 DOGE"
        }
    ]
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n--- 测试案例 {i}: {case['description']} ---")
        
        symbol = case['symbol']
        ct_val = case['ct_val']
        order_amount = case['order_amount']
        
        print(f"原始交易量: {order_amount} 币")
        print(f"合约面值: {ct_val} 币/张")
        
        # 模拟 OKX 张数计算
        okx_contracts = int(order_amount / ct_val)  # 向下取整到整张
        actual_order_amount = okx_contracts * ct_val  # 实际建仓数量
        
        print(f"计算张数: {order_amount} ÷ {ct_val} = {order_amount/ct_val:.6f} -> {okx_contracts} 张")
        print(f"实际交易量: {okx_contracts} × {ct_val} = {actual_order_amount} 币")
        
        # 计算损失
        loss = order_amount - actual_order_amount
        loss_pct = (loss / order_amount * 100) if order_amount > 0 else 0
        
        print(f"数量损失: {loss:.6f} 币 ({loss_pct:.2f}%)")
        
        if okx_contracts == 0:
            print("⚠️ 警告: 张数为0，无法交易")
        elif loss_pct > 5:
            print("⚠️ 警告: 数量损失超过5%")
        else:
            print("✅ 修正结果合理")

def test_calculate_trade_amount_with_okx():
    """测试包含 OKX 的 calculate_trade_amount 函数"""
    print("\n=== 测试 calculate_trade_amount 中的 OKX 修正 ===")
    
    try:
        from trader import ArbitrageTrader
        from exchange import OkxExchangeHTTP, BinanceExchangeHTTP
        from api_keys import EXCHANGE_API_KEYS
        
        # 创建模拟的交易所实例
        class MockOkxExchange:
            def __init__(self):
                self.exchange_name = 'Okx'
            
            def get_symbol_info(self, symbol):
                # 模拟返回 BTC 合约信息
                if 'BTC' in symbol:
                    return 0.0001, 1000.0, 0.01  # min_qty, max_qty, ct_val
                elif 'ETH' in symbol:
                    return 0.01, 10000.0, 0.1
                else:
                    return 1.0, 100000.0, 1.0
            
            def lcm(self, a, b):
                return abs(a * b) // math.gcd(int(a * 1000000), int(b * 1000000)) * 1000000 if a > 0 and b > 0 else max(a, b)
        
        class MockBinanceExchange:
            def __init__(self):
                self.exchange_name = 'Binance'
            
            def get_symbol_info(self, symbol):
                # 模拟返回 Binance 合约信息
                return 0.001, 1000.0, 1.0  # min_qty, max_qty, ct_val (Binance 合约面值为1)
            
            def lcm(self, a, b):
                return abs(a * b) // math.gcd(int(a * 1000000), int(b * 1000000)) * 1000000 if a > 0 and b > 0 else max(a, b)
        
        # 创建交易者实例
        trader = ArbitrageTrader()
        
        # 模拟测试数据
        test_scenarios = [
            {
                "buy_exchange": MockOkxExchange(),
                "sell_exchange": MockBinanceExchange(),
                "symbol": "BTCUSDT",
                "buy_price": 50000,
                "sell_price": 50100,
                "usdt_available": 1000,
                "description": "OKX买入，Binance卖出"
            },
            {
                "buy_exchange": MockBinanceExchange(),
                "sell_exchange": MockOkxExchange(),
                "symbol": "BTCUSDT", 
                "buy_price": 50000,
                "sell_price": 50100,
                "usdt_available": 1000,
                "description": "Binance买入，OKX卖出"
            }
        ]
        
        for i, scenario in enumerate(test_scenarios, 1):
            print(f"\n--- 场景 {i}: {scenario['description']} ---")
            
            # 调用 calculate_trade_amount 函数
            order_amount = trader.calculate_trade_amount(
                scenario['buy_exchange'],
                scenario['sell_exchange'],
                scenario['symbol'],
                scenario['buy_price'],
                scenario['sell_price'],
                scenario['usdt_available']
            )
            
            print(f"最终交易量: {order_amount} 币")
            
            if order_amount > 0:
                print("✅ 交易量计算成功")
            else:
                print("⚠️ 交易量为0")
                
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主测试函数"""
    print("=== OKX 合约面值修正测试 ===\n")
    
    # 模拟合约面值修正
    simulate_okx_contract_adjustment()
    
    # 测试实际函数
    test_calculate_trade_amount_with_okx()
    
    print("\n=== 测试完成 ===")
    print("现在可以运行实际的套利程序来验证修正效果")

if __name__ == "__main__":
    main()
