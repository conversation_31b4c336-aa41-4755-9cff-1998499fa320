from order_publisher import WsSignal,bitget_place_order
open_long_order = WsSignal(
    instId="ADAUSDT", # OKX 永续合约通常用 -
    tdMode="cross",      # 仓位模式: 逐仓 'isolated' 或 全仓 'cross'
    side="buy",          # 动作: 'buy' 或 'sell'
    ordType="market",    # 订单类型: 'market' (市价), 'limit' (限价)
    sz=7,             # 数量
)
bitget_place_order(open_long_order)

# open_long_order = WsSignal(
#     instId="ADAUSDT", # OKX 永续合约通常用 -
#     tdMode="cross",      # 仓位模式: 逐仓 'isolated' 或 全仓 'cross'
#     side="sell",          # 动作: 'buy' 或 'sell'
#     ordType="market",    # 订单类型: 'market' (市价), 'limit' (限价)
#     sz=7,             # 数量
# )
# bitget_place_order(open_long_order)