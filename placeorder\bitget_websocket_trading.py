#!/usr/bin/env python3
# 文件名: bitget_websocket_trading.py

import asyncio, json, logging, time, hmac, hashlib, base64
from typing import Dict, Any
import websockets
from websockets.legacy.client import WebSocketClientProtocol
import redis.asyncio as redis

try:
    from api_keys import EXCHANGE_API_KEYS
except ImportError:
    print("错误：找不到 api_keys.py 文件。")
    exit()

LOG_FORMAT = "%(asctime)s | %(levelname)-7s | %(module)s:%(lineno)-4d | %(message)s"
logging.basicConfig(level=logging.INFO, format=LOG_FORMAT, datefmt="%H:%M:%S")
logger = logging.getLogger("BitgetWsOrderService")

# WS_URL = "wss://wspap.bitget.com/v2/ws/private"
WS_URL = 'wss://ws.bitget.com/v2/ws/private'
REDIS_URL = "redis://localhost:6379/0"
REDIS_ORDER_CHANNEL = "trading_orders:bitget"

class BitgetWebSocketManager:
    def __init__(self, api_key: str, secret_key: str, passphrase: str, url: str):
        self._api_key, self._secret_key, self._passphrase, self._url = api_key, secret_key, passphrase, url
        self._ws: WebSocketClientProtocol = None
        self._is_connected = False
        self._listener_task = None

    async def connect(self):
        while True:
            try:
                logger.info(f"正在尝试连接到 Bitget: {self._url}...")
                self._ws = await websockets.connect(
                    self._url, 
                    ping_interval=None, 
                    ping_timeout=None
                )
                await self._login()
                self._is_connected = True
                logger.info("Bitget WebSocket 已连接并认证成功。")
                if self._listener_task: self._listener_task.cancel()
                self._listener_task = asyncio.create_task(self._listen())
                await self._listener_task
            except (websockets.exceptions.ConnectionClosedError, ConnectionError) as e:
                logger.warning(f"Bitget WebSocket 连接丢失: {e}。5秒后将尝试重连...")
            except Exception as e:
                logger.error(f"连接过程中发生未知错误: {e}", exc_info=True)
            finally:
                self._is_connected = False
                await asyncio.sleep(5)

    async def _login(self):
        timestamp = str(int(time.time()))
        pre_hash_string = f"{timestamp}GET/user/verify"
        mac = hmac.new(self._secret_key.encode('utf-8'), pre_hash_string.encode('utf-8'), hashlib.sha256)
        signature = base64.b64encode(mac.digest()).decode('utf-8')
        login_request = {"op": "login", "args": [{"apiKey": self._api_key, "passphrase": self._passphrase, "timestamp": timestamp, "sign": signature}]}
        await self._ws.send(json.dumps(login_request))
        logger.info("Bitget 认证请求已发送。")
        auth_response = json.loads(await asyncio.wait_for(self._ws.recv(), timeout=10))
        if auth_response.get("event") != "login" or auth_response.get("code") != 0:
            raise ConnectionError(f"Bitget 认证失败: {auth_response.get('msg', '未知错误')}")

    async def _listen(self):
        logger.info("Bitget WebSocket 消息监听任务已启动。")
        while True:
            try:
                message = await asyncio.wait_for(self._ws.recv(), timeout=30)
                
                # 处理服务器主动发来的 ping
                if message == 'ping':
                    await self._ws.send('pong')
                    logger.info("收到服务器 ping, 已回复 pong。")
                    continue
                
                # --- 新增的修复逻辑 ---
                # 处理我们主动发送 ping 后，服务器回复的 pong
                if message == 'pong':
                    logger.info("收到 pong 心跳回执，连接正常。")
                    continue

                # 如果不是心跳消息，则尝试作为JSON解析
                data = json.loads(message)
                if data.get("event") == "trade":
                    self._handle_order_response(data)
                else:
                    logger.debug(f"收到未处理的 Bitget 消息: {data}")

            except asyncio.TimeoutError:
                logger.info("30秒内无活动，主动发送字符串 'ping' 以保持连接。")
                await self._ws.send('ping')
            except websockets.exceptions.ConnectionClosed as e:
                logger.warning(f"Bitget 监听任务因连接关闭而终止: {e}")
                raise

    def _handle_order_response(self, response: Dict[str, Any]):
        request_id = response.get("arg", [{}])[0].get("id", "N/A")
        if response.get("code") == 0:
            order_data = response.get("arg", [{}])[0].get("params", {})
            order_id = order_data.get("orderId", "N/A")
            client_oid = order_data.get("clientOid", "N/A")
            logger.info(f"✅ [Bitget 下单成功] 请求ID: {request_id}, 订单ID: {order_id}, 客户ID: {client_oid}")
        else:
            code = response.get("code")
            msg = response.get("msg", "未知错误")
            logger.error(f"❌ [Bitget 下单失败] 请求ID: {request_id}, 错误码: {code}, 原因: {msg}")

    async def place_order(self, order_args: Dict[str, Any]):
        # ... 此函数及后续代码保持不变 ...
        payload = {"op": "trade", "args": [order_args]}
        if not self._is_connected or not self._ws:
            logger.error(f"无法下单 (请求ID: {order_args.get('id')}): WebSocket 未连接。")
            return
        try:
            logger.info(f"-> 准备发送 Bitget 订单 (请求ID: {order_args.get('id')}): {json.dumps(payload)}")
            await self._ws.send(json.dumps(payload))
        except Exception as e:
            logger.error(f"发送 Bitget 订单时出错 (请求ID: {order_args.get('id')}): {e}", exc_info=True)

async def listen_for_redis_orders(ws_manager: BitgetWebSocketManager):
    while True:
        try:
            logger.info(f"正在连接到 Redis ({REDIS_URL})...")
            r = await redis.from_url(REDIS_URL, decode_responses=True)
            pubsub = r.pubsub()
            await pubsub.subscribe(REDIS_ORDER_CHANNEL)
            logger.info(f"✅ 已成功订阅 Redis 频道: '{REDIS_ORDER_CHANNEL}'")
            async for message in pubsub.listen():
                if message['type'] != 'message': continue
                logger.info(f"从 Redis 收到消息: {message['data']}")
                try:
                    order_details_dict = json.loads(message['data'])
                    await ws_manager.place_order(order_details_dict)
                except json.JSONDecodeError:
                    logger.error(f"无法解析来自 Redis 的 JSON 消息: {message['data']}")
                except Exception as e:
                    logger.error(f"处理 Redis 消息时出错: {e}", exc_info=True)
        except redis.exceptions.ConnectionError as e:
            logger.error(f"Redis 连接失败: {e}。10秒后将重试...")
            await asyncio.sleep(10)

async def main():
    logger.info("启动 Bitget WebSocket 下单服务...")
    credentials = EXCHANGE_API_KEYS.get("bitget")
    if not credentials or "api_key" not in credentials:
        logger.error("错误：请在 api_keys.py 文件中正确配置您的 Bitget API 信息。")
        return
    ws_manager = BitgetWebSocketManager(
        api_key=credentials["api_key"], secret_key=credentials["secret"],
        passphrase=credentials["passphrase"], url=WS_URL
    )
    try:
        await asyncio.gather(ws_manager.connect(), listen_for_redis_orders(ws_manager))
    finally:
        logger.info("服务正在关闭...")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("收到关闭信号 (Ctrl+C)，程序退出。")