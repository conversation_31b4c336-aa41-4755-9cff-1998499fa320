"""
OKX USDT 永续合约 (SWAP) 最优买卖价订阅示例
---------------------------------------------
频道: tickers
Endpoint: wss://ws.okx.com:8443/ws/v5/public
目标: 订阅 BTC-USDT-SWAP, ETH-USDT-SWAP (支持用户输入 BTCUSDT / ETHUSDT 自动规范化)

返回字段(节选):
  bidPx / bidSz  - 最优买一价 / 数量
  askPx / askSz  - 最优卖一价 / 数量
  ts             - 服务器时间戳 (ms)

功能特性:
  - 多合约批量订阅
  - 心跳 (ping -> pong)
  - 自动重连 (指数退避 + 抖动)
  - 消息超时 watchdog
  - 延迟计算 (本地收到时刻 - 服务端 ts)
  - 优雅退出 (Ctrl+C)

若需要更详细盘口(多档)可用: books5 / books / books50-l2-tbt 等频道。
"""

import asyncio
import json
import logging
import signal
import time
from dataclasses import dataclass
from typing import List, Optional, Callable, Awaitable

import websockets
from websockets import WebSocketClientProtocol

OKX_PUBLIC_WS = "wss://ws.okx.com:8443/ws/v5/public"

# 心跳间隔（秒）
HEARTBEAT_INTERVAL = 15
# 若超过此秒数未收到任意消息（包括 pong/data），视为异常
MESSAGE_TIMEOUT = 45
# 重连退避
RECONNECT_BASE_DELAY = 2
RECONNECT_MAX_DELAY = 60


@dataclass
class BestQuote:
    inst_id: str
    bid_px: float
    bid_sz: float
    ask_px: float
    ask_sz: float
    ts: int        # 服务器毫秒时间戳
    latency_ms: float  # 本地简单延迟估计


class OKXSwapTickersClient:
    """
    OKX 合约 tickers 频道客户端（聚焦最优买卖）。
    """

    def __init__(
        self,
        symbols: List[str],
        on_quote: Optional[Callable[[BestQuote], Awaitable[None]]] = None,
    ):
        """
        :param symbols: 用户输入，如 ["BTCUSDT", "ETHUSDT"] 或 ["BTC-USDT-SWAP"]
        :param on_quote: 异步回调，收到 BestQuote 时调用
        """
        self.raw_symbols = symbols
        self.instruments = [self._normalize_symbol(s) for s in symbols]
        self.on_quote = on_quote

        self._ws: Optional[WebSocketClientProtocol] = None
        self._stop = asyncio.Event()

        self._last_msg_time: float = 0.0
          # 最近收到任意消息的本地时间 (time.time())
        self._reconnect_attempt: int = 0
        self._running_tasks: list[asyncio.Task] = []

    # ------------- 规范化合约符号 ------------- #
    @staticmethod
    def _normalize_symbol(sym: str) -> str:
        """
        将多种写法转为 OKX 永续合约 instId:
          BTCUSDT   -> BTC-USDT-SWAP
          btc-usdt  -> BTC-USDT-SWAP
          BTC-USDT  -> BTC-USDT-SWAP (若未带 -SWAP 且无交割合约日期)
          BTC-USDT-SWAP -> 保持不变
          BTC-USDT-240927 -> 视为交割合约（不强加 -SWAP）
        
        简易规则：
          1. 大写
          2. 若包含 -SWAP / -YYYYMMDD 直接返回
          3. 若无 '-' 但以 USDT/USDC 结尾 => BASE-QUOTE-SWAP
          4. 若有一个 '-' (BASE-QUOTE) => BASE-QUOTE-SWAP
        """
        s = sym.upper().strip()
        if "-SWAP" in s:
            return s
        # 交割合约日期匹配: 8 位数字结尾
        if len(s) >= 9 and s[-8:].isdigit():
            # 交割合约，不追加 -SWAP
            # 例如 BTC-USDT-240927
            if s.count("-") == 2:
                return s
        # 无 '-': 可能 BTCUSDT
        if "-" not in s:
            for quote in ("USDT", "USDC", "BTC", "ETH"):
                if s.endswith(quote):
                    base = s[:-len(quote)]
                    if base:
                        return f"{base}-{quote}-SWAP"
        # 有一个 '-' ：BASE-QUOTE
        if s.count("-") == 1:
            return s + "-SWAP"
        return s  # 其它情况保持

    # ------------- 对外启动/停止 ------------- #
    async def start(self):
        logging.info("启动 OKXSwapTickersClient 订阅: %s", self.instruments)
        while not self._stop.is_set():
            try:
                await self._connect_and_loop()
            except asyncio.CancelledError:
                raise
            except Exception as e:
                logging.exception("主循环异常: %s", e)

            if self._stop.is_set():
                break

            # 指数退避
            self._reconnect_attempt += 1
            delay = min(RECONNECT_BASE_DELAY * (2 ** (self._reconnect_attempt - 1)), RECONNECT_MAX_DELAY)
            jitter = delay * 0.2
            wait_time = delay + (jitter * (0.5 - (time.time() % 1)))
            logging.warning("将在 %.2f 秒后重连 (尝试 #%d)", wait_time, self._reconnect_attempt)
            try:
                await asyncio.wait_for(self._stop.wait(), timeout=wait_time)
            except asyncio.TimeoutError:
                pass

        logging.info("客户端已停止。")

    async def stop(self):
        logging.info("停止中 ...")
        self._stop.set()
        await self._close_ws()
        for t in self._running_tasks:
            if not t.done():
                t.cancel()
        await asyncio.gather(*self._running_tasks, return_exceptions=True)

    # ------------- 内部连接&任务 ------------- #
    async def _connect_and_loop(self):
        logging.info("连接 %s", OKX_PUBLIC_WS)
        async with websockets.connect(
            OKX_PUBLIC_WS,
            ping_interval=None,   # 手动心跳
            ping_timeout=None,
            max_queue=1024,
            close_timeout=5,
        ) as ws:
            self._ws = ws
            self._reconnect_attempt = 0
            self._last_msg_time = time.time()

            await self._subscribe()

            recv_task = asyncio.create_task(self._receiver(), name="receiver")
            heartbeat_task = asyncio.create_task(self._heartbeat(), name="heartbeat")
            watchdog_task = asyncio.create_task(self._watchdog(), name="watchdog")
            self._running_tasks = [recv_task, heartbeat_task, watchdog_task]

            done, pending = await asyncio.wait(self._running_tasks, return_when=asyncio.FIRST_EXCEPTION)
            for d in done:
                if exc := d.exception():
                    raise exc
        self._ws = None

    async def _close_ws(self):
        if self._ws:
            try:
                await self._ws.close()
            except Exception:
                pass

    async def _send_json(self, obj: dict):
        if not self._ws:
            return
        txt = json.dumps(obj, separators=(",", ":"))
        await self._ws.send(txt)
        logging.debug("发送: %s", txt)

    async def _subscribe(self):
        args = [{"channel": "tickers", "instId": inst} for inst in self.instruments]
        msg = {"op": "subscribe", "args": args}
        await self._send_json(msg)
        logging.info("已发送订阅: %s", args)

    # ------------- 心跳 & 看门狗 ------------- #
    async def _heartbeat(self):
        while not self._stop.is_set():
            await asyncio.sleep(HEARTBEAT_INTERVAL)
            if not self._ws:
                continue
            try:
                await self._ws.send("ping")
                logging.debug("发送 ping")
            except Exception as e:
                logging.warning("心跳异常: %s", e)
                break

    async def _watchdog(self):
        while not self._stop.is_set():
            await asyncio.sleep(5)
            idle = time.time() - self._last_msg_time
            if idle > MESSAGE_TIMEOUT:
                logging.warning("超过 %.1fs 未收到消息 (阈值=%ss)，强制断开触发重连", idle, MESSAGE_TIMEOUT)
                await self._close_ws()
                break

    # ------------- 接收处理 ------------- #
    async def _receiver(self):
        assert self._ws is not None
        ws = self._ws
        async for raw in ws:
            self._last_msg_time = time.time()

            if raw == "pong":
                logging.debug("收到 pong")
                continue

            try:
                msg = json.loads(raw)
            except json.JSONDecodeError:
                logging.debug("非 JSON 消息: %s", raw)
                continue

            # 错误事件
            if msg.get("event") == "error":
                logging.error("错误事件: %s", msg)
                continue

            # 订阅确认
            if msg.get("event") in ("subscribe", "unsubscribe"):
                logging.info("事件: %s -> %s", msg.get("event"), msg.get("arg"))
                continue

            # 数据消息
            if "arg" in msg and "data" in msg:
                channel = msg["arg"].get("channel")
                if channel == "tickers":
                    for d in msg.get("data", []):
                        try:
                            ts = int(d.get("ts", 0))
                            now_ms = int(time.time() * 1000)
                            latency = now_ms - ts if ts > 0 else -1
                            quote = BestQuote(
                                inst_id=d["instId"],
                                bid_px=float(d["bidPx"]) if d.get("bidPx") else 0.0,
                                bid_sz=float(d["bidSz"]) if d.get("bidSz") else 0.0,
                                ask_px=float(d["askPx"]) if d.get("askPx") else 0.0,
                                ask_sz=float(d["askSz"]) if d.get("askSz") else 0.0,
                                ts=ts,
                                latency_ms=latency
                            )
                            if self.on_quote:
                                await self.on_quote(quote)
                        except Exception as e:
                            logging.exception("处理 tickers 数据异常: %s | raw=%s", e, d)
                else:
                    logging.debug("忽略频道: %s", channel)
            else:
                logging.debug("未知结构: %s", msg)


# ------------- 使用示例 ------------- #

async def example_on_quote(q: BestQuote):
    print(
        f"[{time.strftime('%X')}] {q.inst_id} "
        f"Bid {q.bid_px:.2f} ({q.bid_sz:.4f}) | "
        f"Ask {q.ask_px:.2f} ({q.ask_sz:.4f}) | "
        f"ts={q.ts} latency={q.latency_ms}ms"
    )

async def main():
    # 用户可直接写 BTCUSDT / ETHUSDT，会被转换为 BTC-USDT-SWAP / ETH-USDT-SWAP
    symbols = ["OPUSDT","LDOUSDT","NEARUSDT","CRVUSDT","ZETAUSDT","LRCUSDT","ARBUSDT","LINKUSDT","BCHUSDT","MASKUSDT","ALGOUSDT","YFIUSDT","XLMUSDT","CROUSDT","ETHWUSDT","ETHUSDT","ETCUSDT","USDCUSDT","THETAUSDT","CHZUSDT","AXSUSDT","SANDUSDT","ADAUSDT","FLOWUSDT","AI16ZUSDT","INJUSDT","SUSHIUSDT","1INCHUSDT","MAJORUSDT","MYRIAUSDT","CFXUSDT","ZILUSDT","GMTUSDT","HMSTRUSDT","ORBSUSDT","ATOMUSDT","SOLUSDT","DOGSUSDT","ETHFIUSDT","SAHARAUSDT","KSMUSDT","AVAXUSDT","IDUSDT","BOMEUSDT","MINAUSDT","AAVEUSDT","LPTUSDT","IOTAUSDT","NEOUSDT","ACTUSDT","TONUSDT","TRXUSDT","CELOUSDT","XTZUSDT","YGGUSDT","COMPUSDT","BTCUSDT","BNBUSDT","NOTUSDT","HBARUSDT","SHELLUSDT","1000000MOGUSDT","DOGEUSDT","CETUSUSDT","XRPUSDT","QTUMUSDT","BANDUSDT","ICPUSDT","KAITOUSDT","CATIUSDT","IMXUSDT","SUIUSDT","LTCUSDT","BRETTUSDT","BNTUSDT","NILUSDT","SPKUSDT","FILUSDT","HUMAUSDT","LQTYUSDT","JUPUSDT","PARTIUSDT","AUCTIONUSDT","MAGICUSDT","DOTUSDT"]
    client = OKXSwapTickersClient(symbols, on_quote=example_on_quote)

    loop = asyncio.get_running_loop()
    stop_evt = asyncio.Event()

    def _stop():
        if not stop_evt.is_set():
            stop_evt.set()
            asyncio.create_task(client.stop())

    for sig in (signal.SIGINT, signal.SIGTERM):
        try:
            loop.add_signal_handler(sig, _stop)
        except NotImplementedError:
            pass

    runner = asyncio.create_task(client.start())
    await stop_evt.wait()
    await runner

if __name__ == "__main__":
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s | %(levelname)-8s | %(message)s",
        datefmt="%H:%M:%S",
    )
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("用户中断")