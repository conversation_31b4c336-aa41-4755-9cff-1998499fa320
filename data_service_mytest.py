import redis
import json
import time
import random
import matplotlib.pyplot as plt
from datetime import datetime

# Redis 连接
redis_client = redis.Redis(host="localhost", port=6379, db=0, decode_responses=True)

# 收集 30 秒数据
collected_data = []
start_time = time.time()
while time.time() - start_time < 120:
    print(time.time()-start_time)
    raw_data = redis_client.get("trading:exchange_data")
    if raw_data:
        try:
            data = json.loads(raw_data)
            collected_data.append({"timestamp": datetime.now(), "data": data})
        except Exception as e:
            print("解析失败:", e)
    time.sleep(1)  # 每秒取一次

# 随机选择 10 个 symbol
if collected_data:
    all_symbols = list(collected_data[-1]["data"].keys())
    sampled_symbols = random.sample(all_symbols, min(10, len(all_symbols)))

    # 画图
    for symbol in sampled_symbols:
        plt.figure(figsize=(10, 6))
        for exchange in ["Binance", "OKX", "Bitget", "Bybit"]:
            times = []
            prices = []
            for entry in collected_data:
                market_data = entry["data"].get(symbol, {}).get(exchange, {})
                bid = market_data.get("bid")
                if bid:
                    times.append(entry["timestamp"])
                    prices.append(float(bid))
            if times and prices:
                plt.plot(times, prices, label=exchange)

            
        plt.title(f"{symbol} Price vs Time")
        plt.xlabel("Time")
        plt.ylabel("Bid Price")
        plt.legend()
        plt.tight_layout()
        # plt.show()
        plt.savefig(f'cross_exchange_re/test_plots/{symbol}_200sec', dpi=300)
