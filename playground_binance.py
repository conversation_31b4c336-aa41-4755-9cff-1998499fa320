from binance.client import Client

client = Client("6iMLRKb3ArZlDSwh0PkSNsTjFZF1THr3AoOk1ctjIu2lu75H18NjEY3Zla8MEwYh",
                 "ZKZWlr9w7Wdkn60FlgzkBNYhFIwrTQdyUnYzxNLgNnnDyxOUNUEi24FqSzzlcCuP", testnet=False)

balance = client.futures_account_balance()
order = client.futures_create_order(symbol="BTCUSDT",
                                    side="BUY")
print(balance)
info = client.get_exchange_info()
print('hello')