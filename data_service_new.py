#!/usr/bin/env python3
"""
重构版 data_service.py
--------------------------------
核心改进：
  1. 订阅确认 (ack/error) 与统计
  2. JSO<PERSON> ping/pong 心跳（Bitget / Bybit / OKX）
  3. 生产者 (WebSocket recv) / 消费者 (业务更新) 解耦
  4. parser 返回 list[QuoteUpdate]，支持一帧多 symbol
  5. 批量 flush 更新 state 与 realtime_data_manager，减少锁竞争
  6. 无限重连 + 指数退避 + jitter
  7. inactivity watchdog：行情长时间停滞自动重连
  8. 增大 max_queue 防止高频溢出
  9. 统一 metrics 计数器，定期输出
  10. 交易所适配：Binance (futures bookTicker) / Bybit / OKX / Bitget
可继续扩展：Prometheus / 策略回调 / 风控。
"""

from __future__ import annotations

import asyncio
import json
import logging
import random
import signal
import sys
import time
from dataclasses import dataclass
from typing import Any, Dict, List, Optional, Callable, Coroutine

# 依赖 websockets
import websockets
from websockets import WebSocketClientProtocol

# ----------------------------------------------------
# 与外部模块衔接（trading_state / realtime_data_manager）
# ----------------------------------------------------
try:
    from trading_state import TradingState
except ImportError:
    class TradingState:
        """Fallback stub，仅为演示。实际请使用真实 TradingState。"""
        def __init__(self):
            self.symbols = []
            self.shared_data: Dict[str, Dict[str, Dict[str, float]]] = {}
            self.lock = asyncio.Lock()

        def init_symbols(self):
            # 真实实现中应配置所需 symbols
            if not self.symbols:
                self.symbols = ["OPUSDT","LDOUSDT","NEARUSDT","CRVUSDT","ZETAUSDT","LRCUSDT","ARBUSDT","LINKUSDT","BCHUSDT","MASKUSDT","ALGOUSDT","XLMUSDT","CROUSDT","ETHWUSDT","ETCUSDT","USDCUSDT","THETAUSDT","CHZUSDT","AXSUSDT","SANDUSDT","ADAUSDT","FLOWUSDT","AI16ZUSDT","INJUSDT","SUSHIUSDT","1INCHUSDT","MAJORUSDT","CFXUSDT","ZILUSDT","GMTUSDT","HMSTRUSDT","ATOMUSDT","DOGSUSDT","ETHFIUSDT","SAHARAUSDT","KSMUSDT","AVAXUSDT","BOMEUSDT","MINAUSDT","AAVEUSDT","LPTUSDT","IOTAUSDT","NEOUSDT","ACTUSDT","TONUSDT","TRXUSDT","CELOUSDT","XTZUSDT","YGGUSDT","COMPUSDT","NOTUSDT","HBARUSDT","SHELLUSDT","DOGEUSDT","CETUSUSDT","QTUMUSDT","BANDUSDT","ICPUSDT","KAITOUSDT","CATIUSDT","IMXUSDT","SUIUSDT","LTCUSDT","BRETTUSDT","BNTUSDT","SPKUSDT","FILUSDT","HUMAUSDT","LQTYUSDT","JUPUSDT","PARTIUSDT","AUCTIONUSDT","MAGICUSDT","DOTUSDT"] # "BTCUSDT","ETHUSDT","SOLUSDT","BNBUSDT","XRPUSDT",
                for s in self.symbols:
                    self.shared_data[s] = {
                        "Binance": {"bid": 0.0, "ask": 0.0},
                        "Bybit": {"bid": 0.0, "ask": 0.0},
                        "OKX": {"bid": 0.0, "ask": 0.0},
                        "Bitget": {"bid": 0.0, "ask": 0.0},
                    }

try:
    from realtime_data_manager import realtime_data_manager
except ImportError:
    class DummyRDM:
        def update_price(self, symbol, exchange, bid, ask):
            pass
    realtime_data_manager = DummyRDM()

# ----------------------------------------------------
# 全局日志
# ----------------------------------------------------
LOG_FORMAT = "%(asctime)s | %(levelname)-7s | %(message)s"
logging.basicConfig(
    level=logging.INFO,
    format=LOG_FORMAT,
    datefmt="%H:%M:%S",
)
logger = logging.getLogger("data_service")

# ----------------------------------------------------
# 数据结构
# ----------------------------------------------------
@dataclass
class QuoteUpdate:
    symbol: str
    bid: float
    ask: float
    ts_exchange: Optional[int] = None

# ----------------------------------------------------
# 全局队列 & 统计
# ----------------------------------------------------
PRICE_QUEUE: asyncio.Queue[tuple[str, QuoteUpdate]] = asyncio.Queue(maxsize=10_000)

METRICS = {
    "ws_connected": 0,
    "ws_reconnects": 0,
    "ws_subscribe_ok": 0,
    "ws_subscribe_fail": 0,
    "msg_frames": 0,
    "quotes_parsed": 0,
    "parser_errors": 0,
    "enqueue_ok": 0,
    "enqueue_dropped": 0,
    "consumer_flush": 0,
    "consumer_updates": 0,
    "inactivity_reconnects": 0
}

METRICS_LAST_DUMP = 0.0

# ----------------------------------------------------
# 交易所配置（可扩展）
# ----------------------------------------------------
# 每个 exchange 定义：
# {
#   "name": ...,
#   "build_uri": lambda symbols: str,
#   "build_subscribe": lambda symbols: List[dict] or dict,
#   "parser": callable(msg) -> list[QuoteUpdate] | QuoteUpdate | None
#   "need_ack": bool,
#   "ack_handler": callable(exchange_name, msg, pending_symbols, metrics) -> None
#   "heartbeat": "json" | "native" | None,
#   "message_timeout": int,
#   "keepalive_interval": int,
#   "max_queue": int
# }
# 注：若交易所需要对 symbol 做转换，外部先传递标准化 symbol（如 BTCUSDT），在构建订阅时再改形态。

def build_okx_inst(symbol: str) -> str:
    s = symbol.upper().replace("_", "").replace("-", "")
    if s.endswith("USDT"):
        base = s[:-4]
        return f"{base}-USDT-SWAP"
    return symbol

def ack_handler_okx(exchange_name: str, msg: dict, pending: set, metrics: dict):
    if msg.get("event") == "subscribe":
        arg = msg.get("arg", {})
        instId = arg.get("instId", "")
        if instId:
            norm = instId.replace("-", "").replace("SWAP", "").upper()
            if norm in pending:
                pending.discard(norm)
                metrics["ws_subscribe_ok"] += 1
    elif msg.get("event") == "error":
        metrics["ws_subscribe_fail"] += 1

def ack_handler_bybit(exchange_name: str, msg: dict, pending: set, metrics: dict):
    # Bybit v5: {"op":"subscribe","args":["tickers.BTCUSDT"],"success":True,"ret_msg":...}
    if msg.get("op") == "subscribe" and msg.get("success"):
        for a in msg.get("args", []):
            sym = a.split(".")[-1].upper()
            if sym in pending:
                pending.discard(sym)
                metrics["ws_subscribe_ok"] += 1
    elif msg.get("op") == "error":
        metrics["ws_subscribe_fail"] += 1

def ack_handler_bitget(exchange_name: str, msg: dict, pending: set, metrics: dict):
    if msg.get("event") == "subscribe":
        arg = msg.get("arg", {})
        instId = arg.get("instId")
          # bitget futures instId: BTCUSDT / BTC-USDT？
        if instId:
            norm = instId.replace("-", "").upper()
            if norm in pending:
                pending.discard(norm)
                metrics["ws_subscribe_ok"] += 1
    elif msg.get("event") == "error":
        metrics["ws_subscribe_fail"] += 1

def ack_handler_binance(exchange_name: str, msg: dict, pending: set, metrics: dict):
    # Combined stream generally no ack; treat all as success
    if pending:
        metrics["ws_subscribe_ok"] += len(pending)
        pending.clear()

def parser_binance(msg: dict):
    data = msg.get("data")
    if not data or "s" not in data:
        return None
    try:
        return QuoteUpdate(
            symbol=data["s"].upper(),
            bid=float(data["b"]),
            ask=float(data["a"]),
            ts_exchange=int(data.get("E") or 0)
        )
    except Exception:
        return None

def parser_bybit(msg: dict):
    topic = msg.get("topic")
    if not topic or not topic.startswith("tickers."):
        return None
    d = msg.get("data")
    if not d:
        return None
    try:
        sym = (d.get("symbol") or topic.split(".", 1)[1]).upper()
        return QuoteUpdate(
            symbol=sym,
            bid=float(d.get("bid1Price") or 0),
            ask=float(d.get("ask1Price") or 0),
            ts_exchange=int(msg.get("ts") or 0)
        )
    except Exception:
        return None

def parser_okx(msg: dict):
    if msg.get("arg", {}).get("channel") != "tickers":
        return None
    out = []
    for d in msg.get("data", []):
        inst = d.get("instId")
        if not inst:
            continue
        norm = inst.replace("-", "").replace("SWAP", "").upper()
        try:
            out.append(QuoteUpdate(
                symbol=norm,
                bid=float(d.get("bidPx") or 0),
                ask=float(d.get("askPx") or 0),
                ts_exchange=int(d.get("ts") or 0)
            ))
        except Exception:
            continue
    return out or None

def parser_bitget(msg: dict):
    if msg.get("arg", {}).get("channel") != "ticker":
        return None
    out = []
    for d in msg.get("data", []):
        inst = d.get("instId")
        if not inst:
            continue
        norm = inst.replace("-", "").upper()
        try:
            out.append(QuoteUpdate(
                symbol=norm,
                bid=float(d.get("bidPr") or d.get("bestBid") or 0),
                ask=float(d.get("askPr") or d.get("bestAsk") or 0),
                ts_exchange=int(d.get("ts") or d.get("systemTime") or 0)
            ))
        except Exception:
            continue
    return out or None

EXCHANGES: Dict[str, Dict[str, Any]] = {
    "Binance": {
        "name": "Binance",
        "build_uri": lambda symbols: "wss://fstream.binance.com/stream?streams=" + "/".join(
            f"{s.lower()}@bookTicker" for s in symbols
        ),
        "build_subscribe": None,  # Combined stream URL 直接激活
        "parser": parser_binance,
        "need_ack": False,
        "ack_handler": ack_handler_binance,
        "heartbeat": "native",
        "keepalive_interval": 25,
        "message_timeout": 90,
        "max_queue": 2048
    },
    "Bybit": {
        "name": "Bybit",
        "build_uri": lambda symbols: "wss://stream.bybit.com/v5/public/linear",
        "build_subscribe": lambda symbols: {
            "op": "subscribe",
            "args": [f"tickers.{s.upper()}" for s in symbols]
        },
        "parser": parser_bybit,
        "need_ack": True,
        "ack_handler": ack_handler_bybit,
        "heartbeat": "json",
        "keepalive_interval": 15,
        "message_timeout": 60,
        "max_queue": 1024
    },
    "OKX": {
        "name": "OKX",
        "build_uri": lambda symbols: "wss://ws.okx.com:8443/ws/v5/public",
        "build_subscribe": lambda symbols: {
            "op": "subscribe",
            "args": [{"channel": "tickers", "instId": build_okx_inst(s)} for s in symbols]
        },
        "parser": parser_okx,
        "need_ack": True,
        "ack_handler": ack_handler_okx,
        "heartbeat": "json",
        "keepalive_interval": 15,
        "message_timeout": 45,
        "max_queue": 1024
    },
    "Bitget": {
        "name": "Bitget",
        "build_uri": lambda symbols: "wss://ws.bitget.com/v2/ws/public",
        "build_subscribe": lambda symbols: {
            "op": "subscribe",
            "args": [
                {"instType": "USDT-FUTURES", "channel": "ticker", "instId": s.upper()}
                for s in symbols
            ]
        },
        "parser": parser_bitget,
        "need_ack": True,
        "ack_handler": ack_handler_bitget,
        "heartbeat": "json",
        "keepalive_interval": 15,
        "message_timeout": 45,
        "max_queue": 1024
    },
}

# ----------------------------------------------------
# 连接器
# ----------------------------------------------------
async def ws_connector(
    exchange_name: str,
    symbols: List[str],
    state: TradingState
):
    """
    单交易所 WebSocket 连接器（生产者）：
      - 建立连接
      - 订阅 + ack
      - 心跳 & inactivity watchdog
      - 解析后入队
    无限循环重连。
    """
    cfg = EXCHANGES[exchange_name]
    uri = cfg["build_uri"](symbols)
    build_subscribe = cfg["build_subscribe"]
    parser = cfg["parser"]
    ack_handler = cfg["ack_handler"]
    need_ack = cfg["need_ack"]
    heartbeat_mode = cfg["heartbeat"]
    keepalive_interval = cfg["keepalive_interval"]
    message_timeout = cfg["message_timeout"]
    max_queue = cfg["max_queue"]

    reconnect_attempt = 0

    while True:
        ws: Optional[WebSocketClientProtocol] = None
        try:
            METRICS["ws_reconnects"] += 1
            reconnect_attempt += 1
            logger.info(f"[{exchange_name}] connecting (attempt={reconnect_attempt}) {uri}")

            ws = await websockets.connect(
                uri,
                ping_interval=None,  # 统一手动心跳
                ping_timeout=None,
                max_queue=max_queue,
                close_timeout=10,
                compression=None
            )
            METRICS["ws_connected"] += 1
            logger.info(f"[{exchange_name}] connected.")

            # 发送订阅
            if build_subscribe:
                sub_msg = build_subscribe(symbols)
                if isinstance(sub_msg, list):
                    for sm in sub_msg:
                        await ws.send(json.dumps(sm))
                        logger.debug(f"[{exchange_name}] -> SUB {sm}")
                else:
                    await ws.send(json.dumps(sub_msg))
                    logger.debug(f"[{exchange_name}] -> SUB {sub_msg}")
            else:
                logger.info(f"[{exchange_name}] combined stream (no explicit subscribe).")

            # 处理 ack
            pending = set(s.upper() for s in symbols)
            prebuffer: List[dict] = []
            ack_deadline = time.time() + 6
            if need_ack:
                while pending and time.time() < ack_deadline:
                    try:
                        raw = await asyncio.wait_for(ws.recv(), timeout=ack_deadline - time.time())
                    except asyncio.TimeoutError:
                        break
                    if isinstance(raw, bytes):
                        raw = raw.decode()
                    try:
                        msg = json.loads(raw)
                    except Exception:
                        continue
                    if _is_control_msg(exchange_name, msg):
                        _handle_control(exchange_name, msg, ws)
                        continue
                    ack_handler(exchange_name, msg, pending, METRICS)
                    # 非 ack 的行情提前缓冲
                    if not _looks_like_ack(exchange_name, msg):
                        prebuffer.append(msg)
                if pending:
                    # 这里不给硬失败，打印警告继续
                    logger.warning(f"[{exchange_name}] subscription pending not confirmed: {pending}")
            else:
                # 没 ack 的直接全部记为成功
                METRICS["ws_subscribe_ok"] += len(pending)
                pending.clear()

            logger.info(f"[{exchange_name}] subscribe_ok={METRICS['ws_subscribe_ok']} fail={METRICS['ws_subscribe_fail']}")

            reconnect_attempt = 0  # reset after success
            last_msg_time = time.time()

            # 预缓冲先处理
            for pb in prebuffer:
                # await _parse_and_enqueue(exchange_name, pb, parser)
                await _parse_and_apply(exchange_name, pb, parser, state)

            # 主循环
            while True:
                # 心跳/超时调度策略：
                #  - 使用 wait_for + keepalive_interval
                try:
                    raw = await asyncio.wait_for(ws.recv(), timeout=keepalive_interval)
                    if isinstance(raw, bytes):
                        raw = raw.decode()
                except asyncio.TimeoutError:
                    # 发送心跳
                    await _send_heartbeat(exchange_name, ws, heartbeat_mode)
                else:
                    last_msg_time = time.time()
                    METRICS["msg_frames"] += 1
                    try:
                        msg = json.loads(raw)
                    except Exception:
                        continue

                    # if exchange_name == 'Bitget':
                    #     print(f'Bitget msg: {msg}')

                    if _is_control_msg(exchange_name, msg):
                        _handle_control(exchange_name, msg, ws)
                        continue

                    # await _parse_and_enqueue(exchange_name, msg, parser)
                    await _parse_and_apply(exchange_name, msg, parser, state)

                # inactivity watchdog
                if time.time() - last_msg_time > message_timeout:
                    METRICS["inactivity_reconnects"] += 1
                    logger.warning(f"[{exchange_name}] inactivity {time.time()-last_msg_time:.1f}s > {message_timeout}s -> reconnect")
                    break

        except asyncio.CancelledError:
            raise
        except Exception as e:
            logger.error(f"[{exchange_name}] loop error: {e}", exc_info=False)
            # break
        finally:
            if ws:
                try:
                    await ws.close()
                except Exception:
                    pass
            # 指数退避 + jitter
            delay = min(2 ** min(reconnect_attempt, 6), 60)
            jitter = random.uniform(0, delay * 0.3)
            await asyncio.sleep(delay + jitter)

# ----------------------------------------------------
# 控制 / 心跳相关
# ----------------------------------------------------
def _is_control_msg(exchange: str, msg: dict) -> bool:
    # 识别 ping/pong 或 event 通知
    if exchange == "Bitget" and msg.get("op") in ("ping", "pong") or msg.get("event") in ("error", "subscribe"):
        return True
    if exchange == "Bybit" and msg.get("op") in ("ping", "pong", "subscribe", "error"):
        return True
    if exchange == "OKX" and (msg.get("op") in ("ping", "pong") or msg.get("event") in ("error", "subscribe")):
        return True
    if exchange == "Binance":
        # Binance 几乎无控制消息（忽略）
        return False
    return False

def _looks_like_ack(exchange: str, msg: dict) -> bool:
    # 简单判断 ack 消息（用于区分 ack 与 行情）
    if exchange == "OKX" and msg.get("event") in ("subscribe", "error"):
        return True
    if exchange == "Bybit" and msg.get("op") in ("subscribe", "error"):
        return True
    if exchange == "Bitget" and msg.get("event") in ("subscribe", "error"):
        return True
    return False

async def _send_heartbeat(exchange: str, ws: WebSocketClientProtocol, mode: str):
    if ws.closed:
        return
    try:
        if mode == "json":
            await ws.send(json.dumps({"op": "ping"}))
        elif mode == "native":
            await ws.ping()
        # else: no heartbeat
    except Exception:
        pass

def _handle_control(exchange: str, msg: dict, ws: WebSocketClientProtocol):
    # 控制消息简单处理：pong 忽略；错误打印
    if exchange in ("Bitget", "Bybit", "OKX"):
        if msg.get("op") == "ping":
            # 主动回 pong
            try:
                asyncio.create_task(ws.send(json.dumps({"op": "pong"})))
            except Exception:
                pass
        if msg.get("event") == "error" or msg.get("op") == "error":
            logger.error(f"[{exchange}] error ctrl msg: {msg}")


# ----------------------------------------------------
# 跳过队列，直接执行后续操作
# ----------------------------------------------------
async def _parse_and_apply(exchange: str, msg: dict, parser: Callable, state):
    """
    解析后直接落地（无队列）。这是最直接的方式。
    风险：高频下锁竞争 + IO 阻塞导致 ws.recv 处理变慢。
    """
    try:
        parsed = parser(msg)
    except Exception as e:
        METRICS["parser_errors"] += 1
        logger.debug(f"[{exchange}] parser exception: {e} raw={msg}")
        return

    if not parsed:
        return
    if isinstance(parsed, QuoteUpdate):
        parsed = [parsed]

    valid_items = 0
    # 加锁更新（逐条）
    async with state.lock:
        for q in parsed:
            if q.bid <= 0 or q.ask <= 0:
                continue
            sym = q.symbol
            if sym not in state.shared_data:
                state.shared_data[sym] = {}
            if exchange not in state.shared_data[sym]:
                state.shared_data[sym][exchange] = {"bid": 0.0, "ask": 0.0}
            
            # 若价格没变化则不处理
            if state.shared_data[sym][exchange]["bid"] == q.bid and state.shared_data[sym][exchange]["ask"] == q.ask:
                continue

            state.shared_data[sym][exchange]["bid"] = q.bid
            state.shared_data[sym][exchange]["ask"] = q.ask
            ts_exchange = q.ts_exchange / 1000
            try:
                realtime_data_manager.update_price(sym, exchange, q.bid, q.ask, ts_exchange, ts_exchange)
            except Exception:
                pass
            valid_items += 1

    METRICS["quotes_parsed"] += valid_items
    METRICS["enqueue_ok"] += valid_items  # 复用原统计字段语义：已成功处理的条数

# ----------------------------------------------------
# 解析并入队
# ----------------------------------------------------
async def _parse_and_enqueue(exchange: str, msg: dict, parser: Callable):
    try:
        parsed = parser(msg)
    except Exception as e:
        METRICS["parser_errors"] += 1
        logger.debug(f"[{exchange}] parser exception: {e} raw={msg}")
        return

    if not parsed:
        return

    if isinstance(parsed, QuoteUpdate):
        parsed = [parsed]

    valid_items = 0
    for q in parsed:
        if q.bid <= 0 or q.ask <= 0:
            continue
        try:
            PRICE_QUEUE.put_nowait((exchange, q))
            METRICS["enqueue_ok"] += 1
            valid_items += 1
        except asyncio.QueueFull:
            METRICS["enqueue_dropped"] += 1

    METRICS["quotes_parsed"] += valid_items

# ----------------------------------------------------
# 消费者：批量写入
# ----------------------------------------------------
async def price_consumer(state: TradingState, batch_size: int = 500, flush_interval: float = 0.01):
    """
    从队列批量拉取 (exchange, QuoteUpdate)，合并后写入：
      state.shared_data[symbol][exchange] = {bid, ask}
      realtime_data_manager.update_price(symbol, exchange, bid, ask)
    """
    buffer: List[tuple[str, QuoteUpdate]] = []
    last_flush = time.time()

    while True:
        try:
            item = await asyncio.wait_for(PRICE_QUEUE.get(), timeout=flush_interval)
            buffer.append(item)
            if len(buffer) >= batch_size:
                last_flush = time.time()
                await _flush_batch(state, buffer, last_flush)
                buffer.clear()
        except asyncio.TimeoutError:
            # 定期 flush
            if buffer:
                last_flush = time.time()
                await _flush_batch(state, buffer, last_flush)
                buffer.clear()

async def _flush_batch(state: TradingState, buf: List[tuple[str, QuoteUpdate]], ts_flush: float):
    # 聚合 -> 单锁写入
    METRICS["consumer_flush"] += 1
    updates = 0
    async with state.lock:
        for exchange, q in buf:
            sym = q.symbol
            if sym not in state.shared_data:
                # 动态创建（可按需限制）
                state.shared_data[sym] = {}
            if exchange not in state.shared_data[sym]:
                state.shared_data[sym][exchange] = {"bid": 0.0, "ask": 0.0}
            state.shared_data[sym][exchange]["bid"] = q.bid
            state.shared_data[sym][exchange]["ask"] = q.ask
            ts_exchange = q.ts_exchange
            if q.bid > 0 and q.ask > 0:
                # print(f"{exchange} {sym} {q.bid} {q.ask}")
                try:
                    realtime_data_manager.update_price(sym, exchange, q.bid, q.ask, ts_flush, ts_exchange)
                except Exception:
                    pass
                updates += 1
    METRICS["consumer_updates"] += updates

# ----------------------------------------------------
# 监控输出
# ----------------------------------------------------
async def metrics_reporter(interval: int = 10):
    while True:
        await asyncio.sleep(interval)
        logger.info(
            "[STATS] connected=%d reconnects=%d sub_ok=%d sub_fail=%d frames=%d quotes=%d "
            "enq_ok=%d enq_drop=%d flush=%d upd=%d parser_err=%d inactivity=%d",
            METRICS["ws_connected"],
            METRICS["ws_reconnects"],
            METRICS["ws_subscribe_ok"],
            METRICS["ws_subscribe_fail"],
            METRICS["msg_frames"],
            METRICS["quotes_parsed"],
            METRICS["enqueue_ok"],
            METRICS["enqueue_dropped"],
            METRICS["consumer_flush"],
            METRICS["consumer_updates"],
            METRICS["parser_errors"],
            METRICS["inactivity_reconnects"]
        )

# ----------------------------------------------------
# 主运行
# ----------------------------------------------------
async def start_data_service(
    exchange_symbols_map: Dict[str, List[str]],
    state: Optional[TradingState] = None
):
    """
    exchange_symbols_map: {"Binance": ["BTCUSDT",...], "OKX":[...], ...}
    """
    if state is None:
        state = TradingState()
        state.init_symbols()

    # 用传入的 symbols 覆盖 state
    all_symbols = set()
    for ex, syms in exchange_symbols_map.items():
        for s in syms:
            all_symbols.add(s.upper())

    # 初始化 shared_data 结构（懒加载也可）
    for s in all_symbols:
        if s not in state.shared_data:
            state.shared_data[s] = {}
        for ex in exchange_symbols_map.keys():
            if ex not in state.shared_data[s]:
                state.shared_data[s][ex] = {"bid": 0.0, "ask": 0.0}

    # 启动各交易所连接任务
    producer_tasks = []
    for ex, syms in exchange_symbols_map.items():
        if ex not in EXCHANGES:
            logger.warning(f"Exchange '{ex}' not supported, skip.")
            continue
        if not syms:
            continue
        producer_tasks.append(asyncio.create_task(ws_connector(ex, syms, state), name=f"ws-{ex}"))

    consumer_task = asyncio.create_task(price_consumer(state), name="price-consumer")
    # metrics_task = asyncio.create_task(metrics_reporter(), name="metrics")

    # 等待所有生产者（理论上不结束）
    # await asyncio.gather(*producer_tasks, consumer_task, metrics_task)
    await asyncio.gather(*producer_tasks, consumer_task)

# ----------------------------------------------------
# CLI / main
# ----------------------------------------------------
def _build_default_symbol_map() -> Dict[str, List[str]]:
    # 根据需要修改
    return {
        "Binance": ["OPUSDT","LDOUSDT","NEARUSDT","CRVUSDT","ZETAUSDT","LRCUSDT","ARBUSDT","LINKUSDT","BCHUSDT","ALGOUSDT","XLMUSDT","CROUSDT","ETHWUSDT","ETCUSDT","USDCUSDT","THETAUSDT","CHZUSDT","AXSUSDT","SANDUSDT","ADAUSDT","FLOWUSDT","AI16ZUSDT","INJUSDT","SUSHIUSDT","1INCHUSDT","MAJORUSDT","CFXUSDT","ZILUSDT","GMTUSDT","HMSTRUSDT","ATOMUSDT","DOGSUSDT","ETHFIUSDT","KSMUSDT","AVAXUSDT","BOMEUSDT","MINAUSDT","AAVEUSDT","IOTAUSDT","NEOUSDT","ACTUSDT","TONUSDT","TRXUSDT","CELOUSDT","YGGUSDT","COMPUSDT","NOTUSDT","HBARUSDT","SHELLUSDT","DOGEUSDT","CETUSUSDT","QTUMUSDT","BANDUSDT","ICPUSDT","CATIUSDT","IMXUSDT","SUIUSDT","LTCUSDT","BRETTUSDT","BNTUSDT","SPKUSDT","FILUSDT","HUMAUSDT","LQTYUSDT","JUPUSDT","PARTIUSDT","AUCTIONUSDT","MAGICUSDT","DOTUSDT"], # "BTCUSDT","ETHUSDT","SOLUSDT","BNBUSDT","XRPUSDT", # ,"KAITOUSDT","MASKUSDT","SAHARAUSDT","LPTUSDT","XTZUSDT"
        "Bybit": ["OPUSDT","LDOUSDT","NEARUSDT","CRVUSDT","ZETAUSDT","LRCUSDT","ARBUSDT","LINKUSDT","BCHUSDT","ALGOUSDT","XLMUSDT","CROUSDT","ETHWUSDT","ETCUSDT","USDCUSDT","THETAUSDT","CHZUSDT","AXSUSDT","SANDUSDT","ADAUSDT","FLOWUSDT","AI16ZUSDT","INJUSDT","SUSHIUSDT","1INCHUSDT","MAJORUSDT","CFXUSDT","ZILUSDT","GMTUSDT","HMSTRUSDT","ATOMUSDT","DOGSUSDT","ETHFIUSDT","KSMUSDT","AVAXUSDT","BOMEUSDT","MINAUSDT","AAVEUSDT","IOTAUSDT","NEOUSDT","ACTUSDT","TONUSDT","TRXUSDT","CELOUSDT","YGGUSDT","COMPUSDT","NOTUSDT","HBARUSDT","SHELLUSDT","DOGEUSDT","CETUSUSDT","QTUMUSDT","BANDUSDT","ICPUSDT","CATIUSDT","IMXUSDT","SUIUSDT","LTCUSDT","BRETTUSDT","BNTUSDT","SPKUSDT","FILUSDT","HUMAUSDT","LQTYUSDT","JUPUSDT","PARTIUSDT","AUCTIONUSDT","MAGICUSDT","DOTUSDT"], # "BTCUSDT","ETHUSDT","SOLUSDT","BNBUSDT","XRPUSDT", # ,"KAITOUSDT","MASKUSDT","SAHARAUSDT","LPTUSDT","XTZUSDT"
        "OKX": ["OPUSDT","LDOUSDT","NEARUSDT","CRVUSDT","ZETAUSDT","LRCUSDT","ARBUSDT","LINKUSDT","BCHUSDT","ALGOUSDT","XLMUSDT","CROUSDT","ETHWUSDT","ETCUSDT","USDCUSDT","THETAUSDT","CHZUSDT","AXSUSDT","SANDUSDT","ADAUSDT","FLOWUSDT","AI16ZUSDT","INJUSDT","SUSHIUSDT","1INCHUSDT","MAJORUSDT","CFXUSDT","ZILUSDT","GMTUSDT","HMSTRUSDT","ATOMUSDT","DOGSUSDT","ETHFIUSDT","KSMUSDT","AVAXUSDT","BOMEUSDT","MINAUSDT","AAVEUSDT","IOTAUSDT","NEOUSDT","ACTUSDT","TONUSDT","TRXUSDT","CELOUSDT","YGGUSDT","COMPUSDT","NOTUSDT","HBARUSDT","SHELLUSDT","DOGEUSDT","CETUSUSDT","QTUMUSDT","BANDUSDT","ICPUSDT","CATIUSDT","IMXUSDT","SUIUSDT","LTCUSDT","BRETTUSDT","BNTUSDT","SPKUSDT","FILUSDT","HUMAUSDT","LQTYUSDT","JUPUSDT","PARTIUSDT","AUCTIONUSDT","MAGICUSDT","DOTUSDT"], # "BTCUSDT","ETHUSDT","SOLUSDT","BNBUSDT","XRPUSDT", # ,"KAITOUSDT","MASKUSDT","SAHARAUSDT","LPTUSDT","XTZUSDT"
        "Bitget": ["OPUSDT","LDOUSDT","NEARUSDT","CRVUSDT","ZETAUSDT","LRCUSDT","ARBUSDT","LINKUSDT","BCHUSDT","ALGOUSDT","XLMUSDT","CROUSDT","ETHWUSDT","ETCUSDT","USDCUSDT","THETAUSDT","CHZUSDT","AXSUSDT","SANDUSDT","ADAUSDT","FLOWUSDT","AI16ZUSDT","INJUSDT","SUSHIUSDT","1INCHUSDT","MAJORUSDT","CFXUSDT","ZILUSDT","GMTUSDT","HMSTRUSDT","ATOMUSDT","DOGSUSDT","ETHFIUSDT","KSMUSDT","AVAXUSDT","BOMEUSDT","MINAUSDT","AAVEUSDT","IOTAUSDT","NEOUSDT","ACTUSDT","TONUSDT","TRXUSDT","CELOUSDT","YGGUSDT","COMPUSDT","NOTUSDT","HBARUSDT","SHELLUSDT","DOGEUSDT","CETUSUSDT","QTUMUSDT","BANDUSDT","ICPUSDT","CATIUSDT","IMXUSDT","SUIUSDT","LTCUSDT","BRETTUSDT","BNTUSDT","SPKUSDT","FILUSDT","HUMAUSDT","LQTYUSDT","JUPUSDT","PARTIUSDT","AUCTIONUSDT","MAGICUSDT","DOTUSDT"], # "BTCUSDT","ETHUSDT","SOLUSDT","BNBUSDT","XRPUSDT", # ,"KAITOUSDT","MASKUSDT","SAHARAUSDT","LPTUSDT","XTZUSDT"
    }

async def _shutdown(loop, tasks: List[asyncio.Task]):
    for t in tasks:
        t.cancel()
    await asyncio.gather(*tasks, return_exceptions=True)
    loop.stop()

def main():
    exchange_symbols_map = _build_default_symbol_map()
    state = TradingState()
    state.init_symbols()

    loop = asyncio.get_event_loop()
    main_task = loop.create_task(start_data_service(exchange_symbols_map, state))

    # 优雅退出
    def _handle_sig():
        logger.info("Received termination signal, shutting down...")
        asyncio.create_task(_shutdown(loop, [main_task]))

    for sig in (signal.SIGINT, signal.SIGTERM):
        try:
            loop.add_signal_handler(sig, _handle_sig)
        except NotImplementedError:
            pass

    try:
        loop.run_until_complete(main_task)
    except KeyboardInterrupt:
        logger.info("Interrupted by user.")
    finally:
        pending = asyncio.all_tasks(loop)
        for p in pending:
            p.cancel()
        try:
            loop.run_until_complete(asyncio.gather(*pending, return_exceptions=True))
        except Exception:
            pass
        loop.close()

if __name__ == "__main__":
    main()